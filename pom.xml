<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.1.2</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.mstack</groupId>
	<artifactId>keystone</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>keystone</name>
	<description>Keystone service to handle orders and emails</description>
	<properties>
		<java.version>17</java.version>
		<plugin.prettier.goal>write</plugin.prettier.goal>
	</properties>
	<repositories>
		<repository>
			<id>mvn</id>
			<url>https://mvnrepository.com</url>
		</repository>
	</repositories>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.1</version> <!-- You can use the appropriate version for your needs -->
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-engine-core</artifactId>
			<version>2.3</version> <!-- Replace with the latest version available -->
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>5.5</version> <!-- Use the latest version available -->
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.32</version> <!-- Replace with the latest version available -->
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version> <!-- Specify the desired version -->
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-ses</artifactId>
			<version>1.11.1000</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/redis.clients/jedis -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>4.4.3</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
			<version>3.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.17.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.xhtmlrenderer/flying-saucer-pdf-itext5 -->
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf-itext5</artifactId>
			<version>9.3.1</version>
		</dependency>
		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-core</artifactId>
			<version>1.0.10</version>
		</dependency>
		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-pdfbox</artifactId>
			<version>1.0.10</version>
		</dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
		<dependency>
			<groupId>org.eclipse.angus</groupId>
			<artifactId>jakarta.mail</artifactId>
			<version>2.0.2</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.springframework.security/spring-security-crypto -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
			<version>6.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>3.0.2</version>
		</dependency>


	</dependencies>

	<build>
		<plugins>
<!--			<plugin>-->
<!--				<groupId>com.hubspot.maven.plugins</groupId>-->
<!--				<artifactId>prettier-maven-plugin</artifactId>-->
<!--				<version>0.20</version>-->
<!--				<configuration>-->
<!--					<prettierJavaVersion>2.0.0</prettierJavaVersion>-->
<!--					<printWidth>120</printWidth>-->
<!--					<tabWidth>4</tabWidth>-->
<!--					<useTabs>false</useTabs>-->
<!--					<ignoreConfigFile>true</ignoreConfigFile>-->
<!--					<ignoreEditorConfig>true</ignoreEditorConfig>-->
<!--					&lt;!&ndash; Use <inputGlobs> to override the default input patterns &ndash;&gt;-->
<!--					<inputGlobs>-->
<!--						&lt;!&ndash; These are the default patterns, you can omit <inputGlobs> entirely unless you want to override them &ndash;&gt;-->
<!--						<inputGlob>src/main/java/**/*.java</inputGlob>-->
<!--						<inputGlob>src/test/java/**/*.java</inputGlob>-->
<!--					</inputGlobs>-->
<!--				</configuration>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<phase>validate</phase>-->
<!--						<goals>-->
<!--							<goal>${plugin.prettier.goal}</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>travis</id>
			<activation>
				<property>
					<name>env.GITHUB_ACTIONS</name>
					<value>true</value>
				</property>
			</activation>
			<properties>
				<!-- But in our CI environment we want to validate that code is formatted -->
				<plugin.prettier.goal>check</plugin.prettier.goal>
			</properties>
		</profile>
	</profiles>

</project>
