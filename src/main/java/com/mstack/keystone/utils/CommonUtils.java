package com.mstack.keystone.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mstack.keystone.client.HttpClient;
import com.mstack.keystone.config.UrlConfig;
import com.mstack.keystone.model.dto.CreateUserRequest;
import com.mstack.keystone.model.dto.EmailInfo;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.model.repository.email.Email;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.service.email.interfaces.IEmailCreator;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import static com.mstack.keystone.constants.AppConstants.*;

@Component
public class CommonUtils {

    @Autowired
    HttpClient httpClient;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    IEmailCreator emailCreator;

    @Value("${ENV}")
    String env;

    @Autowired
    UrlConfig urls;

    @SneakyThrows
    public void createUser(String email, String entityType, String name, String entityId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("username", email);
        map.put("password", email.split("@")[0] + "@123");
        map.put("entityType", entityType);
        map.put("permissions", new ArrayList<>());
        map.put("permissionsGroups", new ArrayList<>());
        map.put("entityId", entityId);

        httpClient.makeHttpPostRequest(
            urls.getGatewayUrl() + "/auth/register",
            objectMapper.writeValueAsString(map),
            new HttpHeaders()
        );
        map.put("name", name);
        if (env.equals("prod")) {
            //            sendEmail(email, map, "CustomerOnboardingEmail");
        }
    }

    public void createUserV2(CreateUserRequest createUserRequest) throws JsonProcessingException {
        httpClient.makeHttpPostRequest(
                urls.getGatewayUrl() + "/auth/register",
                objectMapper.writeValueAsString(createUserRequest),
                new HttpHeaders()
        );
    }


    private void sendEmail(String emailId, HashMap<String, Object> context, String templateName) {
        Email email = new Email();
        email.setEmailType(templateName);
        email.setTo(emailId);
        email.setTemplateInfo(context);
        emailCreator.createEmail(email);
    }

   public HashMap<String, List<Activity>> getCategoryWiseActivities(List<Activity> activities){
        HashMap<String,List<Activity>> map=new HashMap<String,List<Activity>>();
        for (Activity activity:activities) {
            if(!map.containsKey(activity.getCategory())){
                map.put(activity.getCategory(),new ArrayList<>());
            }
            List<Activity> value=map.get(activity.getCategory());
            value.add(activity);
        }
        return  map;
    }

    public String CreateID(String prefix, long count) {
        String integerStr = String.format("%05d", (count + 1));
        String formattedString = prefix + integerStr;
        return formattedString;
    }

    public double getTotalOrderValue(CustomerOrder customerOrder){
        double sum=0.0;
        for (OrderedProduct product: customerOrder.getProducts()) {
            sum+= (product.getPrice()*product.getQuantity());
        }
        return sum;
    }

    public double roundOff(double val) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        decimalFormat.setRoundingMode(RoundingMode.HALF_EVEN);
        return Double.parseDouble(decimalFormat.format(val));
    }

    public String printDouble(double val) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        decimalFormat.setRoundingMode(RoundingMode.HALF_EVEN);
        return decimalFormat.format(val);
    }

    public String formatDate(Date date){

//        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd yyyy");
        // cast all dates to ist timezone by default
        dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));
        if(date==null||date.equals("")){
            return "";
        }
        String formattedDate = dateFormat.format(date);
        return formattedDate;
    }

    public String formatDate(Date date, SimpleDateFormat dateFormat) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        if (date == null || date.equals("")) {
            return "";
        }
        // cast all dates to ist timezone by default
        dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));
        String formattedDate = dateFormat.format(date);
        return formattedDate;
    }

    public String formatDateFromString(String date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
        if(date==null||date.equals("")){
            return "";
        }
        try {
            // Parse the string into a Date object
            Date dateObject = sdf.parse(date);
//            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
            SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd yyyy");
            dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));
            String formattedDate = dateFormat.format(dateObject);
            return formattedDate;
        } catch (ParseException e) {
            e.printStackTrace(); // Handle the parsing exception
        }
        return null;
    }
    public String getCurrentFinancialYear() {
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH);
        int startingYear = (currentMonth >= Calendar.APRIL) ? currentYear : currentYear - 1;
        int lastTwoDigitsStartingYear = startingYear % 100;
        int lastTwoDigitsEndingYear = (startingYear + 1) % 100;
        String financialYear = String.format("%02d-%02d", lastTwoDigitsStartingYear, lastTwoDigitsEndingYear);
        return financialYear;
    }
    public Date addDaysToDate(Date date,int days){
        if(date==null)
            return null;
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE,days);
        Date newDate = calendar.getTime();
        return newDate;
    }

    public String getPaymentTermsString(PaymentTerms paymentTerms) {
        if (paymentTerms == null) return null;
        String output = "T/T ";
        output += (paymentTerms.getCreditorDays() + " days at ");
        output += (paymentTerms.getStartDate());
        return output;
    }

    public void updateUser(User user) throws JsonProcessingException {
        // todo fix this
        httpClient.makeHttpPutRequest(
                urls.getGatewayUrl() + "/auth/register",
                objectMapper.writeValueAsString(user),
                new HttpHeaders()
        );
    }

    public UnitOfMeasure getUOM(String s) {
        return switch (s) {
            case "KG", "KILOGRAM" -> UnitOfMeasure.KILOGRAM;
            case "MT" -> UnitOfMeasure.METRIC_TON;
            case "L" -> UnitOfMeasure.LITRE;
            case "KL" -> UnitOfMeasure.KILOLITRE;
            case "GAL" -> UnitOfMeasure.GALLON;
            case "LB" -> UnitOfMeasure.POUND;
            default -> null;
        };
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "LB";
            case GALLON -> "GAL";
            case LITRE -> "L";
            case KILOLITRE -> "KL";
            case KILOGRAM -> "KG";
            default -> "";
        };
    }

    public double getConvertedWeight(double quantity, UnitOfMeasure givenUnit, UnitOfMeasure conversionUnit) {
        double quantityInKilogram = 0.0;
        switch (givenUnit) {
            case METRIC_TON:
                quantityInKilogram = quantity * METRIC_TON_TO_KILOGRAM;
                break;
            case POUND:
                quantityInKilogram = quantity * POUND_TO_KILOGRAM;
                break;
            case GALLON:
                quantityInKilogram = quantity * GALLON_TO_LITRE * LITRE_TO_KILOGRAM;
                break;
            case LITRE:
                quantityInKilogram = quantity * LITRE_TO_KILOGRAM;
                break;
            case KILOLITRE:
                quantityInKilogram = quantity * KILOLITRE_TO_LITRE * LITRE_TO_KILOGRAM;
                break;
            case KILOGRAM:
                quantityInKilogram = quantity;
                break;
            default:
                throw new IllegalArgumentException("Unsupported Unit of Measure: " + givenUnit);
        }
        switch (conversionUnit) {
            case METRIC_TON:
                return quantityInKilogram / METRIC_TON_TO_KILOGRAM;
            case POUND:
                return quantityInKilogram / POUND_TO_KILOGRAM;
            case GALLON:
                return (quantityInKilogram / LITRE_TO_KILOGRAM) / GALLON_TO_LITRE;
            case LITRE:
                return quantityInKilogram / LITRE_TO_KILOGRAM;
            case KILOLITRE:
                return (quantityInKilogram / LITRE_TO_KILOGRAM) / KILOLITRE_TO_LITRE;
            case KILOGRAM:
                return quantityInKilogram;
            default:
                throw new IllegalArgumentException("Unsupported Unit of Measure: " + conversionUnit);
        }
    }

    public boolean isMoreThanOneMinute(Date lastUpdate) {
        Date currentDate = new Date();
        long diffInMillies = Math.abs(currentDate.getTime() - lastUpdate.getTime());
        long diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillies);
        return diffInMinutes > 1;
    }

    public boolean isMoreThan24Hours(Date lastUpdate) {
        Date currentDate = new Date();
        long diffInMillies = Math.abs(currentDate.getTime() - lastUpdate.getTime());
        long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillies);
        return diffInHours > 24;
    }

    public LocalDate convertToLocalDateViaInstant(Date dateToConvert) {
        return dateToConvert.toInstant()
                .atZone(ZoneId.of(INDIA_ZONE_ID))
                .toLocalDate();
    }

    public boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }

    /**
     * Combines all email IDs from EmailInfo and an existing list, removing duplicates.
     * 
     * @param emailInfo The EmailInfo object containing sales, account, and optional emails
     * @param existingEmailIds Existing list of email IDs (can be null or empty)
     * @return List of all unique email IDs
     */
    public List<String> combineEmailIds(EmailInfo emailInfo, List<String> existingEmailIds) {
        // Create a set to automatically handle duplicates
        Set<String> uniqueEmails = new HashSet<>();
        
        // Add existing emails if provided
        if (existingEmailIds != null) {
            uniqueEmails.addAll(existingEmailIds);
        }
        
        // Add emails from EmailInfo if provided
        if (emailInfo != null) {
            // Add sales email if not null or empty
            if (!isNullOrEmpty(emailInfo.getSalesEmail())) {
                uniqueEmails.add(emailInfo.getSalesEmail());
            }
            
            // Add account email if not null or empty
            if (!isNullOrEmpty(emailInfo.getAccountEmail())) {
                uniqueEmails.add(emailInfo.getAccountEmail());
            }
            
            // Add optional emails if not null
            if (emailInfo.getOptionalEmails() != null) {
                uniqueEmails.addAll(emailInfo.getOptionalEmails().stream()
                    .filter(email -> !isNullOrEmpty(email))
                    .collect(Collectors.toList()));
            }
        }
        
        // Convert back to list and return
        return new ArrayList<>(uniqueEmails);
    }

}
