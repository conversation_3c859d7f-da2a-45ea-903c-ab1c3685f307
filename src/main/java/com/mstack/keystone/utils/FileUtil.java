package com.mstack.keystone.utils;

import com.mstack.keystone.exception.ServiceException;
import lombok.SneakyThrows;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;



@Component
public class FileUtil {
    private Path createTempFolder() {
        try {
            Path folderPath = Path.of("./tempFiles");
            if (Files.exists(folderPath)) {
                // delete it to remove any prexisting files
                Files.deleteIfExists(folderPath);
            }
            return Files.createDirectory(folderPath);
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
            return null;
        }
    }

    private void createFolder(Path folderPath) {
        try {
            Files.createDirectories(folderPath);
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
        }
    }

    private void deleteFolder(Path folderPath) {
        try {
            Files.walk(folderPath)
                    .sorted((p1, p2) -> -p1.compareTo(p2)) // Reverse order for deleting files first
                    .map(Path::toFile)
                    .forEach(File::delete);
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
        }
    }

    private void deleteFiles(List<File> files) {
        for (File file : files) {
            if (file.exists()) {
                if (file.delete()) {
                    System.out.println("Deleted file: " + file.getAbsolutePath());
                } else {
                    System.err.println("Failed to delete file: " + file.getAbsolutePath());
                }
            } else {
                System.err.println("File not found: " + file.getAbsolutePath());
            }
        }
    }

    private byte[] createZipArchive(Path sourceFolderPath) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {

            Files.walk(sourceFolderPath)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            ZipEntry zipEntry = new ZipEntry(sourceFolderPath.relativize(path).toString());
                            zipOutputStream.putNextEntry(zipEntry);
                            Files.copy(path, zipOutputStream);
                            zipOutputStream.closeEntry();
                        } catch (IOException e) {
                            e.printStackTrace(); // Handle the exception appropriately
                        }
                    });

            zipOutputStream.finish();
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
            return null;
        }
    }

    public byte[] getAllFilesAsZip(HashMap<String, List<File>> filesMap) {
        Path tempFolderPath = createTempFolder();
        filesMap.forEach((category, files) -> {
            Path categoryFolder = tempFolderPath.resolve(category);
            createFolder(categoryFolder);
            files.forEach(file -> {
                try {
                    Files.copy(file.toPath(), categoryFolder.resolve(file.getName()));
                } catch (IOException e) {
                    e.printStackTrace(); // Handle the exception appropriately
                }
            });
        });

        // Create a ZIP archive from the contents of the temporary folder
        byte[] zipBytes = createZipArchive(tempFolderPath);
        // Cleanup: Delete temporary folder
        deleteFolder(tempFolderPath);
        // Cleanup : Delete files
        filesMap.forEach((category, files) -> {
            deleteFiles(files);
        });
        return zipBytes;

    }

    public MultipartFile convertBytesToMultipart(byte[] data, String fileName) {
        return new MockMultipartFile(fileName, fileName, "application/zip", data);
    }

    public MultipartFile convertFileToMultipart(File file, String fileName) throws IOException {
        return convertBytesToMultipart(Files.readAllBytes(file.toPath()), fileName);
    }

@SneakyThrows
public PDRectangle getInputPageSize(File file) {

        PDRectangle inputPageSize;
        try (PDDocument inputDocument = Loader.loadPDF(file)) {
            PDFRenderer pdfRenderer = new PDFRenderer(inputDocument);
            if (inputDocument.getNumberOfPages() > 0) {
                // Calculate page size using the first page
                BufferedImage firstPageImage = pdfRenderer.renderImageWithDPI(0, 72); // Render at 72 DPI
                inputPageSize = new PDRectangle(firstPageImage.getWidth(), firstPageImage.getHeight());
            } else {
                throw new ServiceException("Input PDF has no pages", HttpStatus.BAD_REQUEST.value());
            }
        } catch (IOException e) {
            throw new ServiceException(
                    "Failed to process PDF document",
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    e
            );
        }

        return inputPageSize;
    }


    public static PDImageXObject createImageXObject(PDDocument document, BufferedImage image) throws IOException {
        // Convert BufferedImage to ByteArrayOutputStream for compatibility
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            // Encode image data to PNG format (compatible with PDFBox)
            ImageIO.write(image, "png", byteArrayOutputStream);
            byte[] imageBytes = byteArrayOutputStream.toByteArray();

            // Create PDImageXObject from byte array
            return PDImageXObject.createFromByteArray(document, imageBytes, "Embedded Image");
        }
    }



    @SneakyThrows
    public java.io.File convertPngToPdf(java.io.File downloadedPngFile, PDRectangle inputPageSize) throws ServiceException {

        try (PDDocument pdfDocument = new PDDocument()) {
            // Read the image file
            BufferedImage bufferedImage = ImageIO.read(downloadedPngFile);

            // Create a blank PDF page with dimensions matching the input page size
            PDPage pdfPage = new PDPage(inputPageSize);
            pdfDocument.addPage(pdfPage);

            // Embed the image into the page and scale it to fit
            PDImageXObject imageXObject = createImageXObject(pdfDocument, bufferedImage);
            try (PDPageContentStream contentStream = new PDPageContentStream(pdfDocument, pdfPage)) {
                float imageWidth = inputPageSize.getWidth();
                float imageHeight = inputPageSize.getHeight();

                // Draw the image to cover the entire page
                contentStream.drawImage(imageXObject, 0, 0, imageWidth, imageHeight);
            }

            // Save the PDF as a temporary file
            java.io.File tempPdfFile = Files.createTempFile("converted", ".pdf").toFile();
            pdfDocument.save(tempPdfFile);
            downloadedPngFile.delete(); // Delete the original image file
            return tempPdfFile;
        } catch (Exception e) {
            throw new ServiceException("Failed to convert image to PDF: " + e.getMessage(),
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }


}
