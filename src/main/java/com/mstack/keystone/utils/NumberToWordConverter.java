package com.mstack.keystone.utils;

public class NumberToWordConverter {

    private static final String[] units = {
            "", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"
    };

    private static final String[] teens = {
            "", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"
    };

    private static final String[] tens = {
            "", "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"
    };

    private static final String[] powersOfTen = {
            "", "Thousand", "Million", "Billion", "Trillion" // Extend as needed
    };

    public static String convertToWord(double number) {
        if (number == 0) {
            return "Zero";
        }

        long wholePart = (long) number;
        long temp = Math.round(number * 100);
        int decimalPart = (int) ((temp) - (wholePart * 100));
//        int decimalPart = (int) ((number - wholePart) * 100); // Assuming two decimal places

        String wordRepresentation = convertToWord(wholePart);
        if(wordRepresentation.equals("Thousand")){
            wordRepresentation="One Thousand";
        }
        if (decimalPart > 0) {
            wordRepresentation += " Point " + convertDecimalToWord(decimalPart);
        }

        return wordRepresentation;
    }

    private static String convertToWord(long number) {
        if (number == 0) {
            return "";
        } else if (number < 0) {
            return "Negative " + convertToWord(-number);
        }

        int powerOfTenIndex = 0;
        String result = "";

        do {
            long currentPart = number % 1000;
            if (currentPart != 0) {
                result = convertToWordPart(currentPart) + " " + powersOfTen[powerOfTenIndex] + " " + result;
            }
            number /= 1000;
            powerOfTenIndex++;
        } while (number > 0);

        return result.trim();
    }

    private static String convertDecimalToWord(int no) {
        String decimalPart = "";
        while (no > 0) {
            int lastDigit = no % 10;
            decimalPart = lastDigit == 0 ? " Zero " : units[lastDigit] + " " + decimalPart;
            no = no / 10;
        }
        return decimalPart;
    }

    private static String convertToWordPart(long number) {
        if (number < 10) {
            return units[(int) number];
        } else if (number < 20) {
            return teens[(int) (number - 10)];
        } else if (number < 100) {
            return tens[(int) (number / 10)] + " " + units[(int) (number % 10)];
        } else {
            return units[(int) (number / 100)] + " Hundred " + convertToWordPart(number % 100);
        }
    }
}
