package com.mstack.keystone.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
public class StringUtils {

    @Autowired
    ObjectMapper objectMapper;

    public static Map<String, String> stringToHash(String jsonString) {
        // Use Gson to convert the JSON-like string to a HashMap
        Type type = HashMap.class;
        Gson gson = new Gson();
        return gson.fromJson(jsonString, type);
    }

    @SneakyThrows
    public String readFileAsString(String filename) {
        Resource resource = new ClassPathResource(filename);
        byte[] htmlBytes = Files.readAllBytes(resource.getFile().toPath());
        return new String(htmlBytes);
    }

    @SneakyThrows
    public ArrayList<String> getPermissionsFromJsonFile() {
        String jsonString = readFileAsString("./permissions.json");
        HashMap<String, Object> map = objectMapper.readValue(jsonString, HashMap.class);
        return (ArrayList<String>) map.getOrDefault("permissions", new ArrayList<>());
    }
}
