package com.mstack.keystone.utils;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.Event;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.enums.BillOfLading;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.enums.OrderStatus;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.ActivityRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.docEngine.DocMetaRepository;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.SupplierOrderRepository;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Component
@Slf4j
public class EventManager {

    @Autowired
    CustomerOrderRepository customerOrderRepository;

    @Autowired
    SupplierOrderRepository supplierOrderRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    ActivityRepository activityRepository;

    @Autowired
    DocMetaRepository docMetaRepository;

    @Autowired
    RequestConfig requestConfig;

    List<OrderStatus> statuses = new ArrayList() {
        {
            add(OrderStatus.UNDER_PRODUCTION);
            add(OrderStatus.PRODUCTION_COMPLETED);
            add(OrderStatus.MANUFACTURED_AND_TESTED);
            add(OrderStatus.UNDER_TESTING);
        }
    };

    public void executeEvent(Event event, Activity activity) {
        if (!checkTrigger(event, activity)) {
            return;
        }
        switch (event.getName()) {
            case "UPDATE_CUSTOMER_ORDER_STATUS":
                updateCustomerStatus(event, activity);
                break;
            case "UPDATE_SUPPLIER_ORDER_STATUS":
                updateSupplierStatus(event, activity);
                break;
            case "UPDATE_TYPE_OF_BL":
                updateTypeOfBlInDispatchOrder( activity);
                break;
            case "UPDATE_MARKS_AND_CONTAINERS":
                updateKeyInDispatchOrder(activity,"MARKS_AND_CONTAINERS","marksAndContainers");
                break;
            case "UPDATE_COUNTRY_OF_ORIGIN":
                updateKeyInDispatchOrder(activity, "COUNTRY_OF_ORIGIN", "countryOfOrigin");
                break;
            case "UPDATE_SEA_BOOKING_INFORMATION":
                updateKeyInDispatchOrder(activity,"FREIGHT_COST","freightCost");
                updateKeyInDispatchOrder(activity,"INSURANCE_COST","insuranceCost");
                break;
            case "UPDATE_BL_NUMBER":
                updateKeyInDispatchOrder(activity,"BL_NUMBER","bLNumber");
                break;
            case "UPDATE_VESSEL_INFORMATION":
                updateKeyInDispatchOrder(activity,"VESSEL_NUMBER","vesselNumber");
                updateKeyInDispatchOrder(activity,"VOYAGE_NAME","voyageName");
                break;

            case "UPDATE_TRACKING_INFORMATION":
                updateKeyInDispatchOrder(activity,"TRACKING_URL","trackingUrl");
                updateKeyInDispatchOrder(activity,"TRACKING_NUMBER","trackingNumber");
                break;
            case "UPDATE_SHIPMENT_DATE":
                updateValueInDispatchOrder(activity,activity.getDueDate(),"shipmentDate");
                break;
            case "UPDATE_DELIVERY_DATE":
                updateValueInDispatchOrder(activity,activity.getDueDate(),"deliveryDate");
                break;
            case "UPDATE_INVOICE_DATE":
                updateKeyInDispatchOrder(activity,"INVOICE_DATE","invoiceDate");
                break;
            case "UPDATE_CHEMSTACK_INVOICE_DATE":
                updateKeyInDispatchOrder(activity, "CHEMSTACK_INVOICE_DATE", "chemstackInvoiceDate");
                break;
            case "UPDATE_MSTACK_INVOICE_NUMBER":
                log.info("disabled direct update for mstack invoice number ");
//                updateKeyInDispatchOrder(activity, "MSTACK_INVOICE_NUMBER", "mstackInvoiceNumber");
                break;
            case "UPDATE_CHEMSTACK_INVOICE_NUMBER":
                log.info("disabled direct update for chemmstack invoice number ");
//                updateKeyInDispatchOrder(activity, "CHEMSTACK_INVOICE_NUMBER", "chemstackInvoiceNumber");
                break;
            case "UPDATE_DOLLAR_RATE":
                updateKeyInDispatchOrder(activity,"DOLLAR_RATE","customExchangeRate");
                break;
            case "UPDATE_FILE":
                updateFile(activity);
                break;
            case "UPDATE_CUSTOMER_PRODUCT_DETAILS":
                updateCustomerProductDetails(activity,"BATCH_DATA","batchData");
                updateCustomerProductDetails(activity, "STATE_OF_ORIGIN", "stateOfOrigin");
                updateCustomerProductDetails(activity, "DISTRICT_OF_ORIGIN", "districtOfOrigin");
                updateCustomerProductDetails(activity, "HAZ_DETAILS", "hazDetails");
                break;
            case "UPDATE_CUSTOM_EXCHANGE_RATE":
                updateKeyInDispatchOrder(activity,"CUSTOM_EXCHANGE_RATE","customExchangeRate");
                break;
            case "UPDATE_TRADE_AGREEMENT_CODE":
                updateKeyInDispatchOrder(activity,"TRADE_AGREEMENT_CODE","tradeAgreementCode");
                updateKeyInDispatchOrder(activity,"BL_DATE","blDate");
                break;
            case "UPDATE_TYPE_OF_SHIPMENT":
                updateKeyInDispatchOrder(activity,"TYPE_OF_SHIPMENT","typeOfShipment");
                break;
            case "UPDATE_NUMBER_OF_PALLETS":
                updateKeyInDispatchOrder(activity, "NUMBER_OF_PALLETS", "numberOfPallets");
                break;
            case "UPDATE_PALLET_WT":
                updateKeyInDispatchOrder(activity, "PALLET_WT", "palletWt");
                break;
            case "UPDATE_MTOCPAYTERMS":
                updatePaymentTerms(activity);
                break;
        }
    }

    @SneakyThrows
    private void updateCustomerProductDetails(Activity activity,String key,String targetKey){
        //update Product details
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, activity.getEntityId());
        Object value=getValueFromConfig(activity.getTaskConfig(),key);
        if(value==null){
            return;
        }
        String id=activity.getAdditionalData().get("id").toString();
        OrderedProduct orderedProduct=null;
        for (OrderedProduct product :customerOrder.getProducts()) {
            if(product.getId().equals(id)){
                orderedProduct=product;
                break;
            }
        }
        if(orderedProduct==null){
            throw new ServiceException("Something is wrong",500);
        }
        Field field = orderedProduct.getClass().getDeclaredField(targetKey);
        field.setAccessible(true);
        field.set(orderedProduct,value);
        mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
    }

    private void updatePaymentTerms(Activity activity){
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, activity.getEntityId());
        Object creditorDays=getValueFromConfig(activity.getTaskConfig(),"CREDITOR_DAYS");
        Object creditAmount=getValueFromConfig(activity.getTaskConfig(),"CREDIT_AMOUNT");
        Object startDate=getValueFromConfig(activity.getTaskConfig(),"START_DATE");
        Object poPaymentTerms=getValueFromConfig(activity.getTaskConfig(),"PO_PAYMENT_TERMS");
        Object mToCPayTerms = getValueFromConfig(activity.getTaskConfig(), "M_TO_C_PAYTERMS");
        PaymentTerms paymentTerms=new PaymentTerms();
        paymentTerms.setPoPaymentTerms(poPaymentTerms!=null?poPaymentTerms.toString():"");
        paymentTerms.setStartDate(startDate!=null?startDate.toString():"DeliveryDate");
        paymentTerms.setCreditAmount(Double.parseDouble(creditAmount!=null?creditAmount.toString():"0.0"));
        paymentTerms.setCreditorDays(Integer.parseInt(creditorDays!=null?creditorDays.toString():"0"));
        customerOrder.setMstackPaymentTerms(paymentTerms);
        if (mToCPayTerms != null) {
            customerOrder.setMToCPayTerms((String) mToCPayTerms);
        }
        mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
    }

    private void updateFile(Activity activity){
        //TODO - Pankaj change the logic to remove the replaced files
//        Optional<Activity> oldActivityOptional=activityRepository.findById(activity.getId());
//        Activity oldActivity=oldActivityOptional.get();
//        List<HashMap<String,Object>> oldTaskConfig=oldActivity.getTaskConfig();
//        List<HashMap<String,Object>> oldTaskConfig=oldActivity.getTaskConfig();
        List<HashMap<String,Object>> taskConfig=activity.getTaskConfig();
        for (HashMap<String,Object> input:taskConfig) {
            if(input.get("inputType").toString().equals("file")){
                List<DocMeta> oldDocMetaList=docMetaRepository.getFileByDocTypeAndEntityId(input.get("key").toString(),activity.getEntityId());
                if(oldDocMetaList!=null){
                    for (DocMeta oldDocMeta:oldDocMetaList) {
                        mongoQueries.softDeleteById(docMetaRepository, oldDocMeta.getId(), DocMeta.class);
                    }
                }
                List<HashMap<String,String>> valueList= (List<HashMap<String, String>>) input.get("value");
                if(valueList==null){
                    return;
                }
                for (HashMap<String,String> valueMap:valueList) {
                    String fileId=valueMap.get("fileId");
                    if(fileId!=null){
//                        DocMeta docMeta=docMetaRepository.getFileByFileId(fileId);
//                        if(docMeta==null){
                            DocMeta docMeta=new DocMeta(input.get("key").toString(),input.get("label").toString(),new ArrayList<>(),"Internal Documents","Final",EntityType.CUSTOMER_ORDER,activity.getEntityId(),null,fileId,valueMap.get("name").toString());
                            docMetaRepository.save(docMeta);
//                        }
                    }
                }
            }
        }
    }


    @SneakyThrows
    private void updateKeyInDispatchOrder(Activity activity,String key,String targetKey){
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, activity.getEntityId());
        Object value=getValueFromConfig(activity.getTaskConfig(),key);
        if(value==null){
            return;
        }
        Field field = customerOrder.getClass().getDeclaredField(targetKey);
        field.setAccessible(true);
        // rewrite - TODO
        if(field.getType().equals(Double.class)){
            field.set(customerOrder,Double.parseDouble(value.toString()));
        }
        // ADDED INTEGER HANDLING
        else if (field.getType().equals(Integer.class)) {
            field.set(customerOrder, Integer.parseInt(value.toString()));
        }
        else if(field.getType().equals(Date.class)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            try {
                Date dateObject = sdf.parse(String.valueOf(getValueFromConfig(activity.getTaskConfig(),key)));
                field.set(customerOrder,dateObject);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        else{
            field.set(customerOrder,value);
        }
        mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
    }
    
    @SneakyThrows
    private void updateValueInDispatchOrder(Activity activity,Object value,String targetKey){
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, activity.getEntityId());
        Field field = customerOrder.getClass().getDeclaredField(targetKey);
        field.setAccessible(true);
        field.set(customerOrder,value);
        customerOrderRepository.save(customerOrder);
    }

    private void updateTypeOfBlInDispatchOrder(Activity activity){
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, activity.getEntityId());
        String value=getValueFromConfig(activity.getTaskConfig(),"TYPE_OF_BL").toString();
        BillOfLading billOfLading=BillOfLading.valueOf(value);
        customerOrder.setTypeOfBL(billOfLading);
        mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
    }


    @SneakyThrows
    private Object getValueFromConfig(List<HashMap<String,Object>> taskConfig,String key){
        if(taskConfig==null||taskConfig.size()==0){
            throw new ServiceException("Task config not found",500);
        }
        for (HashMap input:taskConfig) {
            if(input.get("key").toString().equals(key)){
                return input.get("value");
            }
        }
        return null;
    }

    private void updateSupplierStatus(Event event, Activity activity) {
        List<SupplierOrder> supplierDispatchList = supplierOrderRepository.findAllOrdersWithDispatchOrderId(
            activity.getEntityId()
        );
        for (SupplierOrder dispatchOrder : supplierDispatchList) {
            //            SupplierOrder dispatchOrder = mongoQueries.getEntity(supplierOrderRepository, id);
            OrderStatus orderStatus = OrderStatus.valueOf(event.getContext().get("status").toString());
            dispatchOrder.setStatus(orderStatus);
            HashMap<String, Object> map = new HashMap<>();
            List<HashMap> statusHistory = dispatchOrder.getStatusHistory();
            if (statusHistory == null) {
                statusHistory = new ArrayList<>();
            }
            map.put("status", orderStatus);
            map.put("updatedBy", requestConfig.getEntityId());
            map.put("updatedAt", new Date());
            statusHistory.add(map);
            mongoQueries.updateEntity(supplierOrderRepository, dispatchOrder.getId(), dispatchOrder);
        }

        OrderStatus orderStatus = OrderStatus.valueOf(event.getContext().get("status").toString());

        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, activity.getEntityId());
        String productId = (String) activity.getAdditionalData().get("productId");

        // Not required i suppose
//        customerOrder = mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);

        if (statuses.contains(orderStatus)) {
            for (OrderedProduct orderedProduct : customerOrder.getProducts()) {
                if (orderedProduct.getProduct().getId().equals(productId) &&
                        orderedProduct.getStatus().getValue()<orderStatus.getValue()) {
                    orderedProduct.setStatus(orderStatus);
                }
            }
            checkCustomerOrderUpdate(customerOrder,activity);
        }
    }

    private void checkCustomerOrderUpdate(CustomerOrder customerOrder,Activity activity) {
        OrderStatus orderStatus = customerOrder.getProducts().get(0).getStatus();
        for (OrderedProduct orderedProduct : customerOrder.getProducts()) {
            if (!orderedProduct.getStatus().equals(orderStatus)) {
                customerOrder =
                    mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
                return;
            }
        }
        List<HashMap> statusHistory = customerOrder.getStatusHistory();
        if (statusHistory == null) {
            statusHistory = new ArrayList<>();
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("status", orderStatus);
        map.put("updatedBy", requestConfig.getEntityId());
        map.put("updatedAt", new Date());
        map.put("displayDate", activity.getDisplayDate());
        statusHistory.add(map);
        if(orderStatus.getValue()>customerOrder.getStatus().getValue()){
            customerOrder.setStatus(orderStatus);
        }
        mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
    }

    @SneakyThrows
    private void updateCustomerStatus(Event event, Activity activity) {
        if (!activity.getEntityType().equals(EntityType.CUSTOMER_ORDER)) {
            throw new ServiceException("Something went wrong", 500);
        }
        String id = activity.getEntityId();
        OrderStatus orderStatus = OrderStatus.valueOf(event.getContext().get("status").toString());
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, id);
        if(orderStatus.getValue()>customerOrder.getStatus().getValue()){
            customerOrder.setStatus(orderStatus);
        }
        HashMap<String, Object> map = new HashMap<>();
        List<HashMap> statusHistory = customerOrder.getStatusHistory();
        if (statusHistory == null) {
            statusHistory = new ArrayList<>();
        }
        map.put("status", orderStatus);
        map.put("updatedBy", requestConfig.getEntityId());
        map.put("updatedAt", new Date());
        map.put("displayDate", activity.getDisplayDate());
        statusHistory.add(map);
        customerOrder.setStatusHistory(statusHistory);
        mongoQueries.updateEntity(customerOrderRepository, id, customerOrder);
        //        customerOrderRepository.save(customerOrder);
    }

    private boolean checkTrigger(Event event, Activity activity) {
        String trigger = event.getTrigger();
        if (trigger == null) {
            return false;
        }
        switch (trigger) {
            case "TASK_COMPLETED":
                if (activity.getStatus().equals(ActivityStatus.COMPLETED)) {
                    return true;
                }
                break;

            case "TASK_STARTED":
                if (activity.getStatus().equals(ActivityStatus.IN_PROGRESS)) {
                    return true;
                }
                break;
            case "TASK_UPDATE":
                return true;
        }
        return false;
    }
}
