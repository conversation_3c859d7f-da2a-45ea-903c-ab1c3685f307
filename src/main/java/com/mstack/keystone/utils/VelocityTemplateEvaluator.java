package com.mstack.keystone.utils;

import java.io.StringWriter;
import java.util.HashMap;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;

public class VelocityTemplateEvaluator {

    private static VelocityTemplateEvaluator velocityTemplateEvaluator;
    private final VelocityEngine velocityEngine;

    private VelocityTemplateEvaluator() {
        this.velocityEngine = new VelocityEngine();
        this.velocityEngine.init();
    }

    public static VelocityTemplateEvaluator getInstance() {
        if (velocityTemplateEvaluator == null) {
            velocityTemplateEvaluator = new VelocityTemplateEvaluator();
        }
        return velocityTemplateEvaluator;
    }

    public String evaluate(String template, HashMap<String, Object> context) {
        StringWriter bodyWriter = new StringWriter();
        VelocityContext velocityContext = new VelocityContext(context);
        try {
            this.velocityEngine.evaluate(velocityContext, bodyWriter, "VelocityTemplateEvaluatorError", template);
        } catch (Exception e) {
            //TODO log and exception
        }
        return bodyWriter.toString();
    }
}
