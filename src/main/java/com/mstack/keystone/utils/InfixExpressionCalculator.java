package com.mstack.keystone.utils;

import java.util.Stack;
import org.springframework.stereotype.Component;

@Component
public class InfixExpressionCalculator {

    public int calculateInfixExpression(String expression) {
        int result = 0;
        int operator = 1; // 1 represents addition, -1 represents subtraction

        // Remove any spaces in the expression
        expression = expression.replace(" ", "");

        int i = 0;
        while (i < expression.length()) {
            char c = expression.charAt(i);

            if (c == '-') {
                operator = -1;
                i++;
            } else if (c == '+') {
                operator = 1;
                i++;
            } else {
                // Extract the number from the string
                StringBuilder numBuilder = new StringBuilder();
                while (i < expression.length() && Character.isDigit(expression.charAt(i))) {
                    numBuilder.append(expression.charAt(i));
                    i++;
                }
                int num = Integer.parseInt(numBuilder.toString());
                result += operator * num;
            }
        }

        return result;
    }
}
