package com.mstack.keystone.interceptor;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.repository.user.EmployeeRepository;
import com.mstack.keystone.service.UserActivityService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

@Component
public class RequestInterceptor implements HandlerInterceptor {

    @Autowired
    private RequestConfig requestConfig;

    @Autowired
    UserActivityService userActivityService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        //        return true;
        //        TODO
        //         Capture request headers and store them in the RequestHeaderHolder object
        if (request.getHeader("entityId") == null || request.getHeader("entityType") == null) {
            throw new ServiceException("Unauthorized login", 401);
        }
        requestConfig.setEntityType(request.getHeader("entityType"));
        requestConfig.setEntityId(request.getHeader("entityId"));
        String entityId = request.getHeader("entityId");
        String entityType = request.getHeader("entityType");
        Thread thread = new Thread(() -> {
            LocalDate date = LocalDate.now(ZoneId.of("Asia/Kolkata"));
            userActivityService.updateUserActivity(entityId, entityType, date);
        });
        thread.start();
        return true;
    }

    @Override
    public void postHandle(
        jakarta.servlet.http.HttpServletRequest request,
        jakarta.servlet.http.HttpServletResponse response,
        Object handler,
        @Nullable ModelAndView modelAndView
    ) throws Exception {}

    @Override
    public void afterCompletion(
        HttpServletRequest request,
        HttpServletResponse response,
        Object handler,
        @Nullable Exception ex
    ) throws Exception {}
}
