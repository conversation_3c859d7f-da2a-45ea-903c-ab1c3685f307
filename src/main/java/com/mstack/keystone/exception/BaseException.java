package com.mstack.keystone.exception;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class BaseException extends Exception {

    private int code;

    public BaseException(String message, int code) {
        super(message);
        this.code = code;
    }

    public BaseException(String message, int code, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
}
