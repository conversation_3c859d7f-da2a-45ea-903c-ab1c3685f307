package com.mstack.keystone.mapper;

import com.mstack.keystone.model.dto.CustomerEnquiry;
import com.mstack.keystone.model.dto.CustomerEnquiryStatus;
import com.mstack.keystone.model.dto.EnquiryStatus;
import com.mstack.keystone.model.repository.enquiry.Enquiry;

public class EnquiryToCustomerEnquiryMapper {
    public static CustomerEnquiry mapEnquiryToCustomerEnquiry(Enquiry enquiry) {
        return CustomerEnquiry.builder()
                .id(enquiry.getId())
                .enquiryId(enquiry.getEnquiryId())
                .productName(enquiry.getProductName())
                .casNumber(enquiry.getCasNumber())
                .grade(enquiry.getGrade())
                .createdAt(enquiry.getCreatedAt())
                .createdBy(enquiry.getCreatedBy())
                .lastUpdatedBy(enquiry.getLastUpdatedBy())
                .lastUpdatedAt(enquiry.getLastUpdatedAt())
                .quantity(enquiry.getQuantity())
                .unitOfMeasure(enquiry.getUnitOfMeasure())
                .application(enquiry.getApplication())
                .enquiryStatus(mapEnquiryStatusToCustomerEnquiryStatus(enquiry.getEnquiryStatus()))
                .totalCost(getQuotedPrice(enquiry))
                .expectedDeliveryTime(getExpectedDeliveryTime(enquiry))
                .enquiryIncoterms(enquiry.getIncoTerms())
                // not sending supplier incoterms details
                .supplierIncoterms(null)
//                .supplierIncoterms((enquiry.getQuotation() != null) ? enquiry.getQuotation().getIncoterms() : null)
                .packaging((enquiry.getQuotation() != null) ? enquiry.getQuotation().getPackaging() : null)
                .documents((enquiry.getQuotation() != null) ? enquiry.getQuotation().getDocuments() : null)
                .build();
    }

    private static float getQuotedPrice(Enquiry enquiry) {
        return enquiry.getQuotation() == null ? 0 : enquiry.getQuotation().getQuotedPrice();
    }

    private static int getLeadTime(Enquiry enquiry) {
        return enquiry.getQuotation() == null ? 0 : enquiry.getQuotation().getLeadTime();
    }

    private static int getTransitTime(Enquiry enquiry) {
        return enquiry.getQuotation() == null ? 0 : enquiry.getQuotation().getTransitTime();
    }

    private static int getExpectedDeliveryTime(Enquiry enquiry) {
        return getLeadTime(enquiry) + getTransitTime(enquiry);
    }

    public static CustomerEnquiryStatus mapEnquiryStatusToCustomerEnquiryStatus(EnquiryStatus enquiryStatus) {
        return switch (enquiryStatus) {
            case CREATED, UNASSIGNED -> CustomerEnquiryStatus.INTIATED;
            case ASSIGNED, QUOTED, NEGOTIATE -> CustomerEnquiryStatus.IN_PROGRESS;
            case QUOTATION_APPROVED -> CustomerEnquiryStatus.QUOTED;
            case REJECTED, ACCEPTED -> CustomerEnquiryStatus.CLOSED;
        };
    }

}
