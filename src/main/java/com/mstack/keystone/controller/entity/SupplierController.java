package com.mstack.keystone.controller.entity;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.service.interfaces.ISupplierService;

import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/supplier")
public class SupplierController {

    @Autowired
    ISupplierService supplierService;

    @PostMapping
    public ResponseEntity<Supplier> createSupplier(@RequestBody Supplier supplier) {
        Supplier savedSupplier = supplierService.saveSupplier(supplier);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedSupplier);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<Supplier>> searchSuppliers(@RequestBody FilterRequest request) {
        Page<Supplier> suppliers = supplierService.filterSuppliers(request);
        return ResponseEntity.ok(suppliers);
    }

    @GetMapping("/{supplierId}")
    public ResponseEntity<Supplier> getSupplierById(@PathVariable String supplierId) {
        Supplier supplier = supplierService.getSupplierById(supplierId);
        return ResponseEntity.ok(supplier);
    }

    @PutMapping("/{supplierId}")
    public ResponseEntity<Supplier> updateSupplier(
        @PathVariable String supplierId,
        @RequestBody Supplier updatedSupplier
    ) throws IllegalAccessException {
        updatedSupplier = supplierService.updateSupplier(supplierId, updatedSupplier);
        return ResponseEntity.ok(updatedSupplier);
    }

    @DeleteMapping("/{supplierId}")
    public ResponseEntity deleteSupplier(@PathVariable String supplierId) throws ServiceException {
        supplierService.deleteSupplier(supplierId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/delete-mstack-document")
    public ResponseEntity deleteMstackDocument(@RequestBody DeleteMstackDocument body) {
        supplierService.deleteMstackDocument(body.getDocumentType(),body.getSupplierId(),
                body.getProductId(),body.getFileId());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/product-suppliers/{productId}")
    public ResponseEntity getProductSuppliers(@PathVariable String productId) {
        List<SupplierProductDto> supplierProductDtoList=supplierService.getSuppliersForProductId(productId);
        return ResponseEntity.ok().body(supplierProductDtoList);
    }

    @PostMapping("/upload-mstack-documents")
    public ResponseEntity uploadMstackDocument(@RequestBody UploadMstackDocument body) {
        List<SupplierDocument> documents=supplierService.uploadMstackDocument(body.getSupplierId(), body.getProductId(), body.getDocuments());
        return ResponseEntity.ok().body(documents);
    }
}
