package com.mstack.keystone.controller.entity;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.service.entity.interfaces.ICustomerService;

import java.util.HashMap;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin
@RequestMapping("/customer")
public class CustomerController {

    @Autowired
    ICustomerService customerService;

    @PostMapping
    public ResponseEntity<Customer> createCustomer(@RequestBody Customer customer) {
        Customer savedCustomer = customerService.saveCustomer(customer);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedCustomer);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<Customer>> searchCustomers(@RequestBody FilterRequest request) {
        Page<Customer> customers = customerService.filterCustomers(request);
        return ResponseEntity.ok(customers);
    }

    @PostMapping("/reset-password/{id}")
    public ResponseEntity<Void> resetPassword(@PathVariable String id, @RequestBody HashMap<String, String> body) throws ServiceException {
        String password = body.get("password");
        if (password == null || password.isEmpty() || password.isBlank() || id == null || id.isEmpty() || id.isBlank())
            throw new ServiceException("Password or id cannot be empty ", 400);
        customerService.resetPassword(id, password);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{customerId}")
    public ResponseEntity<Customer> getCustomerById(@PathVariable String customerId) {
        Customer customer = customerService.getCustomerById(customerId);
        return ResponseEntity.ok(customer);
    }

    @PutMapping("/{customerId}")
    public ResponseEntity<Customer> updateCustomer(
        @PathVariable String customerId,
        @RequestBody Customer updatedCustomer
    ) throws IllegalAccessException {
        updatedCustomer = customerService.updateCustomer(customerId, updatedCustomer);
        return ResponseEntity.ok(updatedCustomer);
    }

    @DeleteMapping("/{customerId}")
    public ResponseEntity<Void> deleteCustomer(@PathVariable String customerId) throws ServiceException {
        customerService.deleteCustomer(customerId);
        return ResponseEntity.ok().build();
    }
}
