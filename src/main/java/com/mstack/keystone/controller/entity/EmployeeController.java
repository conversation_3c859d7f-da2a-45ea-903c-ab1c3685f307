package com.mstack.keystone.controller.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.EmployeeUserDetailRequest;
import com.mstack.keystone.model.dto.EmployeeUserDetailResponse;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.service.entity.interfaces.IEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/employee")
public class EmployeeController {

    @Autowired
    IEmployeeService employeeService;

    @PostMapping("/")
    public ResponseEntity<Employee> createEmployee(@RequestBody Employee employee) {
        employee = employeeService.createEmployee(employee);
        return ResponseEntity.status(HttpStatus.CREATED).body(employee);
    }


    @PostMapping("/search")
    public ResponseEntity<Page<Employee>> search(@RequestBody FilterRequest request) {
        Page<Employee> employees = employeeService.filterEmployees(request);
        return ResponseEntity.ok(employees);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Employee> getEmployeeById(@PathVariable String id) {
        Employee employee = employeeService.getEmployee(id);
        return ResponseEntity.ok(employee);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Employee> updateEmployee(@PathVariable String id, @RequestBody Employee employee)
        throws IllegalAccessException {
        employee = employeeService.updateEmployee(id, employee);
        return ResponseEntity.ok(employee);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity deleteCustomer(@PathVariable String id) {
        if (employeeService.deleteEmployee(id)) {
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    @PostMapping("/user-details")
    public ResponseEntity<Void> createEmployeeV2(@RequestBody EmployeeUserDetailRequest employeeUserDetailRequest) throws JsonProcessingException {
        employeeService.createEmployee(employeeUserDetailRequest);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    @PostMapping("/user-details/reset-password")
    public ResponseEntity<Void> resetPassword(@RequestBody EmployeeUserDetailRequest employeeUserDetailRequest) throws JsonProcessingException, ServiceException {
        employeeService.resetPassword(employeeUserDetailRequest);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/user-details")
    public ResponseEntity<List<EmployeeUserDetailResponse>> getEmployeeUserDetails() throws ServiceException {
        return ResponseEntity.ok(employeeService.getEmployeeUserDetails());
    }

    @GetMapping("/user-details/{id}")
    public ResponseEntity<EmployeeUserDetailResponse> getEmployeeUserDetailsById(@PathVariable("id") String id) throws ServiceException, JsonProcessingException {
        return ResponseEntity.ok(employeeService.getEmployeeUserDetailsById(id));
    }

    @PutMapping("/user-details/{id}")
    public ResponseEntity<EmployeeUserDetailResponse> updateEmployeeUserDetail(@PathVariable("id") String id, @RequestBody EmployeeUserDetailRequest employeeUserDetailRequest) throws ServiceException, JsonProcessingException {
        return ResponseEntity.ok(employeeService.updateEmployeeUserDetail(id, employeeUserDetailRequest));
    }

    @DeleteMapping("/user-details/{id}")
    public ResponseEntity<Void> deleteEmployee(@PathVariable("id") String id) throws ServiceException {
        employeeService.deleteEmployeeUserDetail(id);
        return ResponseEntity.ok().build();
    }
}
