package com.mstack.keystone.controller;

import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.service.interfaces.ConfigService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("config")
public class ConfigController {

    @Autowired
    private ConfigService configService;
    

    @GetMapping
    public List<DashBoardConfig> getAllDashboardConfigs() {
        return configService.getAllDashboardConfigs();
    }

    @GetMapping("/{configName}")
    public DashBoardConfig getDashboardConfigByName(@PathVariable String configName) {
        return configService.getDashboardConfigByConfigName(configName);
    }

    @PostMapping
    public ResponseEntity<DashBoardConfig> createDashboardConfig(@RequestBody DashBoardConfig dashboard) {
        DashBoardConfig createdDashboardConfig = configService.createDashboardConfig(dashboard);
        return ResponseEntity.ok(createdDashboardConfig);
    }

    @PutMapping("/{id}")
    public ResponseEntity<DashBoardConfig> updateDashboardConfig(
        @PathVariable String id,
        @RequestBody DashBoardConfig updatedDashboardConfig
    ) {
        DashBoardConfig dashboardConfig = configService.updateDashboardConfig(id, updatedDashboardConfig);
        return ResponseEntity.ok(dashboardConfig);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDashboardConfig(@PathVariable String id) {
        configService.deleteDashboardConfig(id);
        return ResponseEntity.noContent().build();
    }
}
