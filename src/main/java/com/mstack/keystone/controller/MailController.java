package com.mstack.keystone.controller;

import com.mstack.keystone.model.dto.CustomerMailRequest;
import com.mstack.keystone.service.MailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mail")
public class MailController {
    @Autowired
    MailService mailService;

    @PostMapping("/customer")
    public ResponseEntity mailToCustomer(@RequestBody CustomerMailRequest customerMailRequest) {
        mailService.sendMailToCustomer(customerMailRequest);
        return ResponseEntity.ok().build();
    }
}
