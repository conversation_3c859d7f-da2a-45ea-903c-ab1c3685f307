package com.mstack.keystone.controller;

import com.mstack.keystone.model.repository.UserConfig;
import com.mstack.keystone.service.interfaces.IUserConfigService;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("userConfig")
public class UserConfigController {
    @Autowired
    private IUserConfigService userConfigService;

    @PostMapping
    public ResponseEntity<UserConfig> create(@NonNull @RequestBody UserConfig userConfig) {
        return new ResponseEntity<>(userConfigService.createUserConfig(userConfig), HttpStatus.CREATED);
    }

    @GetMapping("/{userId}")
    public ResponseEntity<UserConfig> read(@NonNull @PathVariable String userId) {
        UserConfig userConfig = userConfigService.getUserConfig(userId);
        return userConfig == null ?
                new ResponseEntity<>(null, HttpStatus.NOT_FOUND) :
                new ResponseEntity<>(userConfig, HttpStatus.OK);
    }

    @PutMapping("/{userId}")
    public ResponseEntity<UserConfig> update(@NonNull @PathVariable String userId, @NonNull @RequestBody UserConfig userConfig) {
        return new ResponseEntity<>(userConfigService.updateUserConfig(userId, userConfig), HttpStatus.OK);
    }

    @DeleteMapping("/{userId}")
    public ResponseEntity<Void> delete(@NonNull @PathVariable String userId) {
        userConfigService.deleteUserConfig(userId);
        return ResponseEntity.ok().build();
    }
}
