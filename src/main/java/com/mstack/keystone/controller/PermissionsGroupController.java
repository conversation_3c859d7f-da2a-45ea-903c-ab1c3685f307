package com.mstack.keystone.controller;

import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.model.repository.PermissionsGroup;
import com.mstack.keystone.service.interfaces.IPermissionsGroupService;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

@RestController
@CrossOrigin
@RequestMapping("/permissionsGroup")
public class PermissionsGroupController {
    @Autowired
    private IPermissionsGroupService permissionsGroupService;

    @PostMapping
    public ResponseEntity<PermissionsGroup> create(@NonNull @RequestBody PermissionsGroup permissionsGroup) {
        return new ResponseEntity<>(permissionsGroupService.createPermissionsGroup(permissionsGroup), HttpStatus.CREATED);
    }

    @GetMapping("/{groupIdOrGroupName}")
    public ResponseEntity<PermissionsGroup> read(@NonNull @PathVariable String groupIdOrGroupName) {
        PermissionsGroup permissionsGroup = permissionsGroupService.getPermissionsGroup(groupIdOrGroupName);
        return permissionsGroup == null ?
                new ResponseEntity<>(null, HttpStatus.NOT_FOUND) :
                new ResponseEntity<>(permissionsGroup, HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<PermissionsGroup>> getAllGroups() {
        return new ResponseEntity<>(permissionsGroupService.getAllGroups(), HttpStatus.OK);
    }

    @GetMapping("/permission-config")
    public ResponseEntity<DashBoardConfig> getAllPermissionConfig() {
        return new ResponseEntity<>(permissionsGroupService.getAllPermissionConfig(), HttpStatus.OK);
    }
    @PutMapping("/{groupId}")
    public ResponseEntity<PermissionsGroup> update(@NonNull @PathVariable String groupId, @NonNull @RequestBody PermissionsGroup permissionsGroup) {
        return new ResponseEntity<>(permissionsGroupService.updatePermissionsGroup(groupId, permissionsGroup), HttpStatus.OK);
    }

    @DeleteMapping("/{groupId}")
    public ResponseEntity<Void> delete(@NonNull @PathVariable String groupId) {
        permissionsGroupService.deletePermissionsGroup(groupId);
        return ResponseEntity.ok().build();
    }

}
