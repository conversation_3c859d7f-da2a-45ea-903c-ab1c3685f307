package com.mstack.keystone.controller.email;

import com.mstack.keystone.model.repository.email.EmailTemplate;
import com.mstack.keystone.service.cache.IRedisService;
import com.mstack.keystone.service.email.EmailTemplateServiceImpl;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

@RestController
@RequestMapping("/emailTemplate")
public class EmailTemplateController {

    @Autowired
    EmailTemplateServiceImpl emailTemplateService;

    @Autowired
    IRedisService redisService;

    @PostMapping
    @SneakyThrows
    public ResponseEntity create(
            @RequestParam("htmlTemplate") MultipartFile htmlTemplate,
            @RequestParam("from") String from,
            @RequestParam("subject") String subject,
            @RequestParam("body") String body,
            @RequestParam("emailType") String emailType) {

        EmailTemplate emailTemplate = new EmailTemplate();
        emailTemplate.setFrom(from);
        emailTemplate.setSubject(subject);
        emailTemplate.setBody(body);
        emailTemplate.setEmailType(emailType);
        emailTemplate = emailTemplateService.create(emailTemplate, htmlTemplate);
        return ResponseEntity.status(HttpStatus.OK).body(emailTemplate);
    }

}
