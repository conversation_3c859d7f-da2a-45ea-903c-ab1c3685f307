package com.mstack.keystone.controller.email;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mstack.keystone.model.dto.SendMailRequest;
import com.mstack.keystone.service.cache.IRedisService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/email")
public class EmailController {

    @Autowired
    IRedisService redisService;

    @Autowired
    ObjectMapper objectMapper;

    @PostMapping("/send")
    @SneakyThrows
    public ResponseEntity sendMail(@RequestBody SendMailRequest sendMailRequest) {
        redisService.enqueueMessage(objectMapper.writeValueAsString(sendMailRequest));
        return ResponseEntity.status(HttpStatus.OK).build();
    }
}
