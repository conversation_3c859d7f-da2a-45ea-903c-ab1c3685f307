package com.mstack.keystone.controller.docEngine;

import com.mstack.keystone.model.repository.docEngine.DocEngineTemplate;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.service.docEngine.DocEngineTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/docEngine/template")
public class DocEngineTemplateController {

    @Autowired
    DocEngineTemplateService templateService;

    @PostMapping
    public ResponseEntity<DocEngineTemplate> createTemplate(@RequestBody DocEngineTemplate template) {
        return new ResponseEntity<>(templateService.createTemplate(template), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<DocEngineTemplate> getTemplateById(@PathVariable String id) {
        Optional<DocEngineTemplate> template = templateService.getTemplateById(id);
        if (template.isPresent()) {
            return new ResponseEntity<>(template.get(), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<DocEngineTemplate> updateTemplate(@PathVariable String id, @RequestBody DocEngineTemplate updatedTemplate) {
        Optional<DocEngineTemplate> existingTemplate = templateService.getTemplateById(id);
        if (existingTemplate.isPresent()) {
            DocEngineTemplate response = templateService.updateTemplate(existingTemplate.get(), updatedTemplate);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @PostMapping("/aggregate")
    public ResponseEntity aggregateDocMeta(@RequestBody HashMap docMeta) {
        List list=templateService.aggregateDataForDocument(docMeta);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @GetMapping("/valid-fileTypes/{id}")
    public ResponseEntity getFiles(@PathVariable String id) {
        List list=templateService.getValidFileTypesForOrder(id);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @GetMapping("/templates-by-category/{category}/{id}")
    public ResponseEntity getTemplatesByCategory(@PathVariable String category, @PathVariable String id) {
        List list = templateService.getTemplatesByCategory(category, id);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTemplate(@PathVariable String id) {
        Optional<DocEngineTemplate> template = templateService.getTemplateById(id);
        if (template.isPresent()) {
            templateService.deleteTemplate(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping
    public ResponseEntity<List<DocEngineTemplate>> getAllTemplates() {
        List<DocEngineTemplate> templateList = templateService.getAllTemplates();
        return new ResponseEntity<>(templateList, HttpStatus.OK);
    }
}