package com.mstack.keystone.controller.docEngine;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.service.docEngine.DocMetaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/docEngine/docMeta")
public class DocMetaController {
    @Autowired
    DocMetaService docMetaService;

    @PostMapping
    public ResponseEntity<DocMeta> createDocMeta(@RequestBody DocMeta docMeta) {
//        TODO check if it will work
//        if (docMeta != null && docMeta.getFileName() != null) {
//            //add default pdf extension to file name
//            docMeta.setFileName(docMeta.getFileName() + ".pdf");
//        }
        return new ResponseEntity<>(docMetaService.createDocMeta(docMeta), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<DocMeta> getDocMetaById(@PathVariable String id) {
        Optional<DocMeta> docMeta = docMetaService.getDocMetaById(id);
        if (docMeta.isPresent()) {
            return new ResponseEntity<>(docMeta.get(), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<DocMeta> updateDocMeta(@PathVariable String id, @RequestBody DocMeta updatedDocMeta) {
        Optional<DocMeta> existingDocMeta = docMetaService.getDocMetaById(id);
        if (existingDocMeta.isPresent()) {
            DocMeta response = docMetaService.updateDocMeta(existingDocMeta.get(), updatedDocMeta);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/files/{id}")
    public ResponseEntity getFiles(@PathVariable String id) {
        List list=docMetaService.getAllFiles(id);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @GetMapping("/{id}/files-zip")
    public ResponseEntity getFilesAsZip(@PathVariable String id) {
        byte[] zip = docMetaService.getAllFilesAsZip(id);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", id + "_files.zip");
        return new ResponseEntity<>(zip, headers, HttpStatus.OK);
    }

    @GetMapping("/draft-files/{id}")
    public ResponseEntity getDraftFiles(@PathVariable String id) {
        HashMap map=docMetaService.getAllDraftFiles(id);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @GetMapping("/customer/files/{id}")
    public ResponseEntity customerFiles(@PathVariable String id) {
        HashMap map=docMetaService.getAllCustomerFiles(id);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    // Delete operation
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDocMeta(@PathVariable String id) {
        Optional<DocMeta> docMeta = docMetaService.getDocMetaById(id);
        if (docMeta.isPresent()) {
            docMetaService.deleteDocMeta(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    // Additional Read operation (get all documents)
    @GetMapping
    public ResponseEntity<List<DocMeta>> getAllDocMeta() {
        List<DocMeta> docMetaList = docMetaService.getAllDocMeta();
        return new ResponseEntity<>(docMetaList, HttpStatus.OK);
    }


    @GetMapping("/generateDocMetaForOrders")
    public ResponseEntity checkDocs() throws ServiceException {
        return ResponseEntity.ok(docMetaService.generateDocMetaForOrders());
    }

    @GetMapping("/customer-order-files/{id}")
    public ResponseEntity<List<DocMeta>> getFilesByCustomerOrderId(@PathVariable("id") String customerOrderId) {
        return new ResponseEntity<>(docMetaService.getFilesByCustomerOrderId(customerOrderId), HttpStatus.OK);
    }


}
