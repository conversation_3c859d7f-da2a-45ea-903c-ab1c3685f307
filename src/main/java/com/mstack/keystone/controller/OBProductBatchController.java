package com.mstack.keystone.controller;

import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.ProductBatchDetailRequestDto;
import com.mstack.keystone.model.dto.SDOProductBatchDto;
import com.mstack.keystone.model.dto.SDOVirtualProductBatchDto;
import com.mstack.keystone.model.repository.inventory.OBProductBatch;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.service.batchDetails.OBProductBatchService;
import com.mstack.keystone.service.batchDetails.ProductBatchDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/orderbook-product-batch")
public class OBProductBatchController {

    @Autowired
    private OBProductBatchService obProductBatchService;

    @GetMapping
    private ResponseEntity<OBProductBatch> getBatch(@RequestParam("id") String id) {
        return ResponseEntity.ok().body(obProductBatchService.getBatch(id));
    }


}
