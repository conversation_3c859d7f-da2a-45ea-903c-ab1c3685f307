package com.mstack.keystone.controller;

import com.mstack.keystone.model.dto.ProductBatchDetailRequestDto;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.SDOProductBatchDto;
import com.mstack.keystone.model.dto.SDOVirtualProductBatchDto;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.service.batchDetails.ProductBatchDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/product-batch")
public class ProductBatchController {

    @Autowired
    private ProductBatchDetailService productBatchDetailService;

    @PostMapping
    public ResponseEntity<ProductBatchDetailRequestDto> createProductBatchDetails(@RequestBody ProductBatchDetailRequestDto productBatchDetailRequestDto) {
        return ResponseEntity.status(HttpStatus.CREATED).body(
                productBatchDetailService.createProductBatchDetails(productBatchDetailRequestDto)
        );
    }

    @GetMapping
    public ResponseEntity<ProductBatchDetailRequestDto> getProductBatchDetails(@RequestParam("supplierOrderId") String supplierOrderId) {
        return ResponseEntity.ok().body(productBatchDetailService.getProductBatchDetails(supplierOrderId));
    }

    @PutMapping
    public ProductBatchDetailRequestDto updateProductBatchDetails(@RequestBody ProductBatchDetailRequestDto productBatchDetailRequestDto) {
        productBatchDetailRequestDto=productBatchDetailService.updateProductBatchDetails(productBatchDetailRequestDto);
        return productBatchDetailRequestDto;
    }


    @GetMapping("/{productBatchDetailId}")
    public ProductBatchDetail getProductBatchDetailById(@PathVariable String productBatchDetailId) {
        return productBatchDetailService.getProductBatchDetailById(productBatchDetailId);
    }

    @DeleteMapping("/{productBatchDetailId}")
    public void deleteProductBatchDetail(@PathVariable String productBatchDetailId) {
        productBatchDetailService.deleteProductBatchDetail(productBatchDetailId);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<ProductBatchDetail>> searchProductBatchDetail(@RequestBody FilterRequest request) {
        Page<ProductBatchDetail> productBatchDetailPage = productBatchDetailService.filterByProductBatchDetail(request);
        return ResponseEntity.ok(productBatchDetailPage);
    }

    @GetMapping("/unallocated-batches")
    public List<SDOProductBatchDto> getUnallocatedSupplierBatchesByCustomerOrderId(@RequestParam("customerDispatchOrderId") String orderId) {
        return productBatchDetailService.getUnallocatedSupplierBatchesByCustomerOrderId(orderId);
    }

    @GetMapping("/allocated-batches")
    public List<SDOProductBatchDto> getallocatedSupplierBatchesByCustomerOrderId(@RequestParam("customerDispatchOrderId") String orderId) {
        return productBatchDetailService.getallocatedSupplierBatchesByCustomerOrderId(orderId);
    }

    // TODO ROUTE CAN BE REFACTORED
    @GetMapping("/virtual-batches")
    public List<SDOVirtualProductBatchDto> getVritualBatches(@RequestParam("productId") String productId, @RequestParam("inventoryId") String inventoryId, @RequestParam("packagingId") String packagingId, @RequestParam("customerOrderDispatchId") String customerOrderDispatchId) {
        return productBatchDetailService.getVirtualBatchesByInventoryIdAndProductId(productId, inventoryId, packagingId, customerOrderDispatchId);
    }

//    @PostMapping("/allocate-virtual-batches")
//    public ResponseEntity<Void> allocateVritualBatches(@RequestParam("orderId") String orderId, List<SDOVirtualProductBatchDto> sdoVirtualProductBatchDtos) {
//        productBatchDetailService.allocateVirutalBatches(orderId, sdoVirtualProductBatchDtos);
//        return ResponseEntity.ok().build();
//    }

    @PostMapping("/allocate-batches")
    public ResponseEntity<Void> allocateBatches(@RequestParam("orderId") String orderId, @RequestBody List<ProductBatchDetail> productBatchDetails) {
        productBatchDetailService.allocateBatches(orderId, productBatchDetails);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/reset-batches")
    public ResponseEntity<Void> resetBatches(@RequestParam("orderId") String orderId) {
        productBatchDetailService.resetBatches(orderId);
        return ResponseEntity.ok().build();
    }
}
