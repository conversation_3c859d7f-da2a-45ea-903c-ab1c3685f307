package com.mstack.keystone.controller;

import com.mstack.keystone.scripts.InvoiceScripts;
import com.mstack.keystone.service.UserActivityService;
import com.mstack.keystone.service.enquiry.EnquiryServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileNotFoundException;
import java.io.IOException;

@RestController
@RequestMapping("/health")
public class HealthController {
//    @Autowired
//    ReportingService reportingService;

//    @Autowired
//    PackagingScripts packagingScripts;
//
//    @Autowired
//    RoleScripts roleScripts;

    @Autowired
    InvoiceScripts invoiceScripts;

    @Autowired
    UserActivityService userActivityService;

    @Autowired
    EnquiryServiceImpl enquiryService;


    @GetMapping("/ping")
    public ResponseEntity ping() {
//        userActivityService.generateActivityReport();
//        enquiryService.generateDailyClientEnquriyReport();
        return ResponseEntity.ok(null);
    }
// uncomment below to test generate report
//@GetMapping("/generateReport")
//public ResponseEntity generateReport() throws IOException, ServiceException {
////        reportingService.generateOrderReport();
////        reportingService.generateOrderBookReport();
////    reportingService.generateSupplierReport();
//    return ResponseEntity.ok(null);
//}

    @GetMapping("/packaging")
    public ResponseEntity addPackaging() throws FileNotFoundException {
//        packagingScripts.addPackaging();
//        packagingScripts.updatePackagingInOrderBook();
//        packagingScripts.updatePackagingInOrder();
//        packagingScripts.updatePackagingInSupplier();
//        roleScripts.updateRole();
        return ResponseEntity.ok(null);
    }

//    @GetMapping("/generate-invoice-report")
//    public ResponseEntity<Void> generateInvoiceReport() throws IOException {
////        invoiceScripts.generateMstackInvoiceReportCsv();
//        return ResponseEntity.ok(null);
//    }


}
