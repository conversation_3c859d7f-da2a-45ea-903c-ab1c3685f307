package com.mstack.keystone.controller.enquiry;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.repository.ClientEnquiry;
import com.mstack.keystone.model.repository.EnquiryQuotationHistory;
import com.mstack.keystone.model.repository.enquiry.Enquiry;
import com.mstack.keystone.service.enquiry.interfaces.IEnquiryService;
import jakarta.validation.Valid;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@RestController
@CrossOrigin
@RequestMapping("/enquiry")
public class EnquiryController {

    @Autowired
    private IEnquiryService enquiryService;

    @GetMapping("/{id}")
    public ResponseEntity<EnquiryDto> getEnquiryById(@PathVariable String id) {
        EnquiryDto enquiry = enquiryService.getEnquiryById(id);
        return ResponseEntity.ok().body(enquiry);
    }

    @PostMapping
    public ResponseEntity<Enquiry> createEnquiry(@RequestBody Enquiry enquiry) {
        Enquiry createdEnquiry = enquiryService.createEnquiry(enquiry);
        return ResponseEntity.ok(createdEnquiry);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Enquiry> updateEnquiry(@PathVariable String id, @RequestBody Enquiry enquiry) {
        Enquiry updatedEnquiry = enquiryService.updateEnquiry(id, enquiry);
        return ResponseEntity.ok(updatedEnquiry);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEnquiry(@PathVariable String id) {
        enquiryService.deleteEnquiry(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/search")
    public ResponseEntity<Page<EnquiryDto>> searchEnquiry(@RequestBody FilterRequest request) {
        Page<EnquiryDto> enquiryPage = enquiryService.filterEnquiries(request);
        return ResponseEntity.ok(enquiryPage);
    }
    @PostMapping("/assign")
    public ResponseEntity assignEnquiry(@RequestBody HashMap<String,String> body) {
        EnquiryDto enquiryDto=enquiryService.assignEnquiry(body.get("employeeId"),body.get("enquiryId"));
        return ResponseEntity.ok().body(enquiryDto);
    }

    @PostMapping("/approveEnquiry")
    public ResponseEntity approveEnquiry(@RequestBody HashMap<String,Object> body) {
        EnquiryDto enquiryDto=enquiryService.approveEnquiry(body.get("enquiryId").toString(),
                Boolean.valueOf(body.get("approved").toString()));
        return ResponseEntity.ok().body(enquiryDto);
    }

    @PostMapping("/negotiate/{id}")
    @SneakyThrows
    public ResponseEntity negotiateQuotation(@PathVariable String id, @RequestBody HashMap<String, String> body) {
        if (body.isEmpty() || !body.containsKey("negotiationRemark"))
            throw new ServiceException("negotation remark cannot be empty ", 400);
        EnquiryDto enquiryDto = enquiryService.negotiate(id, body.get("negotiationRemark"));
        return ResponseEntity.ok().body(enquiryDto);
    }

    @PostMapping("/approve-quotation/{id}")
    public ResponseEntity approveQuotation(@PathVariable String id,@RequestBody HashMap body) {
        EnquiryDto enquiryDto=enquiryService.approveQuotation(id,Float.valueOf(body.get("quotedPrice").toString()));
        return ResponseEntity.ok().body(enquiryDto);
    }

    @PostMapping("/reject-quotation/{id}")
    public ResponseEntity rejectQuotation(@PathVariable String id, @RequestBody HashMap body) {
        EnquiryDto enquiryDto = enquiryService.rejectQuotation(id, String.valueOf(body.get("rejectionReason").toString()), EnquiryStatus.REJECTED);
        return ResponseEntity.ok().body(enquiryDto);
    }

    @GetMapping("/get-quotation-history/{enquiryId}")
    public ResponseEntity<EnquiryQuotationHistory> getQuotationHistory(@PathVariable String enquiryId) {
        EnquiryQuotationHistory enquiryQuotationHistory = enquiryService.getQuotationHistory(enquiryId);
        return ResponseEntity.ok().body(enquiryQuotationHistory);
    }

    @PostMapping("/create-quotation/{enquiryId}")
    @SneakyThrows
    public ResponseEntity<EnquiryDto> createQuotation(@RequestBody SupplierQuotation quotation, @PathVariable String enquiryId) {
        EnquiryDto enquiryDto = enquiryService.createQuotation(quotation, enquiryId);
        return ResponseEntity.ok().body(enquiryDto);
    }

    @PostMapping("/close-enquiry/{id}")
    public ResponseEntity customerUpdateEnquiry(@RequestBody HashMap body) throws ServiceException {
        String rejectionRemark = "";
        if (body.isEmpty() || !body.containsKey("status"))
            throw new ServiceException("Please provide status for enquiry closure ", 400);
        if (EnquiryStatus.valueOf(body.get("status").toString()) == EnquiryStatus.REJECTED) {
            if (!body.containsKey("rejectionRemark"))
                throw new ServiceException("Please provide reason to reject eqnuiry ", 400);
            rejectionRemark = body.get("rejectionRemark").toString();
        }

        EnquiryDto enquiryDto=enquiryService.customerUpdateQuotation(body.get("enquiryId").toString(),
                EnquiryStatus.valueOf(body.get("status").toString()), rejectionRemark);
        return ResponseEntity.ok().body(enquiryDto);
    }

    @PostMapping("/customer/{id}")
    public ResponseEntity<Page<CustomerEnquiry>> getEnquiresByCustomerId(@PathVariable String id, @RequestBody FilterRequest filterRequest) {
        Page<CustomerEnquiry> enquiries = enquiryService.findEnquiriesByCustomerId(id, filterRequest);
        return ResponseEntity.ok().body(enquiries);
    }

    @GetMapping("/customer/{customerId}/enquiryDetail/{enquiryId}")
    public ResponseEntity<CustomerEnquiry> getEnquiresByCustomerId(@PathVariable String customerId, @PathVariable String enquiryId) {
        CustomerEnquiry enquiries = enquiryService.findEnquiryByCustomerIdAndEnquiryId(customerId, enquiryId);
        return ResponseEntity.ok().body(enquiries);
    }

    @PostMapping("/form-submission")
    public ResponseEntity<Void> submitCustomerEnquiry(@Valid @RequestBody ClientEnquiry formData, BindingResult errors) throws JsonProcessingException, ServiceException {
        if (errors.hasErrors()) {
            String errorMessage = errors.getFieldErrors()
                    .stream()
                    .map(FieldError::getDefaultMessage)
                    .reduce((msg1, msg2) -> msg1 + ", " + msg2)
                    .orElse("Validation failed");
            throw new ServiceException(errorMessage, 400);
        }
        enquiryService.submitCustomerEnquiry(formData);
        return ResponseEntity.ok().build();
    }

}
