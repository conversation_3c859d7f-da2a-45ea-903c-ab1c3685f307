package com.mstack.keystone.controller.report;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.model.dto.report.GenerateReportRequest;
import com.mstack.keystone.service.reporting.ReportingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/report")
public class ReportController {
    @Autowired
    ReportingService reportingService;

    @Autowired
    RequestConfig requestConfig;

    @PostMapping("/generate-report")
    public ResponseEntity<Void> generateReport(@RequestBody GenerateReportRequest generateReportRequest) throws IOException {
        generateReportRequest.setUserId(requestConfig.getEntityId());
        Thread customThread = new Thread(() -> {
            try {
                reportingService.generateReport(generateReportRequest);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        customThread.start();
        return new ResponseEntity<>(null, HttpStatus.OK);
    }
}
