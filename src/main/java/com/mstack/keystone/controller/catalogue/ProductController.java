package com.mstack.keystone.controller.catalogue;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.service.catalogue.IProductService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/product")
public class ProductController {

    private final IProductService productService;

    @Autowired
    public ProductController(IProductService productService) {
        this.productService = productService;
    }

    @PostMapping
    public ResponseEntity<Product> createProduct(@RequestBody Product product) {
        Product createdProduct = productService.createProduct(product);
        return new ResponseEntity<>(createdProduct, HttpStatus.CREATED);
    }

    @GetMapping("/{productId}")
    @SneakyThrows
    public ResponseEntity<Product> getProductById(@PathVariable String productId) {
        Product product = productService.getProductById(productId);
        return new ResponseEntity<>(product, HttpStatus.OK);
    }

    @PutMapping("/{productId}")
    public ResponseEntity<Product> updateProduct(@PathVariable String productId, @RequestBody Product updatedProduct) {
        Product product = productService.updateProduct(productId, updatedProduct);
        return new ResponseEntity<>(product, HttpStatus.OK);
    }

    @DeleteMapping("/{productId}")
    public ResponseEntity<Void> deleteProduct(@PathVariable String productId) throws ServiceException {
        productService.deleteProduct(productId);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<Product>> searchCustomers(@RequestBody FilterRequest request) {
        Page<Product> orderBookPage = productService.filterProducts(request);
        return ResponseEntity.ok(orderBookPage);
    }

    @GetMapping
    public ResponseEntity<Product> getProductByProductName(@RequestParam String productName){
        Product product= productService.getProductByProductName(productName);
        return new ResponseEntity<>(product,HttpStatus.OK);
    }
    @PostMapping("/upload")
    public ResponseEntity<String> uploadCsvFile(@RequestParam("file") MultipartFile file) {
        try {
            productService.processCsv(file);
            return ResponseEntity.ok("File uploaded and processed successfully.");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Error processing the CSV file: " + e.getMessage());
        }
    }
}
