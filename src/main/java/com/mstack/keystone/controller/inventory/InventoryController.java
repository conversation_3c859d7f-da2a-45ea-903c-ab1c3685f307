package com.mstack.keystone.controller.inventory;

import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.inventory.Inventory;
import com.mstack.keystone.service.inventory.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/inventory")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @PostMapping
    public Inventory createInventory(@RequestBody Inventory inventory) {
        return inventoryService.createInventory(inventory);
    }

    @GetMapping("/{inventoryId}")
    public Inventory getInventoryById(@PathVariable String inventoryId) {
        return inventoryService.getInventoryById(inventoryId);
    }

    @PutMapping("/{inventoryId}")
    public Inventory updateInventory(@PathVariable String inventoryId, @RequestBody Inventory inventory) {
        return inventoryService.updateInventory(inventoryId, inventory);
    }

    @DeleteMapping("/{inventoryId}")
    public void deleteInventory(@PathVariable String inventoryId) {
        inventoryService.deleteInventory(inventoryId);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<Inventory>> searchInventory(@RequestBody FilterRequest request) {
        Page<Inventory> inventoryPage = inventoryService.filterByInventory(request);
        return ResponseEntity.ok(inventoryPage);
    }
}

