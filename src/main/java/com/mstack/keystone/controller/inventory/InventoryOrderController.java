//package com.mstack.keystone.controller.inventory;
//
//import com.mstack.keystone.service.inventory.InventoryOrderService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//@RequestMapping("/inventory/order")
//public class InventoryOrderController {
//
//    @Autowired
//    private InventoryOrderService inventoryOrderService;
//
//    @PostMapping("/")
//    public InventoryOrder createInventoryOrder(@RequestBody InventoryOrder inventoryOrder) {
//        return inventoryOrderService.createInventoryOrder(inventoryOrder);
//    }
//
//    @GetMapping("/{inventoryOrderId}")
//    public InventoryOrder getInventoryOrderById(@PathVariable String inventoryOrderId) {
//        return inventoryOrderService.getInventoryOrderById(inventoryOrderId);
//    }
//
//    @PutMapping("/{inventoryOrderId}")
//    public InventoryOrder updateInventoryOrder(@PathVariable String inventoryOrderId, @RequestBody InventoryOrder inventoryOrder) {
//        return inventoryOrderService.updateInventoryOrder(inventoryOrderId, inventoryOrder);
//    }
//
//    @DeleteMapping("/{inventoryOrderId}")
//    public void deleteInventoryOrder(@PathVariable String inventoryOrderId) {
//        inventoryOrderService.deleteInventoryOrder(inventoryOrderId);
//    }
//}
//
