package com.mstack.keystone.controller.inventory;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.AllocatedInventoryItem.AllocatedInventoryItem;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.InventoryProductAllocationDto;
import com.mstack.keystone.model.dto.InventoryProductDto;
import com.mstack.keystone.model.repository.inventory.InventoryProduct;
import com.mstack.keystone.service.inventory.InventoryProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/inventory-product")
public class InventoryProductController {

    @Autowired
    private InventoryProductService inventoryProductService;

    @GetMapping("/{inventoryId}")
    public List<InventoryProduct> getAllProductsOfInventory(@PathVariable String inventoryId) {
        return inventoryProductService.getAllProductsOfInventory(inventoryId);
    }

    @PostMapping("/getBatches")
    public ResponseEntity getAllBatches(@RequestBody HashMap request) {
        return new ResponseEntity<>(inventoryProductService.getAllBatches(request), HttpStatus.OK);
    }

    @PostMapping("/getTransactions")
    public ResponseEntity getAllTransactions(@RequestBody FilterRequest request) {
        return new ResponseEntity<>(inventoryProductService.getAllTransactions(request), HttpStatus.OK);
    }

    @GetMapping("/getStockDetail/{productId}")
    public ResponseEntity getAllTransactions(@PathVariable String productId) {
        return new ResponseEntity<>(inventoryProductService.getInventoryProductStockDetail(productId), HttpStatus.OK);
    }

    @GetMapping("/checkInventory/{orderBookId}")
    public ResponseEntity checkInventory(@PathVariable String orderBookId) {
        return new ResponseEntity<>(inventoryProductService.checkInventory(orderBookId), HttpStatus.OK);
    }

    @GetMapping("/product-units/{orderBookId}")
    public ResponseEntity<List<InventoryProductAllocationDto>> getAvaialbleUnitsForOrderBookId(@PathVariable String orderBookId) {
        return new ResponseEntity<>(inventoryProductService.getAvaialableUnitsForOrderBookId(orderBookId), HttpStatus.OK);
    }

    @PostMapping("/product-units/{orderBookId}")
    public ResponseEntity<Void> allocateAvaialbleUnitsForOrderBookId(@PathVariable String orderBookId, @RequestBody List<InventoryProductAllocationDto> inventoryProductAllocations) throws ServiceException {
        inventoryProductService.allocateAvaialableUnitsForOrderBookId(orderBookId, inventoryProductAllocations);
        return ResponseEntity.ok().build();
    }


    @PostMapping("/search")
    public ResponseEntity<Page<InventoryProductDto>> searchInventoryProduct(@RequestBody FilterRequest request) {
        Page<InventoryProductDto> inventoryProductPage = inventoryProductService.filterInventoryProduct(request);
        return ResponseEntity.ok(inventoryProductPage);
    }

    @PutMapping("/product-units/{orderBookId}")
    public ResponseEntity updateProductUnits(@PathVariable String orderBookId, @RequestBody List<InventoryProductAllocationDto> body) {
        inventoryProductService.updateProductUnits(orderBookId,body);
        return new ResponseEntity<>(null, HttpStatus.OK);
    }

    @GetMapping("/getAllocatedInventory/{orderbookId}")
    public ResponseEntity<List<AllocatedInventoryItem>> getAllocatedInventory(@PathVariable String orderbookId) {
        return new ResponseEntity<>(inventoryProductService.getAllocatedInventory(orderbookId), HttpStatus.OK);
    }

    @PostMapping("/unAllocateInventory")
    public ResponseEntity<Void> unAllocateInventory(@RequestBody List<String> obBatchIds) throws ServiceException {
        inventoryProductService.unAllocateInventory(obBatchIds);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/getAIForProduct/{orderbookId}/{productId}/{packagingId}")
    public ResponseEntity getAllocatedInventoryForProduct(@PathVariable String orderbookId,@PathVariable String productId,@PathVariable String packagingId) {
        return new ResponseEntity<>(inventoryProductService.getAllocatedInventoryForProduct(orderbookId,productId,packagingId), HttpStatus.OK);
    }
}
