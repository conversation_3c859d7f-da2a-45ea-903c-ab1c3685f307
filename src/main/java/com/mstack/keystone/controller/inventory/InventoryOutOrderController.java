package com.mstack.keystone.controller.inventory;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.repository.inventory.InventoryOutOrder;
import com.mstack.keystone.service.inventory.InventoryOutOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/inventory-out-order")
public class InventoryOutOrderController {
    @Autowired
    InventoryOutOrderService inventoryOutOrderService;

    @GetMapping("getByOrderBook/{orderBookId}")
    public ResponseEntity getProductFromInventory(@PathVariable String orderBookId) {
        return new ResponseEntity<>(inventoryOutOrderService.getInventoryOutOrderFromOrderbookId(orderBookId), HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<InventoryOutOrder> createInventoryOutOrder(@RequestBody InventoryOutOrder inventoryOutOrder) {
        InventoryOutOrder res = inventoryOutOrderService.createInventoryOutOrder(inventoryOutOrder);
        return ResponseEntity.status(HttpStatus.CREATED.value()).body(res);
    }

    @DeleteMapping("/{inventoryOutOrderId}")
    public ResponseEntity<Void> deleteInventoryOutOrder(@PathVariable String inventoryOutOrderId) throws ServiceException {
        inventoryOutOrderService.deleteInventoryOutOrder(inventoryOutOrderId);
        return ResponseEntity.ok().build();
    }
}
