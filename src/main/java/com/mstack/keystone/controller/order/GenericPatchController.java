package com.mstack.keystone.controller.order;

import com.mstack.keystone.service.order.interfaces.GenericPatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@CrossOrigin
@RequestMapping("/patch")
public class GenericPatchController {

  @Autowired
  private GenericPatchService genericPatchService;

  @PatchMapping("/{entity}/{id}")
  public ResponseEntity<?> patchEntity(
      @PathVariable String entity,
      @PathVariable String id,
      @RequestBody Map<String, Object> updates) {

    Object updatedEntity = genericPatchService.patchEntity(entity, id, updates);
    return ResponseEntity.ok(updatedEntity);
  }
}
