package com.mstack.keystone.controller.order;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.HardAssignPoRequest;
import com.mstack.keystone.model.dto.OrderPOValidationRequest;
import com.mstack.keystone.model.dto.OrderPOValidationResponse;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.service.order.interfaces.IOrderBookService;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/order-book")
public class OrderBookController {

    private final IOrderBookService orderBookService;

    @Autowired
    public OrderBookController(IOrderBookService orderBookService) {
        this.orderBookService = orderBookService;
    }

    @PostMapping
    public ResponseEntity<OrderBook> createOrderBook(@RequestBody OrderBook orderBook) {
        OrderBook createdOrderBook = orderBookService.createOrderBook(orderBook);
        return new ResponseEntity<>(createdOrderBook, HttpStatus.CREATED);
    }

    @GetMapping("/{orderBookId}")
    public ResponseEntity<OrderBook> getOrderBookById(@PathVariable String orderBookId) {
        OrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        return new ResponseEntity<>(orderBook, HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<OrderBook>> getOrderBookByCustomerId(@RequestParam("customerId") String customerId) {
        return new ResponseEntity<>(orderBookService.getCustomerRelatedOrders(customerId), HttpStatus.OK);
    }

    @PostMapping("/validatePO")
    public ResponseEntity<OrderPOValidationResponse> checkOrderBookByPONumber(@RequestBody OrderPOValidationRequest orderPOValidationRequest) {
        OrderBook orderBook = orderBookService.getByPONumber(
                orderPOValidationRequest.getPoNumber()
        );
        OrderPOValidationResponse orderPOValidationResponse=orderBook!=null?new OrderPOValidationResponse(false):new OrderPOValidationResponse(true);
        return new ResponseEntity<>(orderPOValidationResponse,HttpStatus.OK);
    }

    @PutMapping("/{orderBookId}")
    public ResponseEntity<OrderBook> updateOrderBook(
        @PathVariable String orderBookId,
        @RequestBody OrderBook updatedOrderBook
    ) {
        OrderBook orderBook = orderBookService.updateOrderBook(orderBookId, updatedOrderBook);
        return new ResponseEntity<>(orderBook, HttpStatus.OK);
    }

    @DeleteMapping("/{orderBookId}")
    public ResponseEntity<Void> deleteOrderBook(@PathVariable String orderBookId) {
        orderBookService.deleteOrderBook(orderBookId);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<OrderBook>> searchCustomers(@RequestBody FilterRequest request) {
        Page<OrderBook> orderBookPage = orderBookService.filterOrderBook(request);
        return ResponseEntity.ok(orderBookPage);
    }

    @PostMapping("/hard-assign")
    public ResponseEntity<Void> hardAssignPo(@RequestBody HardAssignPoRequest hardAssignPoRequest) throws ServiceException {
        orderBookService.hardAssignPoNumber(hardAssignPoRequest);
        return ResponseEntity.ok().build();
    }
}
