package com.mstack.keystone.controller.order;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.DocComment;
import com.mstack.keystone.model.repository.DocumentComments;
import com.mstack.keystone.service.order.DocReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin
@RequestMapping("/document-review")
public class DocumentReviewController {
    @Autowired
    DocReviewService docReviewService;
    @GetMapping("/comments/{id}")
    public ResponseEntity<DocumentComments> getDocComments(@PathVariable String id) {
        DocumentComments documentComments=docReviewService.getCommentsForFile(id);
        return ResponseEntity.ok(documentComments);
    }
    @PostMapping("/addComment/{id}")
    public ResponseEntity<DocComment> createComment(@PathVariable String id, @RequestBody DocComment comment) {
        DocComment documentComments=docReviewService.addComment(id,comment);
        return ResponseEntity.ok(documentComments);
    }

    @PutMapping("/updateComment/{id}")
    public ResponseEntity<DocComment> updateComment(@PathVariable String id, @RequestBody DocComment comment) {
        DocComment documentComments=docReviewService.updateDocument(id,comment);
        return ResponseEntity.ok(documentComments);
    }

    @DeleteMapping("/deleteComment/{id}")
    public ResponseEntity deleteComment(@PathVariable String id) {
        docReviewService.deleteComment(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
    @DeleteMapping("/deleteAllComment/{fileId}")
    public ResponseEntity deleteAllComment(@PathVariable String fileId) {
        docReviewService.deleteAllComments(fileId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/approve/{orderId}/{docType}/{id}")
    public ResponseEntity approveDocument(@PathVariable String id,@PathVariable String orderId,@PathVariable String docType) {
        docReviewService.approveDocument(orderId, docType, id, null);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/send-for-review/{id}")
    public ResponseEntity sendForReview(@PathVariable String id) {
        docReviewService.sendForReview(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/send-to-customer/{id}")
    public ResponseEntity sendToCustomer(@PathVariable String id) throws ServiceException {
        docReviewService.sendToCustomer(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/approve-and-send/{id}")
    public ResponseEntity approveAndSend(@PathVariable String id) {
        docReviewService.approveAndSend(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/send-for-revision/{id}")
    public ResponseEntity sendForRevision(@PathVariable String id) {
        docReviewService.sendForRevision(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }




}
