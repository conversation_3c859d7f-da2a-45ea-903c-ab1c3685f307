package com.mstack.keystone.controller.order;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin
@RequestMapping("/supplier-order")
public class SupplierOrderController {

    private final SupplierOrderService supplierOrderService;

    @Autowired
    public SupplierOrderController(SupplierOrderService supplierOrderService) {
        this.supplierOrderService = supplierOrderService;
    }

    @GetMapping
    public List<SupplierOrder> getAllNonDeletedSupplierOrders() {
        return supplierOrderService.getAllNonDeletedSupplierOrders();
    }

    @GetMapping("/{id}")
    public ResponseEntity<SupplierOrder> getSupplierOrderById(@PathVariable String id) {
        Optional<SupplierOrder> supplierOrder = supplierOrderService.getSupplierOrderById(id);
        return supplierOrder.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/customer-order-book/{id}")
    public ResponseEntity<List<SupplierOrder>> getSupplierOrdersByCustomerOrderBookId(@PathVariable String id) {
        return ResponseEntity.ok().body(supplierOrderService.getSupplierOrdersByCustomerOrderBookId(id));
    }

    @PostMapping
    public ResponseEntity<SupplierOrder> createSupplierQuotation(@RequestBody SupplierOrder supplierOrder) {
        SupplierOrder createdSupplierOrder = supplierOrderService.createSupplierOrder(supplierOrder);
        return ResponseEntity.ok(createdSupplierOrder);
    }

    @PutMapping("/{id}")
    public ResponseEntity<SupplierOrder> updateSupplierOrder(
        @PathVariable String id,
        @RequestBody SupplierOrder supplierOrder
    ) {
        SupplierOrder updatedSupplierOrder = supplierOrderService.updateSupplierOrder(id, supplierOrder);
        return ResponseEntity.ok(updatedSupplierOrder);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSupplierOrder(@PathVariable String id) {
        supplierOrderService.deleteSupplierOrder(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/search")
    public ResponseEntity<Page<SupplierOrder>> searchSupplierOrders(@RequestBody FilterRequest request) {
        Page<SupplierOrder> supplierOrderPage = supplierOrderService.filterSupplierOrders(request);
        return ResponseEntity.ok(supplierOrderPage);
    }

    @PostMapping("/validateOrderQuantity")
    public ResponseEntity<HashMap<String, Object>> validateOrderQuantity(@RequestBody HashMap<String, Object> body) throws ServiceException {
        String poNumber = (String) body.get("poNumber");
        String productId = (String) body.get("productId");
        // Using number type to handle both integer and decimal cases
        double productQuantity = ((Number) body.get("productQuantity")).doubleValue();
        String dispatchId = (String) body.getOrDefault("dispatchId", null);
        // quantity cannot be negative
        if (productQuantity <= 0) {
            throw new ServiceException("Product quantity can never be less then or equal to 0", 400);
        }
        return ResponseEntity.ok(supplierOrderService.validateProductQuantity(poNumber, productId, productQuantity, dispatchId));
    }

    // API FOR GETTING LINKED SUPPLIER ORDERS WHOSE SUPPLIER ORDER BOOK IS MAPPED WITH CUSTOMER ORDER BOOK FOR BATCH DETAILS
    @GetMapping("/linked-orders")
    public ResponseEntity<List<SupplierOrder>> getLinkedSupplierOrders(@RequestParam("customerOrderDispatchId") String customerOrderDispatchId) {
        return ResponseEntity.ok(supplierOrderService.getLinkedSupplierOrders(customerOrderDispatchId));
    }

}
