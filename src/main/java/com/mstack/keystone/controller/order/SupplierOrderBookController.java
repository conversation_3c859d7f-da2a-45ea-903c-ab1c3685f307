package com.mstack.keystone.controller.order;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.SupplierOrderPOValidationRequest;
import com.mstack.keystone.model.dto.SupplierOrderPOValidationResponse;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.service.order.interfaces.ISupplierOrderBook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/supplier-order-book")
public class SupplierOrderBookController {

    private final ISupplierOrderBook orderBookService;

    @Autowired
    public SupplierOrderBookController(ISupplierOrderBook orderBookService) {
        this.orderBookService = orderBookService;
    }

    @PostMapping
    public ResponseEntity<SupplierOrderBook> createOrderBook(@RequestBody SupplierOrderBook orderBook) {
        SupplierOrderBook createdOrderBook = orderBookService.createOrderBook(orderBook);
        return new ResponseEntity<>(createdOrderBook, HttpStatus.CREATED);
    }

    @GetMapping("/{orderBookId}")
    public ResponseEntity<SupplierOrderBook> getOrderBookById(@PathVariable String orderBookId) {
        SupplierOrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        return new ResponseEntity<>(orderBook, HttpStatus.OK);
    }

    @GetMapping("/customer-order-book/{orderBookId}")
    public ResponseEntity<List<SupplierOrderBook>> getOrderBookByCustomerPoNumberId(@PathVariable String orderBookId) {
        List<SupplierOrderBook> orderBookList = orderBookService.getOrderBooksByCustomerOrderBookNumber(orderBookId);
        return new ResponseEntity<>(orderBookList, HttpStatus.OK);
    }


    @PostMapping("/validatePO")
    public ResponseEntity<SupplierOrderPOValidationResponse> checkOrderBookByPONumber(@RequestBody SupplierOrderPOValidationRequest supplierOrderPOValidationRequest ) {
        SupplierOrderBook orderBook = orderBookService.getByPONumberAndCustomerId(
                supplierOrderPOValidationRequest.getPoNumber(),
                supplierOrderPOValidationRequest.getSupplierId()
        );
        SupplierOrderPOValidationResponse supplierOrderPOValidationResponse=orderBook!=null?new SupplierOrderPOValidationResponse(false):new SupplierOrderPOValidationResponse(true);
        return new ResponseEntity<>(supplierOrderPOValidationResponse,HttpStatus.OK);
    }

    @PutMapping("/{orderBookId}")
    public ResponseEntity<SupplierOrderBook> updateOrderBook(
        @PathVariable String orderBookId,
        @RequestBody SupplierOrderBook updatedOrderBook
    ) {
        SupplierOrderBook orderBook = orderBookService.updateOrderBook(orderBookId, updatedOrderBook);
        return new ResponseEntity<>(orderBook, HttpStatus.OK);
    }

    @DeleteMapping("/{orderBookId}")
    public ResponseEntity<Void> deleteOrderBook(@PathVariable String orderBookId) {
        orderBookService.deleteOrderBook(orderBookId);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<SupplierOrderBook>> searchCustomers(@RequestBody FilterRequest request) {
        Page<SupplierOrderBook> orderBookPage = orderBookService.filterOrderBook(request);
        return ResponseEntity.ok(orderBookPage);
    }

    @PutMapping("/update-po")
    public ResponseEntity<File> updatePO(@RequestBody File file, @RequestParam("orderBookId") String orderBookId) {
        return ResponseEntity.ok(orderBookService.updatePOFile(orderBookId, file));
    }

    @GetMapping("/get-supplierOrderBook-by-name")
    public ResponseEntity<List<Map<String, Object>>> getSuppliersByName(@RequestParam("name") String name,@RequestParam("customerName") String customerName) {
        List<Map<String, Object>> orderBookPage=orderBookService.getOrdersBySupplierOrCustomerName(name,customerName);
        return ResponseEntity.ok(orderBookPage);
    }
}
