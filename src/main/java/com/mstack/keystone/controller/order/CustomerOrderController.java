package com.mstack.keystone.controller.order;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.InvoiceNumberGenerationRequest;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import jakarta.validation.constraints.NotEmpty;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@CrossOrigin
@RequestMapping("/customer-order")
public class CustomerOrderController {

    @Autowired
    private CustomerOrderService customerOrderService;

    @Autowired
    private RequestConfig requestConfig;

    @GetMapping
    public List<CustomerOrder> getAllCustomerOrders() {
        return customerOrderService.getAllNonDeletedCustomerOrders();
    }

    @GetMapping("/{id}")
    public ResponseEntity<CustomerOrder> getCustomerOrderById(@PathVariable String id) {
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(id);
        return ResponseEntity.ok(customerOrder);
    }

    @GetMapping("orderBook/{id}")
    public ResponseEntity<List<CustomerOrder>> getCustomerOrderByOrderBookId(@PathVariable String id) {
        List<CustomerOrder> customerOrders = customerOrderService.getCustomerOrderByOrderBookId(id);
        return ResponseEntity.ok(customerOrders);
    }

    @PostMapping
    public ResponseEntity<CustomerOrder> createCustomerOrder(@RequestBody CustomerOrder customerOrder) {
        CustomerOrder createdCustomerOrder = customerOrderService.createCustomerOrder(customerOrder);
        return ResponseEntity.ok(createdCustomerOrder);
    }

    @PutMapping("/updateDraftDocumentByFile/{orderId}/{fileName}")
    public ResponseEntity<Void> updateDraftDocumentByFile(@PathVariable @NotEmpty String orderId, @NotEmpty @PathVariable String fileName, @RequestParam("file") MultipartFile file) {
        customerOrderService.updateDraftDocuments(orderId, fileName, file);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/updateDraftDocument/{orderId}/{fileName}")
    public ResponseEntity<Void> updateDraftDoucmentByFileId(@PathVariable @NotEmpty String orderId, @NotEmpty @PathVariable String fileName, @RequestBody HashMap<String, String> body) throws ServiceException {
        if (!body.containsKey("file"))
            throw new ServiceException("File id cannot be null", 400);
        customerOrderService.updateDraftDocuments(orderId, fileName, body.get("file"));
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}")
    public ResponseEntity<CustomerOrder> updateCustomerOrder(
        @PathVariable String id,
        @RequestBody CustomerOrder customerOrder
    ) {
        CustomerOrder updatedCustomerOrder = customerOrderService.updateCustomerOrder(id, customerOrder);
        return ResponseEntity.ok(updatedCustomerOrder);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomerOrder(@PathVariable String id) {
        customerOrderService.deleteCustomerOrder(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/search")
    public ResponseEntity<Page<CustomerOrder>> searchCustomerOrders(@RequestBody FilterRequest request) {
        Page<CustomerOrder> customerOrderPage = customerOrderService.filterCustomerOrders(request);
        return ResponseEntity.ok(customerOrderPage);
    }

    @GetMapping("/findByPoNumberOrOrderNumber")
    public ResponseEntity<Page<CustomerOrder>> findByPoNumberOrOrderNumber(@RequestParam String searchText, @RequestParam(required = false,defaultValue = "100") int size , @RequestParam(required = false,defaultValue = "0") int number ){
        Page<CustomerOrder> customerOrderPage = customerOrderService.findCustomerOrderByPoNumberOrOrderNumber(searchText.trim(), PageRequest.of(number,size));
        return ResponseEntity.ok(customerOrderPage);
    }

    @GetMapping("/findByPoNumber")
    public ResponseEntity<List<CustomerOrder>> findByPoNumberOrOrderNumber(@RequestParam String poNumber) {
        List<CustomerOrder> customerOrderPage = customerOrderService.findCustomerOrderByPoNumber(poNumber);
        return ResponseEntity.ok(customerOrderPage);
    }

    @GetMapping("/exportCompleteData")
    public ResponseEntity<Page<CustomerOrder>> exportCompleteData(){
        customerOrderService.exportCompleteData();
        return ResponseEntity.ok(null);
    }

    @GetMapping("/generateSummarySheet")
    @SneakyThrows
    public ResponseEntity generateSummarySheet() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(() -> {
            try {
                customerOrderService.generateDispatchOrderSummarySheet();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        executorService.shutdown();
        return ResponseEntity.ok(null);
    }

    @PostMapping("/validateOrderQuantity")
    public ResponseEntity<HashMap<String, Object>> validateOrderQuantity(@RequestBody HashMap<String, Object> body) throws ServiceException {
        String poNumber = (String) body.get("poNumber");
        String productId = (String) body.get("productId");
        String customerId = (String) body.get("customerId");
        String inventoryId = (String) body.get("inventoryId");
        // Using number type to handle both integer and decimal cases
        double productQuantity = ((Number) body.get("productQuantity")).doubleValue();
        String dispatchId = (String) body.getOrDefault("dispatchId", null);
        // quantity cannot be negative
        if (productQuantity <= 0) {
            throw new ServiceException("Product quantity can never be less then or equal to 0", 400);
        }
        return ResponseEntity.ok(customerOrderService.validateProductQuantity(poNumber, productId, productQuantity, dispatchId, customerId, inventoryId));
    }

    @PostMapping("/moveToInventory")
    public ResponseEntity moveToInventory(@RequestBody HashMap<String, Object> body) throws ServiceException {
        customerOrderService.moveToInventory(body);
        return ResponseEntity.ok(null);
    }

    @PostMapping("/generate-invoice-number")
    public ResponseEntity generateInvoiceNumber(@RequestBody InvoiceNumberGenerationRequest body) throws ServiceException {
        customerOrderService.generateInvoiceNumber(body);
        return ResponseEntity.ok(null);
    }

}
