package com.mstack.keystone.controller.accounting;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.invoice.ReportDateRange;
import com.mstack.keystone.model.repository.Accounting.Invoice;
import com.mstack.keystone.service.accounting.InvoiceService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

@RestController
@RequestMapping("/invoice")
public class InvoiceController {
    private final InvoiceService invoiceService;

    @Autowired
    RequestConfig requestConfig;

    @Autowired
    public InvoiceController(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }

    @PostMapping
    public ResponseEntity<Invoice> createInvoice(@RequestBody Invoice invoice) {
        Invoice createdInvoice = invoiceService.createInvoice(invoice);
        return new ResponseEntity<>(createdInvoice, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Invoice> getInvoice(@PathVariable String id) {
        Invoice createdInvoice = invoiceService.getInvoice(id);
        return new ResponseEntity<>(createdInvoice, HttpStatus.CREATED);
    }

    @PostMapping("/search")
    public ResponseEntity getByFilters(@RequestBody FilterRequest filterRequest) {
        Page invoices = invoiceService.getAllInvoices(filterRequest);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

    @PostMapping("/getReport")
    @SneakyThrows
    public ResponseEntity getReport(@RequestBody ReportDateRange body) {
        Date toDate = body.getTo();
        Date fromDate = body.getFrom();
        String entityId = requestConfig.getEntityId();
        Thread customThread = new Thread(() -> {
            invoiceService.createReport(fromDate, toDate, entityId);
        });
        customThread.start();
        return new ResponseEntity<>(null, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @SneakyThrows
    public ResponseEntity<Void> deleteInvoice(@PathVariable String id) {
        invoiceService.deleteInvoice(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    public static Date stringToDate(String dateString, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);

        try {
            return formatter.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

}
