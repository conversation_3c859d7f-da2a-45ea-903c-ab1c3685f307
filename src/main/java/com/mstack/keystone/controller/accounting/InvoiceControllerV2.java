package com.mstack.keystone.controller.accounting;

import com.mstack.keystone.model.repository.Accounting.OperationalInvoices;
import com.mstack.keystone.model.repository.Accounting.MarginAnalysisResponse;
import com.mstack.keystone.service.accounting.InvoiceServiceV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/invoice/v2")
public class InvoiceControllerV2 {
    private final InvoiceServiceV2 invoiceServiceV2;

    @Autowired
    public InvoiceControllerV2(InvoiceServiceV2 invoiceServiceV2) {
        this.invoiceServiceV2 = invoiceServiceV2;
    }

    @PostMapping("create")
    public ResponseEntity<OperationalInvoices> createInvoice(@RequestBody OperationalInvoices operationalInvoices) {
        OperationalInvoices createdInvoice = invoiceServiceV2.createInvoice(operationalInvoices);
        return new ResponseEntity<>(createdInvoice, HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<OperationalInvoices> getInvoice(@RequestParam String id) {
        OperationalInvoices operationalInvoices = invoiceServiceV2.getInvoiceByCustomerPurchcaseOrderId(id);
        return new ResponseEntity<>(operationalInvoices, HttpStatus.OK);
    }

    @GetMapping("/calculate")
    public ResponseEntity<MarginAnalysisResponse> calculateMargin(@RequestParam String poId) {
        MarginAnalysisResponse invoices = invoiceServiceV2.getCalculatedData(poId);
        return new ResponseEntity<>(invoices, HttpStatus.OK);
    }

}
