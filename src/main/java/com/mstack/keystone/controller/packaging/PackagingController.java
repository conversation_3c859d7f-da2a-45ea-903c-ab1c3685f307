package com.mstack.keystone.controller.packaging;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.repository.inventory.InventoryProduct;
import com.mstack.keystone.service.packaging.PackagingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/packaging")
public class PackagingController {

    @Autowired
    private PackagingService packagingService;

    @PostMapping
    public Packaging createPackaging(@RequestBody Packaging packaging) throws ServiceException {
        return packagingService.createPackaging(packaging);
    }

    @GetMapping
    public List<Packaging> getAllPackagingList() {
        return packagingService.getAllPackagingList();
    }

    @GetMapping("/{packagingId}")
    public Packaging getPackagingById(@PathVariable String packagingId) {
        return packagingService.getPackagingById(packagingId);
    }


    @PutMapping("/{packagingId}")
    public Packaging updatePackaging(@PathVariable String packagingId, @RequestBody Packaging packaging) {
        return packagingService.updatePackaging(packagingId, packaging);
    }

    @DeleteMapping("/{packagingId}")
    public void deletePackaging(@PathVariable String packagingId) {
        packagingService.deletePackaging(packagingId);
    }

    @PostMapping("/search")
    public ResponseEntity<Page<Packaging>> searchPackaging(@RequestBody FilterRequest request) {
        Page<Packaging> packagingPage = packagingService.filterByPackaging(request);
        return ResponseEntity.ok(packagingPage);
    }
}

