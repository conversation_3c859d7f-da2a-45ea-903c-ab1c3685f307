package com.mstack.keystone.controller.criticalPath;

import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.model.dto.ActivityDto;
import com.mstack.keystone.model.dto.DispatchOrderSummary;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.ActivitiesRequest;
import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.Audit;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.ActivityRepository;
import com.mstack.keystone.repository.AuditRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.order.SupplierOrderBookRepo;
import com.mstack.keystone.service.criticalPath.CriticalPathService;
import com.mstack.keystone.service.criticalPath.interfaces.ActivityService;
import com.mstack.keystone.exception.ServiceException;

import java.util.*;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/activities")
public class ActivityController {

    @Autowired
    @Lazy
    private ActivityService activityService;

    @Autowired
    OrderBookRepository orderBookRepository;

    @Autowired
    SupplierOrderBookRepo supplierOrderBookRepo;

    @Autowired
    CriticalPathService criticalPathService;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    AuditRepository auditRepository;


    private static final Map<String, Function<OrderBook, Date>> ORDER_BOOK_DUE_DATE_FIELDS = Map.of(
            "expectedDeliveryDate", orderBook ->
                    orderBook.getProducts().isEmpty() ? null
                            : orderBook.getProducts().get(0).getExpectedDeliveryDate(),

            "purchaseOrderDate", OrderBook::getPurchaseOrderDate
    );

    @GetMapping
    public List<Activity> getAllActivities() {
        return activityService.getAllActivities();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Activity> getActivityById(@PathVariable String id) {
        Activity activity = activityService.getActivityById(id);
        return ResponseEntity.ok().body(activity);
    }

    @PostMapping
    public ResponseEntity<Activity> createActivity(@RequestBody Activity activity) {
        Activity createdActivity = activityService.createActivity(activity);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdActivity);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Activity> updateActivity(@PathVariable String id, @RequestBody Activity activity) {
        Activity updatedActivity = activityService.updateActivity(id, activity);
        return ResponseEntity.status(HttpStatus.resolve(200)).body(updatedActivity);
    }

    @GetMapping("/my-activities")
    public ResponseEntity getActivitiesForUser() {
        Page<ActivityDto> activities = activityService.getActivitiesForUser();
        return ResponseEntity.ok().body(activities);
    }

    @PostMapping("/activity-summary/{id}")
    public ResponseEntity getActivitiesSummary(@PathVariable String id) {
        List<DispatchOrderSummary> summary=activityService.getOrderSummaryForCustomer(id);
        return ResponseEntity.ok().body(summary);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteActivity(@PathVariable String id) {
        activityService.deleteActivity(id);
        return ResponseEntity.noContent().build();
    }
    //    @PostMapping("updateStatus/{id}/{status}")
    //    public ResponseEntity<Activity> updateActivityStatus(@PathVariable String id, @PathVariable ActivityStatus status) {
    //        Activity createdActivity = activityService.updateActivityStatus(id, status);
    //        return ResponseEntity.status(HttpStatus.CREATED).body(createdActivity);
    //    }

    @PostMapping("/search")
    public ResponseEntity<Page<Activity>> filterActivities(
            @RequestBody FilterRequest filterRequest) {
        return ResponseEntity.ok().body(activityService.filterActivities(filterRequest));
    }

    @PostMapping("/create-activities")
    public ResponseEntity<List<Activity>> createActivities(@RequestBody ActivitiesRequest request) throws ServiceException {
        try {
            // Check if 'templateName' is "poFlow"
            if ("poFlow".equalsIgnoreCase(request.getTemplateName())) {
                // Retrieve the OrderBook by orderId
                List <OrderBook> optionalOrderBook = orderBookRepository.findByOrderBookId(request.getOrderId());

                // If the OrderBook is found, update it
                if (!optionalOrderBook.isEmpty()) {
                    OrderBook orderBook = optionalOrderBook.get(0);

                    // Update the category in OrderBook using the value from the request
                    orderBook.setCategory(request.getCategory());
                    OrderType orderType = OrderType.valueOf(request.getOrderType().toUpperCase());
                    orderBook.setOrderType(orderType);

                    // Save the updated OrderBook
                    orderBookRepository.save(orderBook);
                } else {
                    throw new ServiceException("OrderBook not found with ID: " + request.getOrderId(), 404);
                }
            }

//            if ("SUPPLIER_ORDER_BOOK".equalsIgnoreCase(request.getCategoryName())) {
//                // Fetch the Supplier OrderBook using the entityId
//                List <SupplierOrderBook> supplierOrderBook = supplierOrderBookRepo.findByCustomerOrderBookId(request.getEntityId());
//
//                // If the Supplier OrderBook is found, update it
//                if (!supplierOrderBook.isEmpty()) {
//                    SupplierOrderBook orderBook = supplierOrderBook.get(0);
//
//                    // Update the linkedCOBId field with the orderId
//                    orderBook.setLinkedCOBId(request.getOrderId());
//
//                    // Save the updated Supplier OrderBook
//                    supplierOrderBookRepo.save(orderBook);
//                } else {
//                    throw new ServiceException("Supplier OrderBook not found with ID: " + request.getEntityId(), 404);
//                }
//            }


            // Create ActivitiesRequest object for creating activities
            ActivitiesRequest activitiesRequest = new ActivitiesRequest().builder()
                    .orderId(request.getOrderId())
                    .categoryName(request.getCategoryName())
                    .entityId(request.getEntityId())
                    .templateName(request.getTemplateName())
                    .dueDateFields(request.getDueDateFields())
                    .entityName(request.getEntityName())
                    .secondaryId(request.getSecondaryId())
                    .orderType(request.getOrderType())
                    .category(request.getCategory())
                    .build();

            // Call the service to create or update tasks
            List<Activity> activities = criticalPathService.createOrUpdateTask(activitiesRequest);

            // Return response with created activities
            return ResponseEntity.status(HttpStatus.CREATED).body(activities);
        } catch (Exception e) {
            throw new ServiceException("Error creating activities: " + e.getMessage(), 500);
        }
    }
}