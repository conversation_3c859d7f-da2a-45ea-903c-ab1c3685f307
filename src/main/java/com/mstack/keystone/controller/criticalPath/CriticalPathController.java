package com.mstack.keystone.controller.criticalPath;

import com.mstack.keystone.model.dto.CriticalPath;
import com.mstack.keystone.model.dto.CriticalPathTemplate;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.service.criticalPath.CriticalPathService;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/critical-path")
public class CriticalPathController {

    @Autowired
    CriticalPathService criticalPathService;

    @PostMapping
    @SneakyThrows
    public ResponseEntity addCriticalPath(@RequestBody CriticalPath criticalPath) {
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @GetMapping("/activities/{entityId}")
    @SneakyThrows
    public ResponseEntity getCriticalPath(@PathVariable String entityId) {
        List<Activity> activities = criticalPathService.getCriticalPath(entityId);
        return ResponseEntity.status(HttpStatus.OK).body(activities);
    }

    @GetMapping("/getCriticalPathForm/{name}")
    @SneakyThrows
    public ResponseEntity<CriticalPathTemplate> getCriticalPathTemplate(@PathVariable String name) {
        CriticalPathTemplate variables = criticalPathService.getCriticalPathForm(name);
        return ResponseEntity.status(HttpStatus.OK).body(variables);
    }

    @PostMapping("/submitCriticalPathForm/{entityType}/{entityId}/{name}")
    @SneakyThrows
    public ResponseEntity<List<Activity>> submitCriticalPathForm(
            @PathVariable EntityType entityType,
            @PathVariable String entityId,
            @PathVariable String name,
            @RequestBody HashMap<String, Object> criticalPathForm
    ) {
        List<Activity> activities = criticalPathService.generateCriticalPath(
                entityId,
                entityType,
                name,
                criticalPathForm
        );
        return ResponseEntity.status(HttpStatus.OK).body(activities);
    }
//    @PostMapping("/updateCriticalPath")
//    @SneakyThrows
//    public ResponseEntity<List<Activity>> updateCriticalPath() {
//        criticalPathService.updateCriticalPath();
//        return ResponseEntity.status(HttpStatus.OK).body(null);
//    }

    @PutMapping("/submitCriticalPathForm/{entityType}/{entityId}/{name}")
    @SneakyThrows
    public ResponseEntity<List<Activity>> updateCriticalPathForm(
            @PathVariable EntityType entityType,
            @PathVariable String entityId,
            @PathVariable String name,
            @RequestBody HashMap<String, Object> criticalPathForm
    ) {
        List<Activity> activities = criticalPathService.updateCriticalPath(
                entityId,
                entityType,
                name,
                criticalPathForm
        );
        return ResponseEntity.status(HttpStatus.OK).body(activities);
    }

}
