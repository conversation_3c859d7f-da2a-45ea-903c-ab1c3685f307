package com.mstack.keystone.client;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class HttpClient {

    @Autowired
    RestTemplate restTemplate;

    public String makeHttpGetRequest(String url, HttpHeaders customHeaders) {
        HttpEntity<String> requestEntity = new HttpEntity<>(customHeaders);

        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody();
    }

    public String makeHttpPostRequest(String url, String requestBody, HttpHeaders customHeaders) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // Set the content type as needed
        headers.addAll(customHeaders);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class).getBody();
    }

    public String makeHttpPutRequest(String url, String requestBody, HttpHeaders customHeaders) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // Set the content type as needed
        headers.addAll(customHeaders);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        return restTemplate.exchange(url, HttpMethod.PUT, requestEntity, String.class).getBody();
    }
}
