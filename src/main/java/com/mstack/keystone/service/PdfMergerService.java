package com.mstack.keystone.service;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.service.document.DocumentService;
import com.mstack.keystone.utils.FileUtil;
import lombok.SneakyThrows;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Service
public class PdfMergerService {
    @Autowired
    DocumentService documentService;

    @Autowired
    FileUtil fileUtil;

    // creating new pdf from existing pdfs
    void mergePdf(List<File> inputFiles, String outputFilePath) throws IOException {
        PDFMergerUtility pdfMergerUtility = new PDFMergerUtility();
        pdfMergerUtility.setDestinationFileName(outputFilePath);
        inputFiles.forEach(file -> {
            try {
                pdfMergerUtility.addSource(file);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
        });
        pdfMergerUtility.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly().streamCache);
    }
    @SneakyThrows
    public com.mstack.keystone.model.dto.File mergeFiles(List<com.mstack.keystone.model.dto.File> files, HashMap<String, Object> meta) {
            // Download files (with conversion of images to PDF)
            List<File> documents = downloadFiles(files);
            // merge the files
            java.io.File tempFile = Files.createTempFile("temp", "").toFile();
            mergePdf(documents, tempFile.getAbsolutePath());
            // upload the merged file
            com.mstack.keystone.model.dto.File response = documentService.uploadFile(
                    meta,
                    fileUtil.convertFileToMultipart(tempFile, (String) meta.get("name"))
            );
            documents.forEach(file -> {
                if (file.exists())
                    file.delete();
            });
            if (tempFile.exists())
                tempFile.delete();
            return response;
        }

    @SneakyThrows
    private List<File> downloadFiles(List<com.mstack.keystone.model.dto.File> files)  {
        List<File> fileList = new ArrayList<>(); // This will store the final files to be returned
        List<String> supportedImageExtensions = Arrays.asList(".png", ".jpg", ".jpeg");


        // Download the first file, which is always a PDF, and calculate the page size
        com.mstack.keystone.model.dto.File firstFile = files.get(0);
        String signedUrlFromFileId = documentService.getSignedUrlFromFileId(firstFile.getFileId());
        java.io.File firstDownloadedFile = documentService.downloadFileFromSignedUrl(signedUrlFromFileId, firstFile.getName());



        // Calculate the inputPageSize from the first PDF file
        PDRectangle inputPageSize = fileUtil.getInputPageSize(firstDownloadedFile);

        // Add first file to the file list as it is (PDFs don't need conversion)
        fileList.add(firstDownloadedFile);

        // Process remaining files
        for (int i = 1; i < files.size(); i++) {
            com.mstack.keystone.model.dto.File filePath = files.get(i);
            String signedUrl = documentService.getSignedUrlFromFileId(filePath.getFileId());
            java.io.File downloadedFile = documentService.downloadFileFromSignedUrl(signedUrl, filePath.getName());

            if (supportedImageExtensions.stream().anyMatch(ext -> filePath.getName().toLowerCase().endsWith(ext))) {
                // Convert image files to PDF using the calculated inputPageSize
                File convertedFile = fileUtil.convertPngToPdf(downloadedFile, inputPageSize);
                fileList.add(convertedFile);
            } else {
                // Add PDF files directly to the list
                fileList.add(downloadedFile);
            }
        }

        return fileList;
    }
    // return pdf
}
