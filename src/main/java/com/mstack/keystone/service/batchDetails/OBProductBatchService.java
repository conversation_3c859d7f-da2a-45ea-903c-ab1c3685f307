package com.mstack.keystone.service.batchDetails;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.enums.BatchType;
import com.mstack.keystone.model.enums.OBBatchType;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.inventory.InventoryOutOrder;
import com.mstack.keystone.model.repository.inventory.OBProductBatch;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.repository.OBProductBatchRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OBProductBatchService {

    @Autowired
    private OBProductBatchRepository obProductBatchRepository;

    @Autowired
    private OrderBookRepository orderBookRepository;
    @Autowired
    private CustomerOrderRepository customerOrderRepository;
    @Autowired
    private MongoQueries mongoQueries;

    public OBProductBatch createBatch(OBProductBatch obProductBatch) {
        // Implementation to create and return the inventory
        return mongoQueries.saveEntity(obProductBatchRepository, obProductBatch);
    }

    public OBProductBatch getBatch(String batchId) {
        return mongoQueries.getEntity(obProductBatchRepository, batchId);
    }

    // get batch by supplier order book id
    public List<OBProductBatch> getBatchesBySOBId(String supplierOrderBookId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("supplierOrderBookId", List.of(supplierOrderBookId)),
                Map.entry("deleted", List.of(Boolean.FALSE))
        );
        return mongoQueries.findByFields(filters, null, OBProductBatch.class);
    }

    public List<OBProductBatch> getAllocatedBatchesBySOBIdAndProductId(String supplierOrderBookId, String productId,String packagingId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("supplierOrderBookId", List.of(supplierOrderBookId)),
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("type", List.of(OBBatchType.ALLOCATED.getValue())),
                Map.entry("deleted", List.of(Boolean.FALSE))
        );
        return mongoQueries.findByFields(filters, null, OBProductBatch.class);
    }

    public List<OBProductBatch> getAllocatedBatchesByOBIdAndProductIdAndPackagingId(String orderBookId, String productId, String packagingId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("customerOrderBookId", List.of(orderBookId)),
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("type", List.of(OBBatchType.ALLOCATED.getValue())),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(filters, null, OBProductBatch.class);
    }

    public List<OBProductBatch> getUnAllocatedBatchesByProductIdAndPackagingId(String productId, String packagingId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("type", List.of(OBBatchType.UNALLOCATED.getValue())),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(filters, null, OBProductBatch.class);
    }

    public OBProductBatch updateBatch(String batchId, OBProductBatch obProductBatch) {
        return mongoQueries.updateEntity(obProductBatchRepository, batchId, obProductBatch);
    }

    public void deleteBatch(String batchId) {
        mongoQueries.softDeleteById(obProductBatchRepository, batchId, OBProductBatch.class);
    }

    @SneakyThrows
    public void allocateBatch(String batchId, String orderBookId, double units) {
        System.out.println("allocating for " + orderBookId);
        OBProductBatch obProductBatch = obProductBatchRepository.findById(batchId).orElse(null);
        if (obProductBatch == null) {
            throw new ServiceException("Invalid batch id provided ", 400);
        }
        if (obProductBatch.getUnits() < units) throw new ServiceException("Invalid units provided ", 400);
        // if equal simply allocate
        if (obProductBatch.getUnits() == units) {
            obProductBatch.setCustomerOrderBookId(orderBookId);
            obProductBatch.setType(OBBatchType.ALLOCATED.getValue());
            mongoQueries.updateEntity(obProductBatchRepository, obProductBatch.getId(), obProductBatch);
        } else {
            // if more update the batch
            OBProductBatch updatedBatch = obProductBatch.getDeepClone();
            // set updated batch units
            updatedBatch.setUnits(units);
            updatedBatch.setCustomerOrderBookId(orderBookId);
            updatedBatch.setType(OBBatchType.ALLOCATED.getValue());
            // update original batch units
            obProductBatch.setUnits(obProductBatch.getUnits() - updatedBatch.getUnits());
            mongoQueries.updateEntity(obProductBatchRepository, obProductBatch.getId(), obProductBatch);
            mongoQueries.saveEntity(obProductBatchRepository, updatedBatch);
        }
    }

    @SneakyThrows
    public void unAllocateBatch(String batchId) {
        OBProductBatch obProductBatch = obProductBatchRepository.findById(batchId).orElse(null);
        if (obProductBatch == null) {
            throw new ServiceException("Invalid batch id provided ", 400);
        }
        OBProductBatch parentBatch = getUnAllocatedBatchesBySOBIdAndProductId(obProductBatch.getSupplierOrderBookId(), obProductBatch.getProductId(), obProductBatch.getPackagingId());
        if (parentBatch == null) {
            // make current batch unallocated
            obProductBatch.setCustomerOrderBookId(null);
            obProductBatch.setType(OBBatchType.UNALLOCATED.getValue());
            obProductBatchRepository.save(obProductBatch);
//            mongoQueries.updateEntity(obProductBatchRepository, obProductBatch.getId(), obProductBatch);
        } else {
            // if parent batch avaialble move quantity of current batch to parent
            parentBatch.setUnits(obProductBatch.getUnits() + parentBatch.getUnits());
            // mark current batch deleted
            obProductBatch.softDelete();
            obProductBatchRepository.save(obProductBatch);
            obProductBatchRepository.save(parentBatch);
        }

    }


    public List<OBProductBatch> getBatches(Map<String, List<Object>> filters) {
        return mongoQueries.findByFields(filters, null, OBProductBatch.class);
    }

    public OBProductBatch getUnAllocatedBatchesBySOBIdAndProductId(String supplierOrderBookId, String productId,String packagingId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("supplierOrderBookId", List.of(supplierOrderBookId)),
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("type", List.of(OBBatchType.UNALLOCATED.getValue())),
                Map.entry("deleted", List.of(Boolean.FALSE))
        );
        List<OBProductBatch> obProductBatches = mongoQueries.findByFields(filters, null, OBProductBatch.class);
        return obProductBatches != null && !obProductBatches.isEmpty() ? obProductBatches.get(0) : null;
    }


    public void canUnAllocateBatch(String batchId) throws ServiceException {
        OBProductBatch obProductBatch = obProductBatchRepository.findById(batchId).orElse(null);
        if (obProductBatch == null) {
            throw new ServiceException("Invalid batch id provided ", 400);
        }
        List<CustomerOrder> customerOrders = getBatchRelatedCustomerOrders(obProductBatch.getCustomerOrderBookId()).stream().filter(customerOrder -> {
            for (OrderedProduct orderedProduct : customerOrder.getProducts()) {
                if (orderedProduct.getProduct().getId().equals(obProductBatch.getProductId())) return true;
            }
            return false;
        }).toList();
        List<String> customerOrderIds = customerOrders.stream().map(CustomerOrder::getId).toList();
        if (customerOrderIds.isEmpty()) return;
        // get product batch related to ob batch
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("customerDispatchOrderId", List.copyOf(customerOrderIds)),
                Map.entry("deleted", List.of(false))
        );
        List<InventoryOutOrder> inventoryOutOrder = mongoQueries.findByFields(filters, null, InventoryOutOrder.class);
        if (inventoryOutOrder != null && !inventoryOutOrder.isEmpty()) {
            CustomerOrder customerOrder = customerOrderRepository.findById(inventoryOutOrder.get(0).getCustomerDispatchOrderId()).orElse(null);
            if (customerOrder == null)
                throw new ServiceException("No customer order found for out order " + inventoryOutOrder.get(0).getId(), 500);
            throw new ServiceException("Inventory order exists for " + customerOrder.getOrderId(), 400);
        }
    }

    private List<CustomerOrder> getBatchRelatedCustomerOrders(String customerOrderBookId) throws ServiceException {
        OrderBook orderBook = orderBookRepository.findById(customerOrderBookId).orElse(null);
        if (orderBook == null)
            throw new ServiceException("Not found order book for given id " + customerOrderBookId, 400);
        return mongoQueries.findByFields(new HashMap<>() {{
            put("purchaseOrderNumber", List.of(orderBook.getPurchaseOrderNumber()));
            put("customer._id", List.of(orderBook.getCustomer().getId()));
            put("deleted", List.of(false));
        }}, null, CustomerOrder.class);
    }
}
