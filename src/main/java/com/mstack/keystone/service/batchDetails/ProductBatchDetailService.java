package com.mstack.keystone.service.batchDetails;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.enums.BatchType;
import com.mstack.keystone.model.repository.inventory.*;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductBatchDetailService {
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;

    @Autowired
    SupplierOrderService supplierOrderService;

    @Autowired
    CustomerOrderService customerOrderService;

    @Autowired
    OrderBookRepository orderBookRepository;

    @Transactional
    @SneakyThrows
    public ProductBatchDetailRequestDto createProductBatchDetails(ProductBatchDetailRequestDto productBatchDetailRequestDto) {
        validateBatchDetails(productBatchDetailRequestDto);
        SupplierOrder order = supplierOrderService.getSupplierOrderById(productBatchDetailRequestDto.getSupplierDispatchOrderId()).orElse(null);
        if (order == null) throw new ServiceException("invalid supplier order id provided ", 400);

        HashMap<String, List<ProductBatchDetail>> map = new HashMap<>();
        for (String orderdProductId : productBatchDetailRequestDto.getProductBatchMap().keySet()) {
            OrderedProduct product = getProductDetails(orderdProductId, order);
            if (product == null)
                throw new ServiceException("couldn't find ordered product details for given product id " + orderdProductId, 400);
            List<ProductBatchDetail> batches = new ArrayList<>();
            CustomerOrder customerOrder=customerOrderService.getCustomerOrderById(product.getLinkedOrders().get(0).getOrderId());
            productBatchDetailRequestDto.getProductBatchMap().get(orderdProductId).forEach(productBatchDetail ->
                    {
                        productBatchDetail.setSupplierOrderDispatchId(productBatchDetailRequestDto.getSupplierDispatchOrderId());
                        // generate batch uid
                        productBatchDetail.setBatchUID(new ObjectId().toString());
                        if(customerOrder.getInventoryId()!=null){
                            productBatchDetail.setType(BatchType.UN_ALLOCATED.getValue());
                            productBatchDetail.setInventoryInOrderId(customerOrder.getId());
                        }
                        else {
                            productBatchDetail.setType(BatchType.ALLOCATED.getValue());
                            productBatchDetail.setCustomerOrderDispatchId(customerOrder.getId());
                        }
                        productBatchDetail.setPackagingId(product.getPackaging().getId());
                        productBatchDetail.setNetWeightPerUnit(product.getPerUnitWeight());
                        productBatchDetail.setProductId(product.getProduct().getId());
                        batches.add(mongoQueries.saveEntity(productBatchDetailRepository, productBatchDetail));
                    }
            );
            map.put(orderdProductId, batches);
        }
        productBatchDetailRequestDto.setProductBatchMap(map);
        return productBatchDetailRequestDto;
    }

    private OrderedProduct getProductDetails(String productId, SupplierOrder order) {
        for (OrderedProduct orderedProduct : order.getProducts()) {
            if (orderedProduct.getId().equals(productId)) return orderedProduct;
        }
        return null;
    }

    @SneakyThrows
    public ProductBatchDetailRequestDto getProductBatchDetails(String supplierOrderId) {
        ProductBatchDetailRequestDto res = new ProductBatchDetailRequestDto();
        SupplierOrder order = supplierOrderService.getSupplierOrderById(supplierOrderId).orElse(null);
        if (order == null) throw new ServiceException("Invalid id provided", 400);
        res.setSupplierDispatchOrderId(supplierOrderId);
        res.setProductBatchMap(new HashMap<>());
        for (OrderedProduct product : order.getProducts()) {
            List<ProductBatchDetail> productBatchDetails = getBatchesBySupplierOrderIdAndProductIdAndPackagingId(supplierOrderId, product.getProduct().getId(), product.getPackaging().getId());
            if (!productBatchDetails.isEmpty())
                res.getProductBatchMap().put(product.getId(), mergeBatchesByBatchUID(productBatchDetails));
        }
        return res;
    }

    private List<ProductBatchDetail> mergeBatchesByBatchUID(List<ProductBatchDetail> productBatchDetails) {
        HashMap<String, List<ProductBatchDetail>> batchMap = new HashMap<>();
        // map batches by batch uid
        productBatchDetails.forEach(productBatchDetail -> {
            List<ProductBatchDetail> list = batchMap.getOrDefault(productBatchDetail.getBatchUID(), new ArrayList<>());
            list.add(productBatchDetail);
            batchMap.put(productBatchDetail.getBatchUID(), list);
        });
        HashMap<String, ProductBatchDetail> mergedMap = new HashMap<>();
        // merge batches into single batch
        batchMap.forEach((batchid, batches) -> {
            mergedMap.put(batchid, getMergedBatch(batches));
        });
        // return merged batches as list
        return mergedMap.values().stream().toList();
    }

    private ProductBatchDetail getMergedBatch(List<ProductBatchDetail> batches) {
        if (batches == null || batches.isEmpty()) return null;
        ProductBatchDetail mergedBatch = batches.get(0).deepClone();
        double totalUnits = 0;
        for (ProductBatchDetail batchDetail : batches) totalUnits += batchDetail.getUnits();
        mergedBatch.setUnits(totalUnits);
        return mergedBatch;
    }

    private List<ProductBatchDetail> getBatchesBySupplierOrderIdAndProductIdAndPackagingId(String supplierOrderId, String productId, String packagingId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("type", List.of(BatchType.ALLOCATED.getValue(), BatchType.UN_ALLOCATED.getValue())),
                Map.entry("supplierOrderDispatchId", List.of(supplierOrderId)),
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
    }

    private List<ProductBatchDetail> getBatchDetail(Map<String, List<Object>> filters) {
        return mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
    }

    @SneakyThrows
    private void validateBatchDetails(ProductBatchDetailRequestDto productBatchDetailRequestDto) {
        SupplierOrder order = supplierOrderService.getSupplierOrderById(productBatchDetailRequestDto.getSupplierDispatchOrderId()).get();
        if (order == null) throw new ServiceException("Invalid order id provided ", 400);
        for (String productId : productBatchDetailRequestDto.getProductBatchMap().keySet()) {
            List<ProductBatchDetail> productBatchDetails = productBatchDetailRequestDto.getProductBatchMap().get(productId);
            double batchUnits = calcBatchUints(productBatchDetails);
            double orderProductUnits = getOrderProductUnits(productId, order);
            if (batchUnits != orderProductUnits)
                throw new ServiceException("Batches unit some doesn't match with ordered product units for productId " + productId, 400);
        }
    }

    private double getOrderProductUnits(String productId, SupplierOrder order) {
        double units = 0;
        for (OrderedProduct orderedProduct : order.getProducts()) {
            if (orderedProduct.getId().equals(productId)) return orderedProduct.getUnits();
        }
        return units;
    }

    private double calcBatchUints(List<ProductBatchDetail> productBatchDetails) {
        double totalUnits = 0;
        for (ProductBatchDetail batch : productBatchDetails)
            totalUnits += batch.getUnits();
        return totalUnits;
    }

    public ProductBatchDetail getProductBatchDetailById(String productBatchDetailId) {
        // Implementation to retrieve and return the productBatchDetail by ID
        return mongoQueries.getEntity(productBatchDetailRepository, productBatchDetailId);
    }

    public ProductBatchDetailRequestDto updateProductBatchDetails(ProductBatchDetailRequestDto productBatchDetailRequestDto) {
        //check if sum of list of units equal to unallocated units in supplier
        List<ProductBatchDetail> totalBatches=productBatchDetailRepository.findBatchesForSupplierDispatchOrder(productBatchDetailRequestDto.getSupplierDispatchOrderId());
        //validations
        checkForValidUnits(productBatchDetailRequestDto,totalBatches);

        //updations of existing batches
        updateBatchListForProduct(productBatchDetailRequestDto,totalBatches);
        // deletion of invalid batches
        deleteInvalidBatches(productBatchDetailRequestDto,totalBatches);
        return getProductBatchDetails(productBatchDetailRequestDto.getSupplierDispatchOrderId());
    }

    @SneakyThrows
    public void updateBatchListForProduct(ProductBatchDetailRequestDto productBatchDetailRequestDto,List<ProductBatchDetail> totalBatches){
        HashMap<String,OrderedProduct> orderedProductHashMap=new HashMap<>();
        SupplierOrder supplierOrder=supplierOrderService.getSupplierOrderById(productBatchDetailRequestDto.getSupplierDispatchOrderId()).get();
        for (OrderedProduct product:supplierOrder.getProducts()) {
            orderedProductHashMap.put(product.getId(), product);
        }
        for (String key:productBatchDetailRequestDto.getProductBatchMap().keySet()) {
            List<ProductBatchDetail> productBatchDetailList=productBatchDetailRequestDto.getProductBatchMap().get(key);
            for (ProductBatchDetail pb:productBatchDetailList) {
                if(pb.getBatchUID()!=null){
                    updateBatch(pb,totalBatches);
                }
                else{
                    OrderedProduct product = orderedProductHashMap.get(key);
                    pb.setSupplierOrderDispatchId(productBatchDetailRequestDto.getSupplierDispatchOrderId());
                    // generate batch uid
                    pb.setBatchUID(new ObjectId().toString());
                    CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(product.getLinkedOrders().get(0).getOrderId());
                    if (customerOrder.getInventoryId() != null) {
                        pb.setType(BatchType.UN_ALLOCATED.getValue());
                        pb.setInventoryInOrderId(customerOrder.getId());
                    } else {
                        pb.setType(BatchType.ALLOCATED.getValue());
                        pb.setCustomerOrderDispatchId(customerOrder.getId());
                    }
                    pb.setPackagingId(product.getPackaging().getId());
                    pb.setNetWeightPerUnit(product.getPerUnitWeight());
                    pb.setProductId(product.getProduct().getId());
                    createProductBatchDetails(pb);
                }
            }
        }
    }

    public void deleteInvalidBatches(ProductBatchDetailRequestDto productBatchDetailRequestDto,List<ProductBatchDetail> totalBatches){
        // get All unique batch Ids that are not in new batches
        List<ProductBatchDetail> newBatches = productBatchDetailRequestDto.getProductBatchMap().values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());;

        List<ProductBatchDetail> oldBatches =  totalBatches.stream()
                .filter(batch -> {
                    // if inventory order then
                    if (batch.getInventoryId() != null) {
                        return "UN_ALLOCATED".equals(batch.getType());
                    } else return batch.getType().equals("UN_ALLOCATED") || batch.getType().equals("ALLOCATED");
                })
                .collect(Collectors.toList());

        List<String> newUniqueBatchIds = newBatches.stream().map(ProductBatchDetail::getBatchUID).distinct().collect(Collectors.toList());
        List<String> olUniqueBatchIds = oldBatches.stream().map(ProductBatchDetail::getBatchUID).distinct().collect(Collectors.toList());
        List<String> missingBatchIds = olUniqueBatchIds.stream()
                .filter(batchId -> !newUniqueBatchIds.contains(batchId))
                .collect(Collectors.toList());
        List<ProductBatchDetail> productBatchDetailList=totalBatches.stream().filter(productBatchDetail -> missingBatchIds.contains(productBatchDetail.getBatchUID())).toList();
        deleteBatchDetails(productBatchDetailList);
    }

    public void deleteBatchDetails(List<ProductBatchDetail> productBatchDetailList){
        for (ProductBatchDetail pb:productBatchDetailList) {
            pb.setDeleted(true);
            productBatchDetailRepository.save(pb);
        }
    }

    private void createProductBatchDetails(ProductBatchDetail productBatchDetail){
        productBatchDetailRepository.save(productBatchDetail);
    }

    private void updateBatch(ProductBatchDetail productBatchDetail, List<ProductBatchDetail> totalBatches)
    {
        List<ProductBatchDetail> oldBatches=totalBatches.stream().filter(batch -> batch.getBatchUID()!=null && batch.getBatchUID().equals(productBatchDetail.getBatchUID())).toList();
        double totalUnits=oldBatches.stream().filter(batch->batch.getBatchUID().equals(productBatchDetail.getBatchUID())).mapToDouble(ProductBatchDetail::getUnits).sum();
        double allocatedUnits=oldBatches.stream().filter(batch->batch.getBatchUID().equals(productBatchDetail.getBatchUID())&&batch.getType().equals("ALLOCATED")).mapToDouble(ProductBatchDetail::getUnits).sum();
        ProductBatchDetail unAllocatedBatchDetail=null;
        if(totalUnits!=productBatchDetail.getUnits()){
            for (ProductBatchDetail pb:oldBatches) {
                if(pb.getType().equals("UN_ALLOCATED")){
                    unAllocatedBatchDetail=pb;
                    break;
                }
            }
            if(unAllocatedBatchDetail!=null){
                unAllocatedBatchDetail.setUnits(productBatchDetail.getUnits()-allocatedUnits);
                productBatchDetailRepository.save(unAllocatedBatchDetail);
            }
//            oldBatches.remove(unAllocatedBatchDetail);
        }
        for (ProductBatchDetail pb:oldBatches) {
            if(pb.equals(unAllocatedBatchDetail)){
                continue;
            }
            pb.setBatchNumber(productBatchDetail.getBatchNumber());
            pb.setContainerNumber(productBatchDetail.getContainerNumber());
            pb.setExpDate(productBatchDetail.getExpDate());
            pb.setNetWeightPerUnit(productBatchDetail.getNetWeightPerUnit());
            pb.setMfgDate(productBatchDetail.getMfgDate());
            pb.setUnits(productBatchDetail.getUnits());
            productBatchDetailRepository.save(pb);
        }
    }



    @SneakyThrows
    private void checkForValidUnits(ProductBatchDetailRequestDto productBatchDetailRequestDto,List<ProductBatchDetail> totalBatches){
        Map<String, Double> actualMap = new HashMap<>();
        Map<String, Double> virtualMap = new HashMap<>();
        //get dispatch order id
        String supplierDispatchId=productBatchDetailRequestDto.getSupplierDispatchOrderId();
        SupplierOrder supplierOrder=supplierOrderService.getSupplierOrderById(supplierDispatchId).get();
        String linkedCustomerId=supplierOrder.getProducts().get(0).getLinkedOrders().get(0).getOrderId();
        CustomerOrder customerOrder=customerOrderService.getCustomerOrderById(linkedCustomerId);
        if(customerOrder.getCustomer()!=null){
            return;
        }
        List<ProductBatchDetail> actualBatches=productBatchDetailRequestDto.getProductBatchMap().values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());;

        List<ProductBatchDetail> virtualBatches=     totalBatches.stream()
                .filter(batch -> "VIRTUAL_ALLOCATED".equals(batch.getType())||"VIRTUAL_UNALLOCATED".equals(batch.getType()))
                .collect(Collectors.toList());;

        // Populate map1
        for (ProductBatchDetail batch : actualBatches) {
            String key = batch.getProductId()+batch.getPackagingId();
            actualMap.put(key, actualMap.getOrDefault(key, 0.0) + batch.getUnits());
        }

        // Populate map2
        for (ProductBatchDetail batch : virtualBatches) {
            String key = batch.getProductId()+batch.getPackagingId();
            virtualMap.put(key, virtualMap.getOrDefault(key, 0.0) + batch.getUnits());
        }

        if(actualMap.size()!= virtualMap.size()){
            throw new ServiceException("Number of units in supplier order do not  match with total number of units of batches for all the products",500);
        }
//         Compare units for each key in both maps
        for (String key : actualMap.keySet()) {
            double units1 = actualMap.get(key);
            double units2 = virtualMap.getOrDefault(key, 0.0);
            if (units1 != units2) {
                throw new ServiceException("Number of units in supplier order do not  match with total number of units of batches for all the products",500);
            }
        }
        for (String key:productBatchDetailRequestDto.getProductBatchMap().keySet()) {
            List<ProductBatchDetail> productBatchDetailList=productBatchDetailRequestDto.getProductBatchMap().get(key);
            if(productBatchDetailList!=null&&productBatchDetailList.size()>0){
                String productId=productBatchDetailList.get(0).getProductId();
                String packagingId=productBatchDetailList.get(0).getPackagingId();
                List<ProductBatchDetail> oldBatches=totalBatches.stream()
                        .filter(productBatchDetail -> productBatchDetail.getProductId().equals(productId)
                        && productBatchDetail.getPackagingId().equals(packagingId)&&(productBatchDetail.getType().equals("ALLOCATED")||productBatchDetail.getType().equals("UN_ALLOCATED"))).collect(Collectors.toList());
                checkForEachProduct(productBatchDetailList,oldBatches);
            }
        }

    }

    @SneakyThrows
    private void checkForEachProduct(List<ProductBatchDetail> newBatches,List<ProductBatchDetail> oldBatches){
        List<String> newUniqueBatchIds = newBatches.stream().map(ProductBatchDetail::getBatchUID).distinct().collect(Collectors.toList());
        List<String> olUniqueBatchIds = oldBatches.stream().map(ProductBatchDetail::getBatchUID).distinct().collect(Collectors.toList());

        // get allocated batches which have been deleted
        List<String> missingBatchIds = olUniqueBatchIds.stream()
                .filter(batchId -> !newUniqueBatchIds.contains(batchId))
                .collect(Collectors.toList());

        List<ProductBatchDetail> filteredList = oldBatches.stream()
                .filter(batch -> missingBatchIds.contains(batch.getBatchUID()) && "ALLOCATED".equals(batch.getType()))
                .collect(Collectors.toList());
        if(filteredList!=null&& filteredList.size()>0){
            throw new ServiceException("A batch that has been allocated cannot be deleted",500);
        }

        //check for the batches if quantity reduced beyond allocated
        for (ProductBatchDetail productBatchDetail:newBatches) {
            if(productBatchDetail.getBatchUID()!=null){
                double allocatedUnits=oldBatches.stream().filter(batch->batch.getBatchUID().equals(productBatchDetail.getBatchUID())&&batch.getType().equals("ALLOCATED")).mapToDouble(ProductBatchDetail::getUnits).sum();
                if(productBatchDetail.getUnits()<allocatedUnits){
                    throw new ServiceException("Not possible to reduce batch units as these have been allocated",500);
                }
            }
        }
    }

    public void deleteProductBatchDetail(String productBatchDetailId) {
        // Implementation to delete the productBatchDetail by ID
        mongoQueries.softDeleteById(productBatchDetailRepository, productBatchDetailId, ProductBatchDetail.class);
    }

    public Page<ProductBatchDetail> filterByProductBatchDetail(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<ProductBatchDetail> page = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                ProductBatchDetail.class
        );
        return page;
    }

    public List<SDOProductBatchDto> getUnallocatedSupplierBatchesByCustomerOrderId(String orderId) {
        HashMap<String, SDOProductBatchDto> map = new HashMap<>();
        for (ProductBatchDetail productBatchDetail : getUnallocatedActualBatchesByCustomerOrderId(orderId)) {
            if (!map.containsKey(productBatchDetail.getSupplierOrderDispatchId())) {
                //CREATE NEW DTO
                SupplierOrder supplierOrder = supplierOrderService.getSupplierOrderById(productBatchDetail.getSupplierOrderDispatchId()).get();
                SDOProductBatchDto sdoProductBatchDto = new SDOProductBatchDto();
                sdoProductBatchDto.setSupplierOrderUID(supplierOrder.getId());
                sdoProductBatchDto.setOrderId(supplierOrder.getOrderId());
                if (supplierOrder.getSupplier() != null && supplierOrder.getSupplier().getName() != null)
                    sdoProductBatchDto.setSupplierName(supplierOrder.getSupplier().getName());
                sdoProductBatchDto.setProductBatchDetailList(new ArrayList<>());
                map.put(productBatchDetail.getSupplierOrderDispatchId(), sdoProductBatchDto);
            }
            map.get(productBatchDetail.getSupplierOrderDispatchId()).getProductBatchDetailList().add(productBatchDetail);
        }
        List<SDOProductBatchDto> list = new ArrayList<>();
        map.forEach((key, value) -> {
            list.add(value);
        });
        return list;
    }

    public List<ProductBatchDetail> getUnallocatedActualBatchesByCustomerOrderId(String orderId) {
        CustomerOrder order = customerOrderService.getCustomerOrderById(orderId);
        List<ProductBatchDetail> unallocatedBatches = new ArrayList<>();
        for (OrderedProduct orderedProduct : order.getProducts()) {
            List<String> supplierOrderIds = findVirtualBatchesRelatedToCustomerOrder(orderId).stream().map(ProductBatchDetail::getSupplierOrderDispatchId).toList();
            Map<String, List<Object>> filters = Map.ofEntries(
                    Map.entry("type", List.of(BatchType.UN_ALLOCATED)),
                    Map.entry("supplierOrderDispatchId", Arrays.asList(supplierOrderIds.toArray())),
                    Map.entry("productId", List.of(orderedProduct.getProduct().getId())),
                    Map.entry("packagingId", List.of(orderedProduct.getPackaging().getId())),
                    Map.entry("deleted", List.of(false))
            );
            List<ProductBatchDetail> batches = mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
            if (batches != null && !batches.isEmpty()) unallocatedBatches.addAll(batches);
        }
        return unallocatedBatches;
    }


    private List<ProductBatchDetail> findVirtualBatchesRelatedToCustomerOrder(String orderId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("customerDispatchOrderId", List.of(orderId))
//                Map.entry("deleted", List.of(Boolean.FALSE))
        );
        List<InventoryOutOrder> inventoryOutOrder = mongoQueries.findByFields(filters, null, InventoryOutOrder.class);
        List<String> outOrderIds = inventoryOutOrder.stream().map(InventoryOutOrder::getId).toList();
        filters = Map.ofEntries(
                Map.entry("type", List.of(BatchType.VIRTUAL_ALLOCATED)),
                Map.entry("inventoryOutOrderId", Arrays.asList(outOrderIds.toArray())),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
    }


    public List<SDOVirtualProductBatchDto> getVirtualBatchesByInventoryIdAndProductId(String productId, String inventoryId, String packagingId, String customerOrderDispatchId) {
        CustomerOrder order = customerOrderService.getCustomerOrderById(customerOrderDispatchId);
        OrderBook orderBook = orderBookRepository.findByPONumber(order.getPurchaseOrderNumber());
        // get all linked ob virtual batches
        List<OBProductBatch> obProductBatches = getOBProductBatches(orderBook.getId(), productId, packagingId, inventoryId);
        List<String> supplierOrderBookIds = obProductBatches.stream().map(OBProductBatch::getSupplierOrderBookId).toList();
        List<String> supplierDispatchOrderIds = supplierOrderBookIds.stream()
                .map(s -> {
                    return supplierOrderService.getAllSupplierOrdersBySOBId(s);
                })
                .flatMap(List::stream)
                .toList()
                .stream()
                .map(SupplierOrder::getId)
                .toList();
        List<SDOVirtualProductBatchDto> list = new ArrayList<>();
        List<ProductBatchDetail> productBatchDetails = new ArrayList<>();
        supplierDispatchOrderIds.forEach(dispatchId -> {
            List<ProductBatchDetail> batchList = productBatchDetailRepository.findVirtualBatches(productId, inventoryId, packagingId, BatchType.VIRTUAL_UNALLOCATED.getValue(), dispatchId);
            if (batchList != null && !batchList.isEmpty()) productBatchDetails.addAll(batchList);
        });
        for (ProductBatchDetail productBatchDetail : productBatchDetails) {
            SupplierOrder supplierOrder = supplierOrderService.getSupplierOrderById(productBatchDetail.getSupplierOrderDispatchId()).get();
            SDOVirtualProductBatchDto dto = getSdoVirtualProductBatchDto(productBatchDetail, supplierOrder);
            list.add(dto);
        }
        return list;
    }

    private static SDOVirtualProductBatchDto getSdoVirtualProductBatchDto(ProductBatchDetail productBatchDetail, SupplierOrder supplierOrder) {
        SDOVirtualProductBatchDto dto = new SDOVirtualProductBatchDto();
        dto.setProductId(productBatchDetail.getProductId());
        dto.setOrderId(supplierOrder.getOrderId());
        dto.setSupplierOrderUID(supplierOrder.getId());
        dto.setUnits(productBatchDetail.getUnits());
        if (supplierOrder.getSupplier() != null && supplierOrder.getSupplier().getName() != null)
            dto.setSupplierName(supplierOrder.getSupplier().getName());
        dto.setProductBatchId(productBatchDetail.getId());
        dto.setPackagingId(productBatchDetail.getPackagingId());
        return dto;
    }

    private List<OBProductBatch> getOBProductBatches(String orderBookId, String productId, String packagingId, String inventoryId) {
        return mongoQueries.findByFields(Map.ofEntries(
                Map.entry("type", List.of(BatchType.ALLOCATED.getValue())),
                Map.entry("customerOrderBookId", List.of(orderBookId)),
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("deleted", List.of(Boolean.FALSE))), null, OBProductBatch.class);
    }

    public void allocateVirutalBatches(InventoryOutOrder order, List<SDOVirtualProductBatchDto> sdoVirtualProductBatchDtos, InventoryOutOrderProduct inventoryOutOrderProduct) {
        for (SDOVirtualProductBatchDto batch : sdoVirtualProductBatchDtos) {
            String supplierOrderId = batch.getSupplierOrderUID();
            Map<String, List<Object>> filters = getUnAllocatedVritualBatchFilters(inventoryOutOrderProduct, supplierOrderId);
            // ASSUMPTION ONLY ONE VIRTAUL BATCH EXIST FOR A SUPPLIER PRODUCT PACKAGING COMBINATION
            ProductBatchDetail existingBatch = getBatchDetail(filters).get(0);
            allocateVirtualBatch(order.getId(), batch, existingBatch);
        }
    }

    public void unAllocateVirtualBatch(String batchId) throws ServiceException {
        ProductBatchDetail batchDetail = productBatchDetailRepository.findById(batchId).orElse(null);
        if (batchDetail == null || batchDetail.isDeleted())
            throw new ServiceException("Invalid batch id provided " + batchId, 400);
        // get parent batch if its null make current batch un-allocated
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("type", List.of(BatchType.VIRTUAL_UNALLOCATED.getValue())),
                Map.entry("supplierOrderDispatchId", List.of(batchDetail.getSupplierOrderDispatchId())),
                Map.entry("productId", List.of(batchDetail.getProductId())),
                Map.entry("packagingId", List.of(batchDetail.getPackagingId())),
                Map.entry("deleted", List.of(false))
        );
        // there should always be only one parent batch
        List<ProductBatchDetail> parentBatchList = mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
        if (parentBatchList == null || parentBatchList.isEmpty()) {
            // mark current batch as unallcoated
            batchDetail.setType(BatchType.VIRTUAL_UNALLOCATED.getValue());
            batchDetail.setCustomerOrderDispatchId(null);
            mongoQueries.updateEntity(productBatchDetailRepository, batchDetail.getId(), batchDetail);
        } else {
            // add quantity of current batch in parent batch
            ProductBatchDetail parentBatch = parentBatchList.get(0);
            parentBatch.setUnits(batchDetail.getUnits() + parentBatch.getUnits());
            mongoQueries.updateEntity(productBatchDetailRepository, parentBatch.getId(), parentBatch);
            // delete current batch
            batchDetail.softDelete();
            mongoQueries.updateEntity(productBatchDetailRepository, batchDetail.getId(), batchDetail);
        }

    }

    private static Map<String, List<Object>> getUnAllocatedVritualBatchFilters(InventoryOutOrderProduct inventoryOutOrderProduct, String supplierOrderId) {

        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("type", List.of(BatchType.VIRTUAL_UNALLOCATED.getValue())),
                Map.entry("supplierOrderDispatchId", List.of(supplierOrderId)),
                Map.entry("productId", List.of(inventoryOutOrderProduct.getProductId())),
                Map.entry("packagingId", List.of(inventoryOutOrderProduct.getPackagingId())),
                Map.entry("deleted", List.of(false))
        );
        return filters;
    }

    @SneakyThrows
    private void allocateVirtualBatch(String orderId, SDOVirtualProductBatchDto batch, ProductBatchDetail existingBatch) {
        // check for quantity
        if (existingBatch.getUnits() - batch.getUnits() < 0)
            throw new ServiceException("Invalid qty provided for batch", 400);
        if (existingBatch.getUnits() == batch.getUnits()) {
            // since new batch and existing are some so don't split
            existingBatch.setType(BatchType.VIRTUAL_ALLOCATED.getValue());
            existingBatch.setInventoryOutOrderId(orderId);
            mongoQueries.updateEntity(productBatchDetailRepository, existingBatch.getId(), existingBatch);
            batch.setProductBatchId(existingBatch.getId());
            return;
        }
        // create new batch from existing batch
        ProductBatchDetail updatedBatch = existingBatch.deepClone();
        updatedBatch.setUnits(batch.getUnits());
        updatedBatch.setId(null);
        // inventory out order id
        updatedBatch.setInventoryOutOrderId(orderId);
        updatedBatch.setType(BatchType.VIRTUAL_ALLOCATED.getValue());
        existingBatch.setUnits(existingBatch.getUnits() - batch.getUnits());
        mongoQueries.updateEntity(productBatchDetailRepository, existingBatch.getId(), existingBatch);
        mongoQueries.saveEntity(productBatchDetailRepository, updatedBatch);
        batch.setProductBatchId(updatedBatch.getId());
    }


    public void allocateBatches(String orderId, List<ProductBatchDetail> productBatchDetails) {
        productBatchDetails.forEach(productBatchDetail -> allocateBatch(orderId, productBatchDetail));
    }

    @SneakyThrows
    private void allocateBatch(String orderId, ProductBatchDetail updatedBatch) {
        CustomerOrder order = customerOrderService.getCustomerOrderById(orderId);
        if (order == null) throw new ServiceException("No order found for given order id " + orderId, 400);
        ProductBatchDetail currentBatch = productBatchDetailRepository.findById(updatedBatch.getId()).orElse(null);
        if (currentBatch == null || currentBatch.getUnits() - updatedBatch.getUnits() < 0)
            throw new ServiceException("Invalid batch detail provided ", 400);
        if (currentBatch.getUnits() == updatedBatch.getUnits()) {
            currentBatch.setInventoryId(order.getInventoryId());
            currentBatch.setCustomerOrderDispatchId(orderId);
            currentBatch.setType(BatchType.ALLOCATED.getValue());
            mongoQueries.updateEntity(productBatchDetailRepository, currentBatch.getId(), currentBatch);
            return;
        }
        // update quantity of existing batch
        currentBatch.setUnits(currentBatch.getUnits() - updatedBatch.getUnits());
        // create new batch for allocated quantity
        updatedBatch.setId(null);
        // set customer order id
        updatedBatch.setCustomerOrderDispatchId(orderId);
        updatedBatch.setInventoryId(order.getInventoryId());
        // set batch type
        updatedBatch.setType(BatchType.ALLOCATED.getValue());
        updatedBatch.setBatchUID(currentBatch.getBatchUID());
        mongoQueries.updateEntity(productBatchDetailRepository, currentBatch.getId(), currentBatch);
        mongoQueries.saveEntity(productBatchDetailRepository, updatedBatch);
    }

    public void resetBatches(String id){
        // Not possible for
        List<ProductBatchDetail> productBatchDetailList=productBatchDetailRepository.findAllocatedBatches(id);
        for (ProductBatchDetail productBatchDetail: productBatchDetailList) {
            // get unallocated for batch
            ProductBatchDetail unAllocatedBatch=productBatchDetailRepository.findUnAllocatedProduct(productBatchDetail.getBatchUID());
            if(unAllocatedBatch==null) {
                productBatchDetail.setType("UN_ALLOCATED");
                productBatchDetail.setCustomerOrderDispatchId(null);
            }
            else {
                productBatchDetail.setDeleted(true);
                unAllocatedBatch.setUnits(productBatchDetail.getUnits()+unAllocatedBatch.getUnits());
                productBatchDetailRepository.save(unAllocatedBatch);
            }
            productBatchDetailRepository.save(productBatchDetail);
        }
    }

    public List<SDOProductBatchDto> getallocatedSupplierBatchesByCustomerOrderId(String orderId) {
        HashMap<String, SDOProductBatchDto> map = new HashMap<>();
        for (ProductBatchDetail productBatchDetail : getallocatedActualBatchesByCustomerOrderId(orderId)) {
            if (!map.containsKey(productBatchDetail.getSupplierOrderDispatchId())) {
                //CREATE NEW DTO
                SupplierOrder supplierOrder = supplierOrderService.getSupplierOrderById(productBatchDetail.getSupplierOrderDispatchId()).get();
                SDOProductBatchDto sdoProductBatchDto = new SDOProductBatchDto();
                sdoProductBatchDto.setSupplierOrderUID(supplierOrder.getId());
                sdoProductBatchDto.setOrderId(supplierOrder.getOrderId());
                if (supplierOrder.getSupplier() != null && supplierOrder.getSupplier().getName() != null)
                    sdoProductBatchDto.setSupplierName(supplierOrder.getSupplier().getName());
                sdoProductBatchDto.setProductBatchDetailList(new ArrayList<>());
                map.put(productBatchDetail.getSupplierOrderDispatchId(), sdoProductBatchDto);
            }
            map.get(productBatchDetail.getSupplierOrderDispatchId()).getProductBatchDetailList().add(productBatchDetail);
        }
        List<SDOProductBatchDto> list = new ArrayList<>();
        map.forEach((key, value) -> {
            list.add(value);
        });
        return list;
    }

    private List<ProductBatchDetail> getallocatedActualBatchesByCustomerOrderId(String orderId) {
        CustomerOrder order = customerOrderService.getCustomerOrderById(orderId);
        List<ProductBatchDetail> allocatedBatches = new ArrayList<>();
            Map<String, List<Object>> filters = Map.ofEntries(
                    Map.entry("customerOrderDispatchId", List.of(order.getId())),
                    Map.entry("type", List.of(BatchType.ALLOCATED)),
                    Map.entry("deleted", List.of(false))
            );
            List<ProductBatchDetail> batches = mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
            if (batches != null && !batches.isEmpty()) allocatedBatches.addAll(batches);
        return allocatedBatches;
    }
}
