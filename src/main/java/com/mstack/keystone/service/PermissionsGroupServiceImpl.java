package com.mstack.keystone.service;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.model.repository.PermissionsGroup;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.repository.PermissionsGroupRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.service.interfaces.IPermissionsGroupService;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PermissionsGroupServiceImpl implements IPermissionsGroupService {

    @Autowired
    PermissionsGroupRepository permissionsGroupRepository;
    @Autowired
    RequestConfig requestConfig;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    DashboardConfigServiceImpl dashboardConfigService;

    @Override
    @SneakyThrows
    public PermissionsGroup createPermissionsGroup(PermissionsGroup permissionsGroup) {
        if(permissionsGroup==null){
            throw new ServiceException("Failed to create , permssionsGroup object cannot be null ", 400);
        }
        if (permissionsGroup.getName().isBlank())
            throw new ServiceException("Failed to create, permission group cannot be blank ", 400);
        if(permissionsGroupRepository.findByPermissionsGroupName(permissionsGroup.getName())!=null)
            throw new ServiceException("Failed to create , permssionsGroup with permission name "+permissionsGroup.getName()+" already exists . Please try with different permission name ",400);
        // generate object id
        permissionsGroup.setId(new ObjectId().toString());
        permissionsGroup.setCreatedAt(new Date());
        permissionsGroup.setCreatedBy(requestConfig.getEntityId());
        return permissionsGroupRepository.save(permissionsGroup);
    }

    @Override
    @SneakyThrows
    public PermissionsGroup getPermissionsGroup(String groupIdOrGroupName) {
        Optional<PermissionsGroup> permissionsGroup = permissionsGroupRepository.findById(groupIdOrGroupName);
        permissionsGroup= Optional.ofNullable(permissionsGroup.orElse(permissionsGroupRepository.findByPermissionsGroupName(groupIdOrGroupName)));
        return permissionsGroup.orElse(null);
    }

    @Override
    @SneakyThrows
    public PermissionsGroup updatePermissionsGroup(String groupId, PermissionsGroup permissionsGroup) {
        if (permissionsGroup == null)
            throw new ServiceException("Failed to update , permissions group object cannot be null ", 400);
        PermissionsGroup existingGroup = getPermissionsGroup(groupId);
        if (existingGroup == null) {
            throw new ServiceException("Failed to update , no object exists with group id: " + groupId, 400);
        }
        existingGroup.setPermissions(permissionsGroup.getPermissions());
        existingGroup.setDescription(permissionsGroup.getDescription());
        existingGroup.setName(permissionsGroup.getName());
        existingGroup.setLastUpdatedAt(new Date());
        existingGroup.setLastUpdatedBy(requestConfig.getEntityId());
        return permissionsGroupRepository.save(existingGroup);
    }

    @Override
    @SneakyThrows
    public void deletePermissionsGroup(String groupId) {
        PermissionsGroup existingGroup = getPermissionsGroup(groupId);
        if (existingGroup != null) {
            validateDeleteRequest(existingGroup);
            permissionsGroupRepository.deleteById(groupId);
            System.out.println("permssions group with groupId: " + groupId + " sucesfully deleted");
        } else throw new ServiceException("Failed to delete , given groupId " + groupId + " doesnot exists", 400);
    }

    @Override
    public List<PermissionsGroup> getAllGroups() {
        List<PermissionsGroup> permissionsGroups = getNonDeletedPermissions();
        permissionsGroups.forEach(this::updateUserCount);
        return permissionsGroups;
    }

    @Override
    public DashBoardConfig getAllPermissionConfig() {
        return dashboardConfigService.getDashboardConfigByConfigName("permissionConfig");
    }

    private void updateUserCount(PermissionsGroup permissionsGroup) {
        List<User> userList = getUsersForRole(permissionsGroup);
        if (permissionsGroup.getMeta() == null) permissionsGroup.setMeta(new HashMap<>());
        permissionsGroup.getMeta().put("userCount", userList.size());
    }

    private List<PermissionsGroup> getNonDeletedPermissions() {
        return permissionsGroupRepository.findAll().stream().filter(permissionsGroup -> !permissionsGroup.isDeleted()).collect(Collectors.toList());
    }

    private void validateDeleteRequest(PermissionsGroup existingGroup) throws ServiceException {
        List<User> userList = getUsersForRole(existingGroup);
        if (!userList.isEmpty())
            throw new ServiceException("Group is already assigned to users ", 400);
    }

    private List<User> getUsersForRole(PermissionsGroup existingGroup) {
        return mongoQueries.findByFields(
                Map.ofEntries(
                        Map.entry("permissionsGroups", List.of(existingGroup.getName()))
                ), null, User.class).stream().filter(user -> !user.isDeleted()).collect(Collectors.toList());
    }
}
