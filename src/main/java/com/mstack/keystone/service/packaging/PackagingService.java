package com.mstack.keystone.service.packaging;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.inventory.InventoryOutOrder;
import com.mstack.keystone.model.repository.inventory.InventoryProduct;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.utils.CommonUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PackagingService {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    PackagingRepository packagingRepository;

    public Packaging createPackaging(Packaging packaging) throws ServiceException {
        packaging.setPackSize(packaging.getPSize() + " " + commonUtils.getAbbreviation(UnitOfMeasure.valueOf(packaging.getPUom())));
        packaging.setTareWeight(packaging.getTWeight() + " " + commonUtils.getAbbreviation(UnitOfMeasure.valueOf(packaging.getTWeightUom())));
        packaging.setPackagingName(packaging.getType().toUpperCase() + "_" + packaging.getPackSize().toUpperCase() + "_" + packaging.getTareWeight().toUpperCase());
        validateCreateRequest(packaging);
        // Implementation to create and return the packaging
        return mongoQueries.saveEntity(packagingRepository, packaging);
    }

    private void validateCreateRequest(Packaging packaging) throws ServiceException {
        List<Packaging> packagings = mongoQueries.findByFields(
                Map.ofEntries(
                        Map.entry("type", List.of(packaging.getType())),
                        Map.entry("packSize", List.of(packaging.getPackSize())),
                        Map.entry("tareWeight", List.of(packaging.getTareWeight())),
                        Map.entry("deleted", List.of(false))
                )
                , null, Packaging.class);
        if (!packagings.isEmpty()) {
            throw new ServiceException("Packaging already exists for given combination with name: " + packagings.get(0).getPackagingName(), 400);
        }
    }

    public Packaging getPackagingById(String packagingId) {
        // Implementation to retrieve and return the packaging by ID
        return mongoQueries.getEntity(packagingRepository, packagingId);
    }

    public Packaging updatePackaging(String packagingId, Packaging packaging) {
        // Implementation to update and return the updated packaging
        return mongoQueries.updateEntity(packagingRepository, packagingId, packaging);
    }

    public Page<Packaging> filterByPackaging(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<Packaging> page = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                Packaging.class
        );
        return page;
    }

    @SneakyThrows
    public void deletePackaging(String packagingId) {
        validateDeleteRequest(packagingId);
        // Implementation to delete the packaging by ID
        mongoQueries.softDeleteById(packagingRepository, packagingId, Packaging.class);
    }

    private void validateDeleteRequest(String packagingId) throws ServiceException {
        List<OrderBook> orderBooks = getOrderBookByPackagingId(packagingId);
        List<SupplierOrderBook> supplierOrderBooks = getSupplierOrderBookByPackagingId(packagingId);
        List<InventoryOutOrder> inventoryOutOrders = getInventoryOutOrdersByPackagingId(packagingId);
        if (inventoryOutOrders != null && !inventoryOutOrders.isEmpty())
            throw new ServiceException("Inventory out order still contain this packaging", 422);
        if (orderBooks != null && !orderBooks.isEmpty())
            throw new ServiceException("Order Book still contain this packaging ", 422);
        if (supplierOrderBooks != null && !supplierOrderBooks.isEmpty())
            throw new ServiceException("Supplier Order Book still contain this packaging ", 422);
    }

    private List<InventoryOutOrder> getInventoryOutOrdersByPackagingId(String packagingId) {
        Map<String, List<Object>> query = Map.ofEntries(
                Map.entry("products.packagingId", List.of(packagingId)),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(query, null, InventoryOutOrder.class);
    }

    private List<SupplierOrderBook> getSupplierOrderBookByPackagingId(String packagingId) {
        Map<String, List<Object>> query = Map.ofEntries(
                Map.entry("products.packaging._id", List.of(packagingId)),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(query, null, SupplierOrderBook.class);
    }

    private List<OrderBook> getOrderBookByPackagingId(String packagingId) {
        Map<String, List<Object>> query = Map.ofEntries(
                Map.entry("products.packaging._id", List.of(packagingId)),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(query, null, OrderBook.class);
    }

    public List<Packaging> getAllPackagingList() {
        return packagingRepository.getAllPackagingList();
    }
}

