package com.mstack.keystone.service.document;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.config.UrlConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.DocumentUploadResponse;
import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.SignedUrlResponse;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.util.*;

@Service
public class DocumentService {
    @Autowired
    UrlConfig urlConfig;
    @Autowired
    RequestConfig requestConfig;

    @SneakyThrows
    public File uploadFile(HashMap<String, Object> meta, MultipartFile file) {
        RestTemplate restTemplate = new RestTemplate();
        // Create the file as a FileSystemResource
        FileSystemResource fileResource;
        java.io.File tempFile = Files.createTempFile("temp", "").toFile();
        file.transferTo(tempFile);
        fileResource = new FileSystemResource(tempFile);
        HttpHeaders headers = new HttpHeaders();
        headers.put("entityId", Collections.singletonList(requestConfig.getEntityId()));
        headers.put("entityType", Collections.singletonList(requestConfig.getEntityType()));
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", fileResource);
        body.add("fileDto", meta);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<DocumentUploadResponse> response = restTemplate.postForEntity(urlConfig.getDocumentUrl() + "/storage/file/upload", requestEntity, DocumentUploadResponse.class);
        File fileData = new File();
        fileData.setFileId(response.getBody().getId());
        fileData.setName(response.getBody().getName());
        // delete the file
        fileResource.getFile().delete();
        return fileData;
    }

    @SneakyThrows
    public File uploadFileWitEntityDetails(HashMap<String, Object> meta, MultipartFile file, String entityType, String entityId) {
        RestTemplate restTemplate = new RestTemplate();
        // Create the file as a FileSystemResource
        FileSystemResource fileResource;
        java.io.File tempFile = Files.createTempFile("temp", "").toFile();
        file.transferTo(tempFile);
        fileResource = new FileSystemResource(tempFile);
        HttpHeaders headers = new HttpHeaders();
        headers.put("entityId", Collections.singletonList(entityId));
        headers.put("entityType", Collections.singletonList(entityType));
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", fileResource);
        body.add("fileDto", meta);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<DocumentUploadResponse> response = restTemplate.postForEntity(urlConfig.getDocumentUrl() + "/storage/file/upload", requestEntity, DocumentUploadResponse.class);
        File fileData = new File();
        fileData.setFileId(response.getBody().getId());
        fileData.setName(response.getBody().getName());
        // delete the file
        fileResource.getFile().delete();
        return fileData;
    }


    public java.io.File downloadFileViaSignedUrl(String id, String fileName) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.put("entityId", Collections.singletonList("KEYSTONE"));
        headers.put("entityType", Collections.singletonList("KEYSTONE"));
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(null, headers);
        ResponseEntity<SignedUrlResponse> response = null;
        try {
            response = restTemplate.getForEntity(urlConfig.getDocumentUrl() + "/storage/file/getSignedUrl/" + id, SignedUrlResponse.class, requestEntity);
        } catch (Exception e) {
            System.out.println("error occured while fetching file details " + e.getMessage());
            // return null if unable to download file
            return null;
        }
        return downloadFileFromSignedUrl(Objects.requireNonNull(response.getBody()).getUrl(), fileName);
    }

    @SneakyThrows
    public String getSignedUrlFromFileId(String id) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.put("entityId", Collections.singletonList("KEYSTONE"));
        headers.put("entityType", Collections.singletonList("KEYSTONE"));
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(null, headers);
        ResponseEntity<SignedUrlResponse> response = null;
        String docEngineUrl =  System.getenv("DOCUMENT_BASE_URL") != null ? System.getenv("DOCUMENT_BASE_URL") : urlConfig.getDocumentUrl();
        try {
            response = restTemplate.getForEntity(docEngineUrl + "/storage/file/getSignedUrl/" + id, SignedUrlResponse.class, requestEntity);
            return response.getBody().getUrl();
        } catch (Exception e) {
            System.out.println("error occured while fetching file details " + e.getMessage());
            // return null if unable to download file
            throw new ServiceException(
                    "No such file is there with id " + id,
                    HttpStatus.BAD_REQUEST.value()

            );
        }
    }

    @SneakyThrows
    public java.io.File downloadFileFromSignedUrl(String signedUrl, String fileName) {
        URL url = new URL(signedUrl);
        URLConnection connection = url.openConnection();
        java.io.File tempFile = new java.io.File(fileName);
        // if exists it will not be overwritten
        tempFile.createNewFile();
        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        return tempFile;
    }

}
