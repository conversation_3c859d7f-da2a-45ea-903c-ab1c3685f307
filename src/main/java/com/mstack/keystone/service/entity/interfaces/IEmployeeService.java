package com.mstack.keystone.service.entity.interfaces;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.EmployeeUserDetailRequest;
import com.mstack.keystone.model.dto.EmployeeUserDetailResponse;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Employee;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

public interface IEmployeeService {
    Employee createEmployee(Employee employee);

    void createEmployee(EmployeeUserDetailRequest employeeUserDetailRequest) throws JsonProcessingException;
    Employee updateEmployee(String id, Employee employee);
    Employee getEmployee(String id);
    boolean deleteEmployee(String id);
    Page<Employee> filterEmployees(FilterRequest filterRequest);

    List<EmployeeUserDetailResponse> getEmployeeUserDetails() throws ServiceException;

    EmployeeUserDetailResponse updateEmployeeUserDetail(String id, EmployeeUserDetailRequest employeeUserDetailRequest) throws ServiceException, JsonProcessingException;

    void deleteEmployeeUserDetail(String id) throws ServiceException;

    EmployeeUserDetailResponse getEmployeeUserDetailsById(String id) throws ServiceException;

    void resetPassword(EmployeeUserDetailRequest employeeUserDetailRequest) throws ServiceException;

}
