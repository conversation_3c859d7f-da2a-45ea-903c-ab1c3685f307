package com.mstack.keystone.service.entity.interfaces;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Customer;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

public interface ICustomerService {
    Customer saveCustomer(Customer customer);
    Page<Customer> filterCustomers(FilterRequest request);
    Customer getCustomerById(String id);
    Customer updateCustomer(String id, Customer newCustomerValues) throws IllegalAccessException;

    void deleteCustomer(String id) throws ServiceException;

    void resetPassword(String id, String password) throws ServiceException;

}
