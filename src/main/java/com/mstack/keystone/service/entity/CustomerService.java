package com.mstack.keystone.service.entity;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.Address;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.model.repository.enquiry.Enquiry;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.user.CustomerRepository;
import com.mstack.keystone.repository.user.UserRepository;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.enquiry.interfaces.IEnquiryService;
import com.mstack.keystone.service.entity.interfaces.ICustomerService;
import com.mstack.keystone.service.order.interfaces.IOrderBookService;
import com.mstack.keystone.utils.CommonUtils;

import java.util.*;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import static com.mstack.keystone.constants.AppConstants.ANALYTICS_GROUP_MAIL_ID;

@Service
public class CustomerService implements ICustomerService {

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    IEnquiryService enquiryService;

    @Autowired
    IOrderBookService orderBookService;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    BCryptPasswordEncoder bCryptPasswordEncoder;

    @Autowired
    UserRepository userRepository;

    @Autowired
    AwsSesService awsSesService;
    @Value("${spring.profiles.active}")
    String envType;


    @Override
    @SneakyThrows
    public Customer saveCustomer(Customer customer) {
        // validate request
        if (customer.getEmail() == null || customer.getEmail().isBlank())
            throw new ServiceException("Customer email cannot be empty ", 400);
        List<User> existingUserList = userRepository.findByUserName(getPrimaryEmail(customer));
        if (existingUserList != null && !existingUserList.isEmpty()) {
            throw new ServiceException("Email id is already registered with another user ", 400);
        }
        customer.setCustomerId(commonUtils.CreateID("CS", mongoQueries.getCount(customerRepository)));
        customer = mongoQueries.saveEntity(customerRepository, customer);

        // Create user with the determined primary email
        commonUtils.createUser(getPrimaryEmail(customer), "CUSTOMER", customer.getName(), customer.getId());
        generateAlert(customer);
        return customer;
    }

    private void generateAlert(Customer customer) {
        if (customer == null) return;
        String sendersAddress = envType.equalsIgnoreCase("dev") || envType.equalsIgnoreCase("local") ? "<EMAIL>" : "<EMAIL>,<EMAIL>,<EMAIL>";
        try {
            awsSesService.sendEmail(
                    "<EMAIL>",
                    sendersAddress,
                    "Alert new customer with name " + customer.getName() + " has been added in the system ",
                    generateCustomerDetailsHtml(customer), null
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }
    }


    public static String generateCustomerDetailsHtml(Customer customer) {
        StringBuilder htmlBuilder = new StringBuilder();
        // Begin HTML structure
        htmlBuilder.append("<!DOCTYPE html>\n")
                .append("<html lang=\"en\">\n")
                .append("<head>\n")
                .append("    <meta charset=\"UTF-8\">\n")
                .append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n")
                .append("    <title>Customer Details</title>\n")
                .append("    <style>\n")
                .append("        body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 20px; color: #333; }\n")
                .append("        .container { max-width: 800px; margin: 0 auto; background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }\n")
                .append("        h2 { text-align: center; color: #333; margin-bottom: 20px; }\n")
                .append("        table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n")
                .append("        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }\n")
                .append("        th { background-color: #f9f9f9; color: #333; }\n")
                .append("        td { color: #555; }\n")
                .append("        .highlight { background-color: #f9f9f9; }\n")
                .append("        .categories { padding: 6px; background-color: #e0e0e0; border-radius: 4px; display: inline-block; margin-right: 5px; margin-bottom: 5px; color: #333; }\n")
                .append("    </style>\n")
                .append("</head>\n")
                .append("<body>\n")
                .append("<div class=\"container\">\n")
                .append("    <h2>Customer Details</h2>\n")
                .append("    <table>\n");

        // Add rows for each key-value pair from Customer object with null checks
        htmlBuilder.append("        <tr>\n")
                .append("            <th>Customer ID</th>\n")
                .append("            <td>").append(customer.getCustomerId() != null ? customer.getCustomerId() : "N/A").append("</td>\n")
                .append("        </tr>\n")
                .append("        <tr class=\"highlight\">\n")
                .append("            <th>Name</th>\n")
                .append("            <td>").append(customer.getName() != null ? customer.getName() : "N/A").append("</td>\n")
                .append("        </tr>\n");

        // Address handling with null checks
        if (customer.getAddress() != null) {
            Address address = customer.getAddress();
            String street = address.getStreet() != null ? address.getStreet() : "N/A";
            String city = address.getCity() != null ? address.getCity() : "N/A";
            String state = address.getState() != null ? address.getState() : "N/A";
            String postalCode = address.getPostalCode() != null ? address.getPostalCode() : "N/A";
            String country = address.getCountry() != null ? address.getCountry() : "N/A";

            htmlBuilder.append("        <tr>\n")
                    .append("            <th>Address (Street,City,State,Pincode,Country) </th>\n")
                    .append("            <td>").append(street).append(", ").append(city).append(", ").append(state)
                    .append(", ").append(postalCode).append(", ").append(country).append("</td>\n")
                    .append("        </tr>\n");
        } else {
            htmlBuilder.append("        <tr>\n")
                    .append("            <th>Address</th>\n")
                    .append("            <td>N/A</td>\n")
                    .append("        </tr>\n");
        }

        // Email
        htmlBuilder.append("        <tr class=\"highlight\">\n")
                .append("            <th>Email</th>\n")
                .append("            <td>").append(customer.getEmail() != null ? customer.getEmail() : "N/A").append("</td>\n")
                .append("        </tr>\n");

        // Size (CompanySize enum handling)
        htmlBuilder.append("        <tr>\n")
                .append("            <th>Size</th>\n")
                .append("            <td>").append(customer.getSize() != null ? customer.getSize().name() : "N/A").append("</td>\n")
                .append("        </tr>\n");

        // Account owner
        htmlBuilder.append("        <tr class=\"highlight\">\n")
                .append("            <th>Account Owner</th>\n")
                .append("            <td>").append(customer.getAccountOwner() != null ? customer.getAccountOwner() : "N/A").append("</td>\n")
                .append("        </tr>\n");

        // Categories (handle null and empty list)
        htmlBuilder.append("        <tr>\n")
                .append("            <th>Categories</th>\n")
                .append("            <td>\n");

        if (customer.getCategories() != null && !customer.getCategories().isEmpty()) {
            for (String category : customer.getCategories()) {
                htmlBuilder.append("                <span class=\"categories\">").append(category).append("</span>\n");
            }
        } else {
            htmlBuilder.append("                N/A\n");
        }

        htmlBuilder.append("            </td>\n")
                .append("        </tr>\n");

        // Type (CustomerType enum handling)
        htmlBuilder.append("        <tr class=\"highlight\">\n")
                .append("            <th>Type</th>\n")
                .append("            <td>").append(customer.getType() != null ? customer.getType().name() : "N/A").append("</td>\n")
                .append("        </tr>\n");

        // End HTML structure
        htmlBuilder.append("    </table>\n")
                .append("</div>\n")
                .append("</body>\n")
                .append("</html>\n");

        return htmlBuilder.toString();
    }


    //    TODO sort by createdAt from user input
    @Override
    public Page<Customer> filterCustomers(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<Customer> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            Customer.class
        );
        List<Customer> customers = page.stream().toList();
        return page;
    }

    @Override
    @SneakyThrows
    public Customer getCustomerById(String id) {
        return mongoQueries.getEntity(customerRepository, id);
    }

    @Override
    public Customer updateCustomer(String id, Customer updatedCustomer) throws IllegalAccessException {
        Customer customer = mongoQueries.getEntity(customerRepository, id);
        if (Objects.nonNull(customer.getEmail()) &&  !customer.getEmail().equals(updatedCustomer.getEmail())) {
            User user = userRepository.findByEntityId(customer.getId());
            if (user != null) {
                user.setUsername(updatedCustomer.getEmail());
                userRepository.save(user);
            }
        }
        updatedCustomer = mongoQueries.updateEntity(customerRepository, id, updatedCustomer);
        return updatedCustomer;
    }

    @Override
    public void deleteCustomer(String id) throws ServiceException {
        validateDeleteRequest(id);
        mongoQueries.softDeleteById(customerRepository, id, Customer.class);
    }

    @Override
    public void resetPassword(String id, String password) throws ServiceException {
        Customer customer = customerRepository.findById(id).orElse(null);
        if (customer == null) throw new ServiceException("Invalid customer id provided ", 400);
        List<User> userList = mongoQueries.findByFields(
                Map.ofEntries(
                        Map.entry("entityType", List.of("CUSTOMER")),
                        Map.entry("entityId", List.of(customer.getId()))), null, User.class);
        if (userList.isEmpty()) throw new ServiceException("Invalid customer id provided ", 400);
        User user = userList.get(0);
        user.setPassword(bCryptPasswordEncoder.encode(password));
        userRepository.save(user);
    }

    private void validateDeleteRequest(String id) throws ServiceException {
        if (!orderBookService.getCustomerRelatedOrders(id).isEmpty())
            throw new ServiceException("Customer orders still exist for given customer ", HttpStatus.UNPROCESSABLE_ENTITY.value());
        Pageable pageable = PageRequest.of(0, 10000);
        Page<Enquiry> enquiries = mongoQueries.findByFiltersAndSearch(null, new HashMap<>() {{
            put("customerId", id);
        }}, pageable, Enquiry.class);
        if (!enquiries.isEmpty())
            throw new ServiceException("Enquiries still exist for given customer ", HttpStatus.UNPROCESSABLE_ENTITY.value());
    }

    public String getPrimaryEmail(Customer customer) {
        if (customer.getEmailInfo() != null && !commonUtils.isNullOrEmpty(customer.getEmailInfo().getSalesEmail())) {
            return customer.getEmailInfo().getSalesEmail();
        }
        return customer.getEmail();
    }
}
