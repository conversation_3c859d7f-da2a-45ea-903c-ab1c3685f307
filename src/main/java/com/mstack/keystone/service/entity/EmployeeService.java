package com.mstack.keystone.service.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.CreateUserRequest;
import com.mstack.keystone.model.dto.EmployeeUserDetailRequest;
import com.mstack.keystone.model.dto.EmployeeUserDetailResponse;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.user.EmployeeRepository;
import com.mstack.keystone.repository.user.UserRepository;
import com.mstack.keystone.service.entity.interfaces.IEmployeeService;
import com.mstack.keystone.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class EmployeeService implements IEmployeeService {

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    EmployeeRepository employeeRepository;
    @Autowired
    UserRepository userRepository;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    BCryptPasswordEncoder bCryptPasswordEncoder;

    @Override
    @SneakyThrows
    public Employee createEmployee(Employee employee) {
        if (employee.getEmail() == null || employee.getEmail().isBlank())
            throw new ServiceException("Employee email cannot be empty ", 400);
        List<User> existingUserList = userRepository.findByUserName(employee.getEmail());
        if (existingUserList != null && !existingUserList.isEmpty())
            throw new ServiceException("Email id is already registered with another user ", 400);
        employee.setEmployeeId(commonUtils.CreateID("EMP", mongoQueries.getCount(employeeRepository)));
        employee = mongoQueries.saveEntity(employeeRepository, employee);
        commonUtils.createUser(employee.getEmail(), "EMPLOYEE", employee.getName(), employee.getId());
        return employee;
    }

    @Override
    public void createEmployee(EmployeeUserDetailRequest employeeUserDetailRequest) throws JsonProcessingException {
        Employee employee = saveEmployee(employeeUserDetailRequest);
        //create employee
        employee = mongoQueries.saveEntity(employeeRepository, employee);
        User user = getUser(employeeUserDetailRequest, employee);
        user.setCreatedAt(new Date());
        userRepository.save(user);
//        commonUtils.createUserV2(new CreateUserRequest(
//                employee.getEmail(),
//                "EMPLOYEE",
//                employee.getId(),
//                employeeUserDetailRequest.getPermissions(),
//                employeeUserDetailRequest.getPermissionGroups(),
//                employeeUserDetailRequest.getPassword(),
//                false
//        ));
    }

    private User getUser(EmployeeUserDetailRequest employeeUserDetailRequest, Employee employee) {
        User user = new User();
        user.setUsername(employee.getEmail());
        user.setPassword(bCryptPasswordEncoder.encode(employeeUserDetailRequest.getPassword()));
        user.setEntityType("EMPLOYEE");
        user.setEntityId(employee.getId());
        user.setPermissions(employeeUserDetailRequest.getPermissions());
        user.setPermissionsGroups(employeeUserDetailRequest.getPermissionGroups());
        user.setDeleted(false);
        return user;
    }

    private Employee saveEmployee(EmployeeUserDetailRequest employeeUserDetailRequest) {
        Employee employee = new Employee();
        employee.setEmail(employeeUserDetailRequest.getEmail());
        employee.setName(employeeUserDetailRequest.getName());
        employee.setEmployeeId(commonUtils.CreateID("EMP", mongoQueries.getCount(employeeRepository)));
        System.out.println("employee created sucessfully ");
        return employee;
    }

    @Override
    @SneakyThrows
    public Employee updateEmployee(String id, Employee employee) {
        employee = mongoQueries.updateEntity(employeeRepository, id, employee);
        return employee;
    }

    @Override
    @SneakyThrows
    public Employee getEmployee(String id) {
        return mongoQueries.getEntity(employeeRepository, id);
    }

    @Override
    public boolean deleteEmployee(String id) {
        mongoQueries.softDeleteById(employeeRepository, id, Employee.class);
        return true;
    }

    @Override
    public Page<Employee> filterEmployees(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        return mongoQueries.findByFiltersAndSearch(request.getFilters(), request.getSearch(), pageable, Employee.class);
    }

    @Override
    public List<EmployeeUserDetailResponse> getEmployeeUserDetails() throws ServiceException {
        List<Employee> employees = mongoQueries.findByFields(
                Map.ofEntries(
                        Map.entry("deleted", List.of(false))
                ), null, Employee.class);
        List<EmployeeUserDetailResponse> employeeUserDetailResponseList = new ArrayList<>();
        for (Employee employee : employees) {
            List<User> userList = mongoQueries.findByFields(Map.ofEntries(
                    Map.entry("entityId", List.of(employee.getId()))
            ), null, User.class);
            if (userList.isEmpty()) continue;
            User user = userList.get(0);
            if (user.isDeleted()) continue;
            employeeUserDetailResponseList.add(
                    new EmployeeUserDetailResponse(
                            employee.getId(),
                            employee.getEmployeeId(), employee.getName(), employee.getEmail(), user.getPermissions(), user.getPermissionsGroups()
                    )
            );
        }
        return employeeUserDetailResponseList;
    }

    @Override
    public EmployeeUserDetailResponse updateEmployeeUserDetail(String id, EmployeeUserDetailRequest employeeUserDetailRequest) throws ServiceException, JsonProcessingException {
        Employee employee = employeeRepository.findById(id).orElse(null);
        if (employee == null) throw new ServiceException("invalid id provided ", 400);
        // update employe
        employee.setName(employeeUserDetailRequest.getName());
        employee.setEmail(employeeUserDetailRequest.getEmail());
        employeeRepository.save(employee);
        // update user
        User user = getUserDetailByEmployeeId(employee.getId());
        user.setUsername(employeeUserDetailRequest.getEmail());
//        user.setPassword(bCryptPasswordEncoder.encode(user.getPassword()));
        user.setPermissions(employeeUserDetailRequest.getPermissions());
        user.setPermissionsGroups(employeeUserDetailRequest.getPermissionGroups());
//        commonUtils.updateUser(user);
        userRepository.save(user);
        return getEmployeeUserDetailResponse(employee, user);
    }

    private User getUserDetailByEmployeeId(String id) {
        return mongoQueries.findByFields(
                Map.ofEntries(
                        Map.entry("entityId", List.of(id))
                ), null, User.class).get(0);
    }

    private static EmployeeUserDetailResponse getEmployeeUserDetailResponse(Employee employee, User user) {
        return new EmployeeUserDetailResponse(employee.getId(), employee.getEmployeeId(), employee.getName(), employee.getEmail(), user.getPermissions(), user.getPermissionsGroups());
    }

    @Override
    public void deleteEmployeeUserDetail(String id) throws ServiceException {
        // delete employee
        Employee employee = employeeRepository.findById(id).orElse(null);
        if (employee == null) throw new ServiceException("Invalid id provided ", 400);
        mongoQueries.softDeleteById(employeeRepository, employee.getId(), Employee.class);
        // delete user
        User user = getUserDetailByEmployeeId(employee.getId());
        user.setDeleted(true);
        userRepository.save(user);
    }

    @Override
    public EmployeeUserDetailResponse getEmployeeUserDetailsById(String id) throws ServiceException {
        Employee employee = employeeRepository.findById(id).orElse(null);
        if (employee == null) throw new ServiceException("Invalid employee id provided ", 400);
        User user = getUserDetailByEmployeeId(employee.getId());
        return getEmployeeUserDetailResponse(employee, user);
    }

    @Override
    public void resetPassword(EmployeeUserDetailRequest employeeUserDetailRequest) throws ServiceException {
        Employee employee = employeeRepository.findById(employeeUserDetailRequest.getId()).orElse(null);
        if (employee == null) throw new ServiceException("Invalid employee id provided ", 400);
        User user = getUserDetailByEmployeeId(employee.getId());
        if (employeeUserDetailRequest.getPassword().isBlank())
            throw new ServiceException("Password cannot be blank ", 400);
        user.setPassword(bCryptPasswordEncoder.encode(employeeUserDetailRequest.getPassword()));
        userRepository.save(user);
    }
}
