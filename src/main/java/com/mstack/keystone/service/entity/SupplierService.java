package com.mstack.keystone.service.entity;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.user.SupplierRepository;
import com.mstack.keystone.service.interfaces.ISupplierService;
import com.mstack.keystone.service.order.SupplierOrderBookService;
import com.mstack.keystone.utils.CommonUtils;

import java.util.*;

import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class SupplierService implements ISupplierService {

    @Autowired
    SupplierRepository supplierRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    SupplierOrderBookService supplierOrderBookService;

    @Override
    @SneakyThrows
    public Supplier saveSupplier(Supplier supplier) {
        supplier.setSupplierId(commonUtils.CreateID("SP", mongoQueries.getCount(supplierRepository)));
        return mongoQueries.saveEntity(supplierRepository, supplier);
    }

    // TODO: Implement sorting by createdAt from user input
    @Override
    public Page<Supplier> filterSuppliers(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<Supplier> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            Supplier.class
        );
        List<Supplier> suppliers = page.getContent();
        return page;
    }

    @Override
    @SneakyThrows
    public Supplier getSupplierById(String id) {
        return mongoQueries.getEntity(supplierRepository, id);
    }

    @Override
    public Supplier updateSupplier(String id, Supplier updatedSupplier) throws IllegalAccessException {
        updatedSupplier = mongoQueries.updateEntity(supplierRepository, id, updatedSupplier);
        return updatedSupplier;
    }

    @Override
    public boolean supplierExists(String id) {
        return supplierRepository.existsById(id);
    }

    @Override
    public void deleteSupplier(String id) throws ServiceException {
        validateDeleteRequest(id);
        mongoQueries.softDeleteById(supplierRepository, id, Supplier.class);
    }

    private void validateDeleteRequest(String id) throws ServiceException {
        // NO SUPPLIER PO SHOULD EXIST
        if (!supplierOrderBookService.getBySupplierId(id).isEmpty())
            throw new ServiceException("Cannot delete supplier. Related purchase orders exist.", 422);
    }

    @Override
    public void deleteMstackDocument(String documentType,String supplierId, String productId, String fileId) {
        Supplier supplier=mongoQueries.getEntity(supplierRepository,supplierId);
        List<SupplierProduct> products=supplier.getProducts();
        for (SupplierProduct product:products) {
            if(productId.equals(product.getProduct().getId())){
                deleteMstackFromProduct(product,documentType,fileId);
            }
        }
        supplierRepository.save(supplier);
    }
    void deleteMstackFromProduct(SupplierProduct product,String documentType,String fileId){
        List<SupplierDocument> documents=product.getMstackDocuments();
        Iterator<SupplierDocument> documentIterator = documents.iterator();
        while (documentIterator.hasNext()) {
            SupplierDocument document=documentIterator.next();
            if(documentType.equals(document.getDocumentType())){
                List<File> files=document.getFiles();
                Iterator<File> iterator = files.iterator();
                while (iterator.hasNext()) {
                    File file = iterator.next();
                    if (file.getFileId().equals(fileId)) {
                        iterator.remove();
                        break;// Remove the file with the matching id.
                    }
                }
                if(files.size()==0){
                    documentIterator.remove();
                }
            }
        }
    }

    @Override
    public List<SupplierProductDto> getSuppliersForProductId(String productId) {
        List<SupplierProductDto> productDtoList=new ArrayList<>();
        List<Supplier> supplierList=supplierRepository.getSuppliersFromProductId(new ObjectId(productId));
        for (Supplier supplier:supplierList) {
            SupplierProductDto dto=new SupplierProductDto(supplier,productId);
            productDtoList.add(dto);
        }
        return productDtoList;
    }

    @Override
    public List<SupplierDocument> uploadMstackDocument(String supplierId, String productId, List<SupplierDocument> documents) {
        Supplier supplier=mongoQueries.getEntity(supplierRepository,supplierId);
        List<SupplierProduct> products=supplier.getProducts();
        SupplierProduct supplierProduct=null;
        for (SupplierProduct product:products) {
            if(productId.equals(product.getProduct().getId())){
                addMstackDocumentsToProduct(product,documents);
                supplierProduct=product;
            }
        }
        supplierRepository.save(supplier);
        return supplierProduct.getMstackDocuments();

    }
    void addMstackDocumentsToProduct(SupplierProduct product,List<SupplierDocument> documents){
        if(product.getMstackDocuments()==null){
            product.setMstackDocuments(new ArrayList<>());
        }
        List<SupplierDocument> existingDocuments=product.getMstackDocuments();
        HashMap<String,List<File>> map=new HashMap<>();
        for (SupplierDocument document:existingDocuments) {
            map.put(document.getDocumentType(),document.getFiles());
        }
        for (SupplierDocument document:documents) {
            if(map.containsKey(document.getDocumentType())){
                mergeTwoList(map.get(document.getDocumentType()),document.getFiles(),document.getRemark());
            }
            else {
                existingDocuments.add(document);
                for (File file:document.getFiles()) {
                    file.setRemark(document.getRemark());
                }
                document.setRemark(null);
                map.put(document.getDocumentType(),document.getFiles());
            }
        }
    }

    void mergeTwoList(List<File> first,List<File> second,String remark){
        for (File file:second) {
            file.setRemark(remark);
            first.add(file);
        }
    }

}
