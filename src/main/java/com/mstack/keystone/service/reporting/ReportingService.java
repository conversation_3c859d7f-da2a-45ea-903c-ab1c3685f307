package com.mstack.keystone.service.reporting;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.report.GenerateReportRequest;
import com.mstack.keystone.model.dto.reporting.CustomerOrderRow;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.user.SupplierRepository;
import com.mstack.keystone.scripts.InvoiceScripts;
import com.opencsv.CSVWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Service
public class ReportingService {

    @Autowired
    InvoiceScripts invoiceScripts;
    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    OrderBookRepository orderBookRepository;

    @Autowired
    SupplierRepository supplierRepository;

    public void generateOrderReport() throws IOException, ServiceException {
        List<CustomerOrder> orders = customerOrderRepository.findAllNonDeletedOrders();
        String filePath = "order_report.csv";
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = {
                "Customer Order Id", "Ordered Product Id", "Product Trade Name", "Type", "Pack Size", "Tare Weight", "Dimension", "Updated Packaging Name"
        };
        writer.writeNext(headerKeys);
        for (CustomerOrder customerOrder : orders) {
            // TODO REMOVE LATER
            if (customerOrder.isDeleted()) throw new ServiceException("Found a deleted order ", 400);
            customerOrder.getProducts().forEach(orderedProduct -> {
                CustomerOrderRow orderRow = new CustomerOrderRow(
                        customerOrder.getOrderId(), orderedProduct.getId(),
                        Optional.ofNullable(orderedProduct.getProduct())
                                .map(Product::getTradeName)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getType)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getPackSize)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getTareWeight)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getDimension)
                                .orElse(""),
                        ""
                );
                writer.writeNext(orderRow.toStringArray());
            });
        }
        writer.flush();
        writer.close();
// un comment to delete file after processing
//        if (file.exists()) {
//            file.delete();
//        }
    }

    public void generateOrderBookReport() throws IOException {
        List<OrderBook> orders = orderBookRepository.findAllNonDeletedOrdersBook();
        String filePath = "order_book_report.csv";
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = {
                "Order Book Id", "Ordered Product Id", "Product Trade Name", "Type", "Pack Size", "Tare Weight", "Dimension", "Updated Packaging Name"
        };
        writer.writeNext(headerKeys);
        orders.forEach(customerOrder -> {
            customerOrder.getProducts().forEach(orderedProduct -> {
                CustomerOrderRow orderRow = new CustomerOrderRow(
                        customerOrder.getOrderBookId(), orderedProduct.getId(),
                        Optional.ofNullable(orderedProduct.getProduct())
                                .map(Product::getTradeName)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getType)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getPackSize)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getTareWeight)
                                .orElse(""),
                        Optional.ofNullable(orderedProduct.getPackaging())
                                .map(Packaging::getDimension)
                                .orElse(""),
                        ""
                );
                writer.writeNext(orderRow.toStringArray());
            });
        });
        writer.flush();
        writer.close();
// un comment to delete file after processing
//        if (file.exists()) {
//            file.delete();
//        }
    }

    public void generateSupplierReport() throws IOException, ServiceException {
        List<Supplier> supplierList = supplierRepository.getAllNonDeletedSupplier();
        String filePath = "supplier_report.csv";
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = {
                "Supplier Id", "Product Id", "Product Trade Name", "Packaging idx", "Type", "Pack Size", "Tare Weight", "Dimension", "Updated Packaging Name"
        };
        writer.writeNext(headerKeys);
        for (Supplier supplier : supplierList) {
            if (supplier.isDeleted()) throw new ServiceException("Found a deleted supplier ", 400);
            supplier.getProducts().forEach(orderedProduct -> {
                int packagingIdx = 0;
                for (Packaging packaging : orderedProduct.getPackaging()) {
                    String[] row = new String[]{
                            supplier.getSupplierId(), "",
                            Optional.ofNullable(orderedProduct.getProduct())
                                    .map(Product::getTradeName)
                                    .orElse(""),
                            String.valueOf(packagingIdx),
                            Optional.ofNullable(packaging.getType())
                                    .orElse(""),
                            Optional.of(packaging)
                                    .map(Packaging::getPackSize)
                                    .orElse(""),
                            Optional.ofNullable(packaging)
                                    .map(Packaging::getTareWeight)
                                    .orElse(""),
                            Optional.ofNullable(packaging)
                                    .map(Packaging::getDimension)
                                    .orElse(""),
                            ""
                    };
                    packagingIdx++;
                    writer.writeNext(row);
                }
            });
        }
        writer.flush();
        writer.close();
    }

    public void generateReport(GenerateReportRequest generateReportRequest) throws IOException {
        if (generateReportRequest.getReportType().equals("mstack_invoice")) {
            invoiceScripts.generateMstackInvoiceReportCsv(generateReportRequest);
        }

        if (generateReportRequest.getReportType().equals("chemstack_invoice")) {
            invoiceScripts.generateChemstackInvoiceReportCsv(generateReportRequest);
        }

        if (generateReportRequest.getReportType().equals("salesorder_invoice")) {
            invoiceScripts.generateSalesOrderReportCsv(generateReportRequest);
        }

        if (generateReportRequest.getReportType().equals("opensalesorder_invoice")) {
            invoiceScripts.generateOpenSalesOrderReportCsv(generateReportRequest);
        }
    }
}
