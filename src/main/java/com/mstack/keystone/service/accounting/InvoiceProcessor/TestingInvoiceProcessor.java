package com.mstack.keystone.service.accounting.InvoiceProcessor;

import com.mstack.keystone.model.repository.Accounting.Expense;
import com.mstack.keystone.model.repository.Accounting.Invoice;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class TestingInvoiceProcessor implements IInvoiceProcessor{
    @Override
    public List<Expense> processInvoice(Invoice invoice) {
        List<HashMap> items = invoice.getItems();
        List expenses = new ArrayList();
        for (HashMap item : items) {
            Expense expense = new Expense();
            expense.setExpenseType("TESTING");
            expense.setAmount(((Number) item.getOrDefault("cost", 0)).floatValue());
            expense.setDescription(item.getOrDefault("description", "").toString());
            expense.setInvoiceId(invoice.getId());
            expense.setCurrencyType(invoice.getCurrencyType());
            expense.setEntityId(item.get("dispatchOrderId").toString());
            expense.setMeta(new HashMap<>());
            expense.getMeta().put("dispatchOrderId", item.getOrDefault("dispatchOrderId", null));
            expense.getMeta().put("productId", item.getOrDefault("productId", null));
            expense.getMeta().put("poNumber", item.getOrDefault("poNumber", null));
            expense.getMeta().put("taxInPercent", item.getOrDefault("taxInPercent", null));
            expenses.add(expense);
        }
        return expenses;
    }
}
