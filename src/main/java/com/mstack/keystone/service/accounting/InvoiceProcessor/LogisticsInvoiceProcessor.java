package com.mstack.keystone.service.accounting.InvoiceProcessor;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.repository.Accounting.Expense;
import com.mstack.keystone.model.repository.Accounting.Invoice;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class LogisticsInvoiceProcessor implements IInvoiceProcessor{

    @Autowired
    CustomerOrderService customerOrderService;

    @Override
    @SneakyThrows
    public List<Expense> processInvoice(Invoice invoice) {
        List<HashMap> items = invoice.getItems();
        List expenses = new ArrayList();
        for (HashMap item : items) {
            // if expense doesn't have a product id split the expense based on the product quantity ratio
            if (item.getOrDefault("productId", null) == null) {
                // fetch order from dispatchOrderId
                String dispatchOrderId = (String) item.getOrDefault("dispatchOrderId", null);
                if (dispatchOrderId == null || dispatchOrderId.isEmpty())
                    throw new ServiceException("Dispatch order id cannot be empty ", 400);
                CustomerOrder order = customerOrderService.getCustomerOrderById(dispatchOrderId);
                if (order == null)
                    throw new ServiceException("Invalid dispatch order id provided ", 400);
                double totalQty = getOrderTotalQty(order);
                double itemCost = ((Number) item.getOrDefault("cost", 0)).doubleValue();
                for (OrderedProduct orderedProduct : order.getProducts()) {
                    double currQty = orderedProduct.getQuantity();
                    double qtyRatio = currQty / totalQty;
                    item.put("cost", qtyRatio * itemCost);
                    item.put("productId", orderedProduct.getId());
                    expenses.add(createExpense(item, invoice));
                }
            } else {
                expenses.add(createExpense(item, invoice));
            }
        }
        return expenses;
    }

    private double getOrderTotalQty(CustomerOrder order) {
        double totalQty = 0;
        for (OrderedProduct product : order.getProducts()) {
            totalQty += product.getQuantity();
        }
        return totalQty;
    }

    public static Expense createExpense(Map<String, Object> item, Invoice invoice) {
        Expense expense = new Expense();
        // TODO HANDLE CHECK PROPERLY
        expense.setExpenseType((String) item.getOrDefault("chargeType", ""));
        expense.setCurrencyType(invoice.getCurrencyType());
        expense.setDescription(item.getOrDefault("description", "").toString());
        expense.setAmount(((Number) item.getOrDefault("cost", 0)).floatValue());
        expense.setInvoiceId(invoice.getId());
        expense.setEntityId(item.get("dispatchOrderId").toString());
        expense.setMeta(new HashMap<>());
        expense.getMeta().put("dispatchOrderId", item.getOrDefault("dispatchOrderId", null));
        expense.getMeta().put("productId", item.getOrDefault("productId", null));
        expense.getMeta().put("poNumber", item.getOrDefault("poNumber", null));
        expense.getMeta().put("taxInPercent", item.getOrDefault("taxInPercent", null));
        return expense;
    }

}
