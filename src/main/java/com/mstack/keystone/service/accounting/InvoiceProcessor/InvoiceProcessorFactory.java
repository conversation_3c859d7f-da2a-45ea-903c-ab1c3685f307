package com.mstack.keystone.service.accounting.InvoiceProcessor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InvoiceProcessorFactory {
    @Autowired
    SupplierInvoiceProcessor supplierInvoiceProcessor;

    @Autowired
    PackagingInvoiceProcessor packagingInvoiceProcessor;

    @Autowired
    LogisticsInvoiceProcessor logisticsInvoiceProcessor;

    @Autowired
    MiscellaneousInvoiceProcessor miscellaneousInvoiceProcessor;

    @Autowired
    TestingInvoiceProcessor testingInvoiceProcessor;

    public IInvoiceProcessor getInvoiceProcessor(String docType){
        switch (docType){
            case "SupplierInvoice":
                return supplierInvoiceProcessor;
            case "TestingInvoice":
                return testingInvoiceProcessor;
            case "LogisticsInvoice":
                return logisticsInvoiceProcessor;
            case "MiscellaneousInvoice":
                return miscellaneousInvoiceProcessor;
            case "PackagingInvoice":
                return packagingInvoiceProcessor;
        }
        return null;
    }
}
