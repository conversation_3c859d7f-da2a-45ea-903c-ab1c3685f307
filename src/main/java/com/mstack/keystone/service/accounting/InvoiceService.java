package com.mstack.keystone.service.accounting;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.InvoiceNumberGenerationRequest;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.enums.CurrencyType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Accounting.Expense;
import com.mstack.keystone.model.repository.Accounting.Invoice;
import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.repository.accounting.ExpenseRepository;
import com.mstack.keystone.repository.accounting.InvoiceRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.user.UserRepository;
import com.mstack.keystone.service.DashboardConfigServiceImpl;
import com.mstack.keystone.service.accounting.InvoiceProcessor.IInvoiceProcessor;
import com.mstack.keystone.service.accounting.InvoiceProcessor.InvoiceProcessorFactory;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.document.DocumentService;
import com.mstack.keystone.utils.FileUtil;
import com.opencsv.CSVWriter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileWriter;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Slf4j
public class InvoiceService {
    @Autowired
    InvoiceProcessorFactory invoiceProcessorFactory;
    @Autowired
    InvoiceRepository invoiceRepository;
    @Autowired
    ExpenseRepository expenseRepository;

    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    RequestConfig requestConfig;
    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    AwsSesService awsSesService;

    @Autowired
    UserRepository userRepository;

    @Autowired
    DocumentService documentService;

    @Autowired
    FileUtil fileUtil;

    @Autowired
    DashboardConfigServiceImpl dashboardConfigService;

    @SneakyThrows
    public String generateInvoiceNumber(InvoiceNumberGenerationRequest igr) {
        String invoiceType = igr.getInvoiceType();
        DashBoardConfig dashBoardConfig = dashboardConfigService.getDashboardConfigByConfigName("invoiceConfig");
        Map<String, Object> invoiceConfig = getInvoiceConfigFromDashboard(dashBoardConfig, invoiceType);
        if (invoiceConfig == null)
            throw new ServiceException("Cannot generate invoice number unable to find config for given doc type", 400);
        String prefix = (String) invoiceConfig.get("invoiceNumberPrefix");
        int offset = (int) invoiceConfig.get("invoiceNumberOffset");
        String invoiceNumber = prefix;
        if (invoiceType.equals("CHEMSTACK_TAX_INVOICE") && offset < 100) invoiceNumber += "0" + offset;
        else invoiceNumber += offset;
        // Increment offset
        invoiceConfig.put("invoiceNumberOffset", offset + 1);
        // update invoice config
        updateInvoiceConfg(invoiceConfig, dashBoardConfig);
        return invoiceNumber;
    }

    private void updateInvoiceConfg(Map<String, Object> invoiceConfig, DashBoardConfig dashBoardConfig) {
        int updateIdx = -1;
        for (int i = 0; i < dashBoardConfig.getValue().size(); i++) {
            Map<String, Object> val = dashBoardConfig.getValue().get(i);
            if (val.containsKey("name") && val.get("name").equals(invoiceConfig.get("name"))) {
                updateIdx = i;
                break;
            }
            ;
        }
        if (updateIdx != -1) {
            dashBoardConfig.getValue().set(updateIdx, invoiceConfig);
            dashboardConfigService.updateDashboardConfig(dashBoardConfig.getId(), dashBoardConfig);
            log.info("updated invoice config for {} with offset as : {}", invoiceConfig.get("name"), invoiceConfig.get("invoiceNumberOffset"));
        }
    }

    private Map<String, Object> getInvoiceConfigFromDashboard(DashBoardConfig dashBoardConfig, String configType) {
        for (Map<String, Object> val : dashBoardConfig.getValue()) {
            if (val.containsKey("name") && val.get("name").equals(configType)) return val;
        }
        return null;
    }

    @SneakyThrows
    public Invoice createInvoice(Invoice invoice){
        IInvoiceProcessor invoiceProcessor=invoiceProcessorFactory.getInvoiceProcessor(invoice.getInvoiceType());
        if(invoiceProcessor== null){
            throw new ServiceException("THe invoice type is not supported",400);
        }
        if(invoice.getCreatedAt()==null){
            invoice.setCreatedAt(new Date());
            invoice.setCreatedBy(requestConfig.getEntityId());
        }
        invoice.setUpdatedAt(new Date());
        invoice.setUpdatedBy(requestConfig.getEntityId());
        invoice=invoiceRepository.save(invoice);
        List<Expense> expenses= invoiceProcessor.processInvoice(invoice);
        deleteOldExpenses(invoice.getId());
        expenseRepository.saveAll(expenses);
        return invoice;
    }

    private void deleteOldExpenses(String invoiceId){
        List<Expense> expenses=expenseRepository.findByInvoiceId(invoiceId);
        if (expenses == null || expenses.isEmpty()) return;
        for (Expense expense:expenses) {
            expense.setDeleted(true);
        }
        expenseRepository.saveAll(expenses);
    }

    public Invoice getInvoice(String id){
        return mongoQueries.getEntity(invoiceRepository,id);
    }

    public Page<Invoice> getAllInvoices(FilterRequest request){
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<Invoice> page = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                Invoice.class
        );
        return page;
    }

    @SneakyThrows
    public void createReport(Date from, Date to, String userId) {
        User user = userRepository.findByEntityId(userId);
        List<CustomerOrder> orderList = customerOrderRepository.findByPoDateBetween(from, to);
        HashMap<String, List<File>> orderFiles = new HashMap<>();
        String filePath = from.toString() + "_" + to.toString() + "_expense_sheet.csv";
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        // round to two decimals
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.CEILING);
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = {"Customer", "Country", "PO Number", "Order Id", "Invoice Date", "PO Date", "Shipment Date", "Product", "Volume", "Unit", "Price", "Sales Value", "COGS", "Packaging", "Inland logistics", "Port charges", "Ocean Freight", "Warehouse", "Last mile logistics", "Customs duty", "Testing", "Insurance", "Total cost", "GM%", "CM%"};
        writer.writeNext(headerKeys);
        for (CustomerOrder order:orderList) {
            List<OrderedProduct> products=order.getProducts();
            String customer = order.getCustomer().getName();
            String country = order.getCustomer().getAddress().getCountry();
            if (country == null)
                country = "";
            String invoiceDate = order.getInvoiceDate() != null ? dateFormat.format(order.getInvoiceDate()) : "";
            String poDate = order.getPurchaseOrderDate() != null ? dateFormat.format(order.getPurchaseOrderDate()) : "";
            String shipmentDate = order.getShipmentDate() != null ? dateFormat.format(order.getShipmentDate()) : "";
            double exchgRate = (order.getCustomExchangeRate() == null) ? 82 : order.getCustomExchangeRate();
            for (OrderedProduct product:products) {
                // get all invoice list
                List<Invoice> proudctInvoices = invoiceRepository.findInvoicesByDispatchOrderIdAndProductIdAndPoNumber(order.getId(), product.getId(), order.getPurchaseOrderNumber());
                orderFiles.put(order.getOrderId() + "_" + product.getProduct().getTradeName().toUpperCase(), getFilesFromInvoiceId(proudctInvoices));
                // get all expenses with dispatch orderId and productId
                List<Expense> expenses = expenseRepository.findByMetaProductIdAndDispatchOrderIdAndPoNumber(product.getId(), order.getId(), order.getPurchaseOrderNumber());
                HashMap<String, Double> expenseMap = new HashMap<>();
                for (Expense expense:expenses) {
                    if (expense.getCurrencyType() != null && expense.getCurrencyType().equals(CurrencyType.RUPEE)) {
                        double expenseAmt = expense.getAmount() / exchgRate;
                        expenseAmt = Double.parseDouble(df.format(expenseAmt));
                        expenseMap.put(expense.getExpenseType(), expenseMap.getOrDefault(expense.getExpenseType(), 0.00) + expenseAmt);
                    } else {
                        double expenseAmt = Double.parseDouble(df.format(expense.getAmount()));
                        expenseMap.put(expense.getExpenseType(), expenseMap.getOrDefault(expense.getExpenseType(), 0.00) + expenseAmt);
                    }
                }
                String productName = product.getProduct().getTradeName();
                double volume = product.getQuantity();
                String unit = getAbbreviation(product.getUom());
                double price = product.getPrice();
                double salesValue = Double.parseDouble(df.format(product.getQuantity() * product.getPrice()));
                if (salesValue == 0) {
                    throw new ServiceException("Sales value cannot be zero for order id " + order.getOrderId(), 400);
                }
                double cogs = Double.parseDouble(df.format(expenseMap.getOrDefault("COGS", 0.0)));
                double packaging = Double.parseDouble(df.format(expenseMap.getOrDefault("PACKAGING", 0.0)));
                double inlandLogistics = Double.parseDouble(df.format(expenseMap.getOrDefault("INLAND_LOGISTICS_CHARGES", 0.0)));
                double portCharges = Double.parseDouble(df.format(expenseMap.getOrDefault("PORT_CHARGES", 0.0)));
                double oceanFreight = Double.parseDouble(df.format(expenseMap.getOrDefault("OCEAN_FREIGHT_CHARGES", 0.0)));
                double warehouseCharges = Double.parseDouble(df.format(expenseMap.getOrDefault("WAREHOUSE_CHARGES", 0.0)));
                double lastMileLogistics = Double.parseDouble(df.format(expenseMap.getOrDefault("LAST_MILE_LOGISTICS_CHARGES", 0.0)));
                double customsDuty = Double.parseDouble(df.format(expenseMap.getOrDefault("CUSTOMS_DUTY_CHARGES", 0.0)));
                double testing = Double.parseDouble(df.format(expenseMap.getOrDefault("TESTING", 0.0)));
                double insuranceCharges = Double.parseDouble(df.format(expenseMap.getOrDefault("INSURANCE_CHARGES", 0.0)));
                double totalCost = cogs + packaging + inlandLogistics + portCharges + oceanFreight + warehouseCharges + lastMileLogistics + customsDuty + testing + insuranceCharges;
                totalCost = Double.parseDouble(df.format(totalCost));
                double gm = ((salesValue - cogs - packaging - customsDuty - insuranceCharges) / salesValue) * 100;
                gm = Double.parseDouble(df.format(gm)); // Round to two decimal places
                double cm = ((salesValue - totalCost) / salesValue) * 100;
                cm = Double.parseDouble(df.format(cm)); // Round to two decimal places
                String[] row = new String[]{
                        String.valueOf(customer), String.valueOf(country),
                        String.valueOf(order.getPurchaseOrderNumber()),
                        String.valueOf(order.getOrderId()),
                        String.valueOf(invoiceDate), String.valueOf(poDate), String.valueOf(shipmentDate),
                        String.valueOf(productName), String.valueOf(volume),
                        String.valueOf(unit), String.valueOf(price),
                        String.valueOf(salesValue), String.valueOf(cogs), String.valueOf(packaging), String.valueOf(inlandLogistics),
                        String.valueOf(portCharges), String.valueOf(oceanFreight), String.valueOf(warehouseCharges),
                        String.valueOf(lastMileLogistics), String.valueOf(customsDuty), String.valueOf(testing), String.valueOf(insuranceCharges),
                        String.valueOf(totalCost), String.valueOf(gm), String.valueOf(cm)
                };
                writer.writeNext(row);
            }
        }
        writer.flush();
        writer.close();
        // generate zip for orders files
        String zipFileName = from.toString() + "_" + to.toString() + "_invoice_files.zip";
        MultipartFile zipFile = fileUtil.convertBytesToMultipart(fileUtil.getAllFilesAsZip(orderFiles), zipFileName);
        // create file meta
        HashMap<String, Object> meta = new HashMap<>();
        HashMap<String, String> extraDetails = new HashMap<>();
        extraDetails.put("fileName", zipFile.getName());
        meta.put("meta", extraDetails);
        meta.put("category", "invoices");
        meta.put("name", zipFile.getName());
        com.mstack.keystone.model.dto.File response = documentService.uploadFileWitEntityDetails(meta, zipFile, user.getEntityId(), user.getEntityType());
        String signedUrl = documentService.getSignedUrlFromFileId(response.getFileId());
        try {
            awsSesService.sendEmailWithAttachments(
                    "<EMAIL>",
                    List.of(user.getUsername()),
                    "Final order report from date " + dateFormat.format(from) + " to " + dateFormat.format(to),
                    "Final order report from date " + dateFormat.format(from) + " to " + dateFormat.format(to) + "\n\n\n" +
                            "You can access the report using the following link: " + signedUrl,
                    List.of(file.getAbsolutePath())
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }
        // after mail sent delete file
        if (file.exists()) {
            file.delete();
        }
    }

    private List<File> getFilesFromInvoiceId(List<Invoice> proudctInvoices) {
        List<File> files = new ArrayList<>();

        proudctInvoices.forEach((productInvoice) -> {
            if (productInvoice.getInvoiceFile() != null && !productInvoice.getInvoiceFile().isEmpty()) {
                productInvoice.getInvoiceFile().forEach(invoiceFile -> {
                    files.add(documentService.downloadFileViaSignedUrl(invoiceFile.getFileId(), invoiceFile.getName()));
                });
            }
        });
        return files;
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "lb";
            case GALLON -> "gal";
            case LITRE -> "L";
            case KILOLITRE -> "kL";
            case KILOGRAM -> "kg";
            default -> "";
        };
    }

    public void deleteInvoice(String invoiceId) {
        // delete invoice related expenses
        deleteOldExpenses(invoiceId);
        Invoice invoice = invoiceRepository.findById(invoiceId).get();
        invoice.softDelete();
        invoiceRepository.save(invoice);
    }
}
