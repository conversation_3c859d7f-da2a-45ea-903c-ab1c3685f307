package com.mstack.keystone.service.accounting;

import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.repository.Accounting.*;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.MonetaryValue;
import com.mstack.keystone.model.repository.order.SupplierRFQ;
import com.mstack.keystone.repository.accounting.InvoiceRepositoryV2;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class InvoiceServiceV2 {

  @Autowired
  InvoiceRepositoryV2 invoiceRepositoryV2;

  @Autowired
  CustomerOrderService customerOrderService;

  @SneakyThrows
  public OperationalInvoices createInvoice(OperationalInvoices operationalInvoices) {
    // Check if record exists
    Optional<OperationalInvoices> existingInvoiceOpt = invoiceRepositoryV2
            .findByCustomerPurchcaseOrderId(operationalInvoices.getCustomerPurchcaseOrderId());

    if (existingInvoiceOpt.isPresent()) {
      OperationalInvoices existingInvoice = existingInvoiceOpt.get();

      // Merge new invoices with existing ones
      List<InvoiceEntry> existingInvoices = Optional.ofNullable(existingInvoice.getInvoices()).orElse(new ArrayList<>());
      List<InvoiceEntry> newInvoices = Optional.ofNullable(operationalInvoices.getInvoices()).orElse(new ArrayList<>());

      existingInvoices.addAll(newInvoices);
      existingInvoice.setInvoices(existingInvoices);

      return invoiceRepositoryV2.save(existingInvoice);
    } else {
      return invoiceRepositoryV2.save(operationalInvoices);
    }
  }

  public OperationalInvoices getInvoiceByCustomerPurchcaseOrderId(String customerPurchcaseOrderId) {
    return invoiceRepositoryV2
            .findByCustomerPurchcaseOrderId(customerPurchcaseOrderId)
            .orElse(null);
  }

  public MarginAnalysisResponse getCalculatedData(String poId) {
    List<CustomerOrder> customerOrders = customerOrderService.findCustomerOrderByPoNumber(poId);
    OperationalInvoices operationalInvoices = getInvoiceByCustomerPurchcaseOrderId(poId);
    return calculateMargin(customerOrders, operationalInvoices);
  }

  public MarginAnalysisResponse computeMarginAnalysis(List<MarginDataDTO> marginDataList, double salesOrderValue, double targetQualificationValue,String currency) {
    Set<String> logisticsCostTypes = Set.of("Total Logistics Cost", "Sales Order Value");

    double totalInvoiceValue = 0.0;
    double pendingRfqValue = 0.0;

    for (MarginDataDTO item : marginDataList) {
      if (!logisticsCostTypes.contains(item.getCostType())) {
        totalInvoiceValue += item.getInvoiceValue();  // already converted
        if (item.getInvoiceValue() <= 0) {
          pendingRfqValue += item.getRfqValue(); // already converted
        }
      }
    }

    double totalCostValue = totalInvoiceValue + pendingRfqValue;

    double overallMarginDeviation = salesOrderValue > 0
            ? ((salesOrderValue - totalCostValue) / salesOrderValue) * 100
            : 0;

    MarginAnalysisResponse response = new MarginAnalysisResponse();
    response.setMarginData(marginDataList);
    response.setOverallMarginDeviation(overallMarginDeviation);
    response.setTargetQualificationValue(targetQualificationValue);
    response.setMasterCurrency(currency);
    return response;
  }

  public MarginAnalysisResponse calculateMargin(List<CustomerOrder> customerOrders, OperationalInvoices operationalInvoices) {
    Map<String, Double> invoiceTotals = new HashMap<>();
    Map<String, Double> rfqTotals = new HashMap<>();

    if (customerOrders.isEmpty()) return null;

    double productInvoiceValue = 0.0;
    double targetQualificationValue = 0.0;

    // Step 1: Process RFQ values with conversion
    for (CustomerOrder rfq : customerOrders) {
      SupplierRFQ details = rfq.getSupplierRfq();
      if (details != null) {
        addConvertedObjectValue(rfqTotals, "firstMileLogistics", details.getFirstMileLogistics(), getLogisticsRate(rfq));
        addConvertedObjectValue(rfqTotals, "seaFreight", details.getSeaFreight(), getLogisticsRate(rfq));
        addConvertedObjectValue(rfqTotals, "lastMileLogistics", details.getLastMileLogistics(), getLogisticsRate(rfq));
        addConvertedObjectValue(rfqTotals, "destinationCharges", details.getDestinationCharges(), getLogisticsRate(rfq));
        addConvertedObjectValue(rfqTotals, "dutyAmountValue", details.getDutyAmountValue(), getRate(rfq, "dutyAmountValue"));
        addConvertedObjectValue(rfqTotals, "purchaseOrderValue", details.getPurchaseOrderValue(), getRate(rfq, "purchaseOrderValue"));
        addConvertedObjectValue(rfqTotals, "logisticsCost", details.getLogisticsCost(), getRate(rfq, "logisticsCost"));

        // No conversion for Sales Order
        if (details.getSalesOrderValue() != null) {
          rfqTotals.put("salesOrderValue", rfqTotals.getOrDefault("salesOrderValue", 0.0) + details.getSalesOrderValue().getValue());
        }

        targetQualificationValue += details.getTargetContributionMargin() != null ? details.getTargetContributionMargin() : 0.0;
      }

      if (rfq.getProducts() != null) {
        for (OrderedProduct product : rfq.getProducts()) {
          double price = defaultDouble(product.getPrice());
          double quantity = defaultDouble(product.getQuantity());
          productInvoiceValue += price * quantity;
          rfqTotals.put("productValue", rfqTotals.getOrDefault("productValue", 0.0) + price * quantity);
        }
      }
    }

    // Step 2: Process invoice entries, summing converted values by type
    if (operationalInvoices != null && operationalInvoices.getInvoices() != null) {
      for (InvoiceEntry invoice : operationalInvoices.getInvoices()) {
        String type = invoice.getInvoiceType();
        double amount = parseDouble(invoice.getTotalAmount());
        double rate = invoice.getConversionRate() != null ? invoice.getConversionRate() : 1.0;
        double converted = amount * rate;
        invoiceTotals.put(type, invoiceTotals.getOrDefault(type, 0.0) + converted);
      }
    }

    // Step 3: Build margin data list
    List<MarginDataDTO> marginData = new ArrayList<>();

    String currency = customerOrders.get(0).getSupplierRfq().getSalesOrderValue().getCurrency();

    marginData.add(build("Sales Order Value", rfqTotals.get("salesOrderValue"), productInvoiceValue));
    marginData.add(build("Supplier Invoice", rfqTotals.get("purchaseOrderValue"), invoiceTotals.get("Supplier Invoice")));
    marginData.add(build("First mile logistics", rfqTotals.get("firstMileLogistics"), invoiceTotals.get("First mile logistics")));
    marginData.add(build("Sea Freight", rfqTotals.get("seaFreight"), invoiceTotals.get("Sea Freight")));
    marginData.add(build("Last mile logistics", rfqTotals.get("lastMileLogistics"), invoiceTotals.get("Last mile logistics")));
    marginData.add(build("Destination Charges", rfqTotals.get("destinationCharges"), invoiceTotals.get("Destination Charges")));
    marginData.add(build("Total Logistics Cost", rfqTotals.get("logisticsCost"),
            invoiceTotals.getOrDefault("First mile logistics", 0.0)
                    + invoiceTotals.getOrDefault("Sea Freight", 0.0)
                    + invoiceTotals.getOrDefault("Last mile logistics", 0.0)
                    + invoiceTotals.getOrDefault("Destination Charges", 0.0)
           ));
    marginData.add(build("Duty Applicable", rfqTotals.get("dutyAmountValue"), invoiceTotals.get("Duty Applicable")));

    // Step 4: Handle any remaining types in invoiceTotals not explicitly processed
    Set<String> knownTypes = Set.of("Supplier Invoice", "First mile logistics", "Sea Freight", "Last mile logistics", "Destination Charges", "Duty Applicable");
    for (Map.Entry<String, Double> entry : invoiceTotals.entrySet()) {
      if (!knownTypes.contains(entry.getKey())) {
        marginData.add(build(entry.getKey(), 0.0, entry.getValue()));
      }
    }

    return computeMarginAnalysis(marginData, productInvoiceValue, targetQualificationValue, currency);
  }

  private void addConvertedObjectValue(Map<String, Double> map, String key, MonetaryValue amount, Double rate) {
    if (amount != null) {
      Double value = amount.getValue();
      if (value != null) {
        map.put(key, map.getOrDefault(key, 0.0) + value * (rate != null ? rate : 1.0));
      }
    }
  }

  // Overloaded version: Handles plain Double values
  private void addConvertedObjectValue(Map<String, Double> map, String key, Double value, Double rate) {
    if (value != null) {
      double conversion = rate != null ? rate : 1.0;
      map.put(key, map.getOrDefault(key, 0.0) + value * conversion);
    }
  }

  private Double getLogisticsRate(CustomerOrder rfq) {
    return rfq.getSupplierRfq() != null && rfq.getSupplierRfq().getLogisticsCost() != null
            ? rfq.getSupplierRfq().getLogisticsCost().getConversionRate()
            : 1.0;
  }

  private Double getRate(CustomerOrder rfq, String key) {
    MonetaryValue value = null;
    if (rfq.getSupplierRfq() == null) return 1.0;
    switch (key) {
      case "purchaseOrderValue":
        value = rfq.getSupplierRfq().getPurchaseOrderValue();
        break;
      case "logisticsCost":
        value = rfq.getSupplierRfq().getLogisticsCost();
        break;
      case "dutyAmountValue":
        value = rfq.getSupplierRfq().getDutyAmountValue();
        break;
    }
    Double rate = value.getConversionRate();  // Use Double, not double
    return rate != null ? value.getConversionRate() : 1.0;
  }


  private MarginDataDTO build(
          String type,
          Double rfqVal,
          Double invoiceVal
  ) {
    rfqVal = (rfqVal == null) ? 0.0 : rfqVal;
    invoiceVal = (invoiceVal == null) ? 0.0 : invoiceVal;
    // Calculate variance and deviation
    double variance = rfqVal - invoiceVal;
    double deviation = rfqVal == 0.0 ? 0.0 : (variance / rfqVal) * 100;

    // Populate MarginDataDTO
    MarginDataDTO dto = new MarginDataDTO();
    dto.setCostType(type);
    dto.setRfqValue(rfqVal);
    dto.setInvoiceValue(invoiceVal);
    dto.setDeviation(deviation);
    dto.setVariance(variance);

    return dto;
  }

  private double defaultDouble(Double d) {
    return d != null ? d : 0.0;
  }

  private double parseDouble(String s) {
    try {
      return s == null ? 0.0 : Double.parseDouble(s);
    } catch (NumberFormatException e) {
      return 0.0;
    }
  }

}
