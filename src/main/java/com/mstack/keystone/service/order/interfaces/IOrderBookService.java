package com.mstack.keystone.service.order.interfaces;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.HardAssignPoRequest;
import com.mstack.keystone.model.repository.order.OrderBook;
import org.springframework.data.domain.Page;

import java.util.List;

public interface IOrderBookService {
    OrderBook createOrderBook(OrderBook orderBook);
    OrderBook getOrderBookById(String orderBookId);
    OrderBook getByPONumber(String poNumber);
    OrderBook updateOrderBook(String orderBookId, OrderBook updatedOrderBook);
    void deleteOrderBook(String orderBookId);
    Page<OrderBook> filterOrderBook(FilterRequest filterRequest);
    OrderBook getByPONumberAndCustomerId(String poNumber,String customerId);

    List<OrderBook> getCustomerRelatedOrders(String customerId);

    void hardAssignPoNumber(HardAssignPoRequest hardAssignPoRequest) throws ServiceException;
}
