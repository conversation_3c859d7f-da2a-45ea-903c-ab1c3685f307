package com.mstack.keystone.service.order;

import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ObjectNotFoundException;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.ActivitiesRequest;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.LinkedOrder;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.enums.BatchType;
import com.mstack.keystone.model.enums.OBBatchType;
import com.mstack.keystone.model.enums.OrderStatus;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.inventory.OBProductBatch;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.*;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.inventory.InventoryTransactionRepo;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.order.SupplierOrderBookRepo;
import com.mstack.keystone.repository.order.SupplierOrderRepository;
import com.mstack.keystone.service.criticalPath.CriticalPathService;
import com.mstack.keystone.service.inventory.InventoryProductService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;

import java.util.*;
import java.util.function.Function;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SupplierOrderServiceImpl implements SupplierOrderService {

    @Autowired
    private SupplierOrderRepository supplierOrderRepository;

    @Autowired
    private SupplierOrderBookRepo supplierOrderBookRepo;
    @Autowired
    private ProductBatchDetailRepository productBatchDetailRepository;
    @Autowired
    InventoryTransactionRepo inventoryTransactionRepo;

    @Autowired
    CustomerOrderService customerOrderService;

    @Autowired
    OrderBookRepository orderBookRepository;

    @Autowired
    InventoryProductService inventoryProductService;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    CriticalPathService criticalPathService;

    private static final Map<String, Function<SupplierOrder, Date>> DUE_DATE_FIELDS_MAP = Map.of(
        "etaForDestination", SupplierOrder::getEtaForDestination, "supplierDispatchedOn",
        SupplierOrder::getSupplierDispatchedOn, "dispatchFromOrigin",
        SupplierOrder::getDispatchFromOrigin, "dispatchFromDestinationOn",
        SupplierOrder::getDispatchFromDestinationOn, "arrivalDateAtOrigin",
        SupplierOrder::getArrivalDateAtOrigin, "dispatchToDestinationDate",
        SupplierOrder::getDispatchToDestinationDate
    );


    @Override
    public List<SupplierOrder> getAllNonDeletedSupplierOrders() {
        return supplierOrderRepository.findByDeletedFalse();
    }

    @Override
    public Optional<SupplierOrder> getSupplierOrderById(String id) {
        return supplierOrderRepository.findById(id);
    }

    @Override
    public List<SupplierOrder> getSupplierOrdersByCustomerOrderBookId(String id) {
        List<SupplierOrderBook> supplierOrderBookList = supplierOrderBookRepo.findByCustomerOrderBookId(id);
        List<SupplierOrder> supplierOrdersList = new ArrayList<>();
        supplierOrderBookList.forEach(supplierOrderBook -> {
            List<SupplierOrder> supplierOrders = supplierOrderRepository.findSupplierOrdersByOrderBookId(supplierOrderBook.getId());
            if (supplierOrders != null && !supplierOrders.isEmpty())
                supplierOrdersList.addAll(supplierOrders);
        });
        return supplierOrdersList;
    }

    @Override
    @SneakyThrows
    @Transactional
    public SupplierOrder createSupplierOrder(SupplierOrder supplierOrder) {
        if (supplierOrder.getLinkedSupplierOrderBookId() == null || supplierOrder.getLinkedSupplierOrderBookId().isEmpty()) {
            throw new ServiceException("Linked order book id cannot be empty ", 400);
        }
        SupplierOrderBook supplierOrderBook = supplierOrderBookRepo.findById(supplierOrder.getLinkedSupplierOrderBookId()).orElse(null);
        if (supplierOrderBook == null) throw new ServiceException("Invalid linked order book id provided ", 400);
        supplierOrder.setSupplier(supplierOrderBook.getSupplier());
        for (OrderedProduct product : supplierOrder.getProducts()) {
            //TODO PS Uncomment this
//            if (product.getPackaging() == null || product.getPackaging().getPackagingId() == null)
//                throw new ServiceException("Packaging id cannot be null for " + product.getId(), 400);
            product.setStatus(OrderStatus.INITIATED);
            // Migrate otherPackagingDetails if present at product level
            if (product.getPackaging() != null && product.getOtherPackagingDetails() != null) {
                product.getPackaging().setOtherPackagingDetails(product.getOtherPackagingDetails());
                product.setOtherPackagingDetails(null);
            }
        }
        supplierOrder.setStatus(OrderStatus.INITIATED);
        supplierOrder.setOrderId(commonUtils.CreateID("SDO", mongoQueries.getCount(supplierOrderRepository)));
        supplierOrder= mongoQueries.saveEntity(supplierOrderRepository, supplierOrder);
        createProductBatches(supplierOrder);
        if (supplierOrderBook.getLinkedCOBId() != null) {
            OrderBook orderBook = orderBookRepository
                .findByOrderBookId(supplierOrderBook.getLinkedCOBId())
                .stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException(
                    "OrderBook not found with id: " + supplierOrderBook.getLinkedCOBId(),
                    HttpStatus.NOT_FOUND.value()));

            HashMap<String, Date> dueDateFields = new HashMap<>();
            SupplierOrder finalSupplierOrder = supplierOrder;
            DUE_DATE_FIELDS_MAP.forEach((key, getter) -> {
                dueDateFields.put(key, getter.apply(finalSupplierOrder));
            });
            String templateName =
                orderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "cdoFlow"
                    : "sampleCdoFlow";
            ActivitiesRequest activitiesRequest = new ActivitiesRequest().builder()
                .orderId(orderBook.getOrderBookId())
                .categoryName("SUPPLIER_DISPATCH_ORDER")
                .entityId(supplierOrder.getOrderId())
                .templateName(templateName)
                .dueDateFields(dueDateFields)
                .entityName(AppConstants.SUPPLIER_ORDER)
                .category(orderBook.getOrderBookId())
                .build();
            criticalPathService.createOrUpdateTask(activitiesRequest);
        }
        return supplierOrder;
    }

    private void updateInventoryProductTransaction(SupplierOrder supplierOrder){
        // if even one dispatch order has inventory not == null it means orderbook under which SDO is being created has to create a transaction for inventory
        String customerDispatchOrderId=supplierOrder.getProducts().get(0).getLinkedOrders().get(0).getId();
        CustomerOrder customerOrder=customerOrderService.getCustomerOrderById(customerDispatchOrderId);
        if(customerOrder.getInventoryId()==null||customerOrder.getInventoryId().isEmpty()){
            return;
        }
        List<OrderedProduct> orderedProducts=supplierOrder.getProducts();
        // first check that the quantity being reduced is equal to or less than unassigned for all products . Create a function for the same and call before update.
        // Also check if the batch details have been given for the same if yes then dont let the quantity to be reduced
        for (OrderedProduct orderedProduct:orderedProducts) {
            //TODO - Gaurav
        }
    }

    private void createProductBatches(SupplierOrder supplierOrder) {
        supplierOrder.getProducts().forEach(orderedProduct -> {
            createProductBatch(orderedProduct, supplierOrder.getId());
        });
    }

    private void createProductBatch(OrderedProduct orderedProduct, String supplierOrderId) {
        ProductBatchDetail productBatchDetail = new ProductBatchDetail();
        productBatchDetail.setUnits(orderedProduct.getUnits());
        productBatchDetail.setProductId(orderedProduct.getProduct().getId());
        productBatchDetail.setUnitOfMeasure(orderedProduct.getUom());
        productBatchDetail.setSupplierOrderDispatchId(supplierOrderId);
        productBatchDetail.setPackagingId(orderedProduct.getPackaging().getId());
        // THIS RESEMBELS THAT ACTUAL BATCH DETAIL ISN'T PRESENT
        productBatchDetail.setType(BatchType.VIRTUAL_UNALLOCATED.getValue());
        // TODO not sure if this write there should be a loop
        CustomerOrder order = customerOrderService.getCustomerOrderById(orderedProduct.getLinkedOrders().get(0).getOrderId());
        // assuming order product is linked with only one customer order
        if (order.getInventoryId() != null) {
            productBatchDetail.setInventoryId(order.getInventoryId());
            productBatchDetail.setInventoryInOrderId(orderedProduct.getLinkedOrders().get(0).getOrderId());
            productBatchDetail.setType(BatchType.VIRTUAL_UNALLOCATED.getValue());

            // update inventory
//            inventoryProductService.addOrUpdateProductFromOrder(productBatchDetail, order.getInventoryId());
        } else {
            productBatchDetail.setCustomerOrderDispatchId(orderedProduct.getLinkedOrders().get(0).getOrderId());
            productBatchDetail.setType(BatchType.VIRTUAL_ALLOCATED.getValue());
        }
        // batch detail repository
        mongoQueries.saveEntity(productBatchDetailRepository, productBatchDetail);
    }

    @Override
    @SneakyThrows
    public SupplierOrder updateSupplierOrder(String id, SupplierOrder supplierOrder) {
        SupplierOrder existingSupplierOrder = supplierOrderRepository.findById(id)
            .orElseThrow(() -> new ServiceException("No entity with id " + id,
                HttpStatus.NOT_FOUND.value()));
        SupplierOrderBook supplierOrderBook = supplierOrderBookRepo.findById(
                supplierOrder.getLinkedSupplierOrderBookId())
            .orElseThrow(() -> new ServiceException("SupplierOrderBook not found with id: "
                + supplierOrder.getLinkedSupplierOrderBookId(), HttpStatus.NOT_FOUND.value()));
        // Migrate otherPackagingDetails if present at product level
        if (supplierOrder.getProducts() != null) {
            for (OrderedProduct product : supplierOrder.getProducts()) {
                if (product.getPackaging() != null && product.getOtherPackagingDetails() != null) {
                    product.getPackaging().setOtherPackagingDetails(product.getOtherPackagingDetails());
                    product.setOtherPackagingDetails(null);
                }
            }
        }

        HashMap<String, Date> dueDateFields = new HashMap<>();
        DUE_DATE_FIELDS_MAP.forEach((key, getter) -> {
            Date oldValue = getter.apply(existingSupplierOrder);
            Date newValue = getter.apply(supplierOrder);
            if (!Objects.equals(oldValue, newValue)) {
                dueDateFields.put(key, newValue);
            }
        });
        if (supplierOrderBook.getLinkedCOBId() != null) {
            OrderBook orderBook = orderBookRepository
                .findByOrderBookId(supplierOrderBook.getLinkedCOBId())
                .stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException(
                    "OrderBook not found with id: " + supplierOrderBook.getLinkedCOBId(),
                    HttpStatus.NOT_FOUND.value()));
            if (!dueDateFields.isEmpty()) { // Only create/update task if there's a change
                String templateName =
                    orderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "cdoFlow"
                        : "sampleCdoFlow";
                ActivitiesRequest activitiesRequest = ActivitiesRequest.builder()
                    .orderId(orderBook.getOrderBookId())
                    .categoryName("SUPPLIER_DISPATCH_ORDER")
                    .entityId(supplierOrder.getOrderId())
                    .templateName(templateName)
                    .dueDateFields(dueDateFields)
                    .entityName(AppConstants.SUPPLIER_ORDER)
                    .category(orderBook.getCategory())
                    .build();
                criticalPathService.createOrUpdateTask(activitiesRequest);
            }
        }
        return mongoQueries.updateEntity(supplierOrderRepository, id, supplierOrder);
    }

    @Override
    public Page<SupplierOrder> filterSupplierOrders(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<SupplierOrder> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            SupplierOrder.class
        );
        return page;
    }

    @Override
    @SneakyThrows
    public void deleteSupplierOrder(String id) {
        SupplierOrder supplierOrder = supplierOrderRepository.findById(id).orElse(null);
        if (supplierOrder == null) throw new ServiceException("Invalid supplier order id provided ", 400);
//        if (supplierOrder.isDeleted()) throw new ServiceException("Supplier order has already been deleted ", 400);
        validateDeleteRequest(supplierOrder);
        // if no batches are allocated then delete
        deleteBatches(id);
        supplierOrder.softDelete();
        supplierOrderRepository.save(supplierOrder);
    }

    private void validateDeleteRequest(SupplierOrder supplierOrder) throws ServiceException {
        for (OrderedProduct orderedProduct : supplierOrder.getProducts()) {
            List<LinkedOrder> linkedOrders = orderedProduct.getLinkedOrders();
            for (LinkedOrder linkedOrder : linkedOrders) {
                CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(linkedOrder.getOrderId());
                // if linked order is of inventory
                if (customerOrder.getInventoryId() != null) {
                    List<ProductBatchDetail> linkedAllocatedBatches = getVirtualAllocatedBatchesBySupplierOrderId(supplierOrder.getId());
                    for (ProductBatchDetail productBatchDetail : linkedAllocatedBatches) {
                        if (productBatchDetail.getCustomerOrderDispatchId() != null) {
                            CustomerOrder mappedOrder = customerOrderService.getCustomerOrderById(productBatchDetail.getCustomerOrderDispatchId());
                            throw new ServiceException("Supplier order batches are mapped with " + mappedOrder.getOrderId(), 400);
                        }
                    }
                }
            }
        }
    }

    private void deleteBatches(String id) {
        deleteVirtualBatches(id);
        deleteActualBatches(id);
    }

    private void deleteActualBatches(String supplierOrderId) {
        Map<String, List<Object>> query = Map.ofEntries(
                Map.entry("supplierOrderDispatchId", List.of(supplierOrderId)),
                Map.entry("deleted", List.of(false)),
                Map.entry("type", List.of(BatchType.UN_ALLOCATED.getValue(), BatchType.ALLOCATED.getValue()))
        );
        List<ProductBatchDetail> productBatchDetails = mongoQueries.findByFields(query, null, ProductBatchDetail.class);
        productBatchDetails.forEach(productBatchDetail -> {
            productBatchDetail.softDelete();
            productBatchDetailRepository.save(productBatchDetail);
        });
    }

    private void deleteVirtualBatches(String supplierOrderId) {
        Map<String, List<Object>> query = Map.ofEntries(
                Map.entry("supplierOrderDispatchId", List.of(supplierOrderId)),
                Map.entry("deleted", List.of(false)),
                Map.entry("type", List.of(BatchType.VIRTUAL_UNALLOCATED.getValue(), BatchType.VIRTUAL_ALLOCATED.getValue()))
        );
        List<ProductBatchDetail> productBatchDetails = mongoQueries.findByFields(query, null, ProductBatchDetail.class);
        productBatchDetails.forEach(productBatchDetail -> {
            productBatchDetail.softDelete();
            productBatchDetailRepository.save(productBatchDetail);
        });
    }

    public List<ProductBatchDetail> getVirtualAllocatedBatchesBySupplierOrderId(String orderId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("type", List.of(BatchType.VIRTUAL_ALLOCATED)),
                Map.entry("supplierOrderDispatchId", List.of(orderId)),
                Map.entry("deleted", List.of(false))
        );
        return mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
    }


    @Override
    public HashMap<String, Object> validateProductQuantity(String poNumber, String productId, double productQuantity, String dispatchId) {
        // get all the orders for the po number
        //fix this TODO PS this should fetch by po number alone for supplier
        List<SupplierOrder> orders = supplierOrderRepository.findByOrderNumberOrPoNumber(poNumber, PageRequest.ofSize(10000)).stream().toList();
        // calculate the product amount
        double usedAmount = 0;
        for (SupplierOrder order : orders) {
            for (OrderedProduct orderedProduct : order.getProducts()) {
                if (orderedProduct.getId() != null && orderedProduct.getId().equals(productId) && !order.getId().equals(dispatchId)) {
                    usedAmount += orderedProduct.getQuantity();
                }
            }
        }
        double totalAmount = 0;
        SupplierOrderBook orderBook = supplierOrderBookRepo.findByPONumber(poNumber);
        for (OrderBookEntry orderedProduct : orderBook.getProducts()) {
            if (orderedProduct.getId() != null && orderedProduct.getId().equals(productId))
                totalAmount += orderedProduct.getQuantity();
        }
        HashMap<String, Object> result = new HashMap<>();
        boolean isValid = !(productQuantity > totalAmount - usedAmount);
        result.put("isValid", isValid);
        result.put("availableQuantity", totalAmount - usedAmount);
        return result;
    }

    @Override
    public List<SupplierOrder> findAllLinkedOrders(String orderId) {
        return supplierOrderRepository.findAllOrdersWithDispatchOrderId(orderId);
    }

    @Override
    @SneakyThrows
    public List<SupplierOrder> getLinkedSupplierOrders(String customerOrderDispatchId) {
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(customerOrderDispatchId);
        if (customerOrder == null) throw new ServiceException("Invalid customer dispatch id provided", 400);
        List<List<String>> productPackagingList = getProductPackagingList(customerOrder);
        OrderBook orderBook = orderBookRepository.findByPONumber(customerOrder.getPurchaseOrderNumber());
        List<OBProductBatch> obProductBatches = mongoQueries.findByFields(getFilterForCustomerOrderBook(orderBook), null, OBProductBatch.class);
        List<SupplierOrder> supplierOrders = new ArrayList<>();
        obProductBatches.stream().map(obProductBatch -> supplierOrderBookRepo.findById(obProductBatch.getSupplierOrderBookId()).orElse(null))
                .map(supplierOrderBook -> (supplierOrderBook == null) ? null : supplierOrderRepository.findSupplierOrdersByOrderBookId(supplierOrderBook.getId())).filter(Objects::nonNull).flatMap(List::stream)
                .filter(supplierOrder -> {
                    // if supplier order has unallocated virtual batches then only share
                    List<ProductBatchDetail> productBatchDetails = findUnAllocatedProductBatches(supplierOrder.getId(), productPackagingList);
                    return !productBatchDetails.isEmpty();
                }).forEach(supplierOrders::add);
        return supplierOrders;
    }

    @Override
    public List<SupplierOrder> getAllSupplierOrdersBySOBId(String supplierOrderBookId) {
        return supplierOrderRepository.findSupplierOrdersByOrderBookId(supplierOrderBookId);
    }


    private List<ProductBatchDetail> findUnAllocatedProductBatches(String supplierOrderId, List<List<String>> productPackagingList) {
        List<ProductBatchDetail> list = new ArrayList<>();
        for (List<String> productPackage : productPackagingList) {
            Map<String, List<Object>> filter = Map.ofEntries(
                    Map.entry("supplierOrderDispatchId", List.of(supplierOrderId)),
                    Map.entry("productId", List.of(productPackage.get(0))),
                    Map.entry("packagingId", List.of(productPackage.get(1))),
                    Map.entry("type", List.of(BatchType.VIRTUAL_UNALLOCATED.getValue())),
                    Map.entry("deleted", List.of(false)));
            List<ProductBatchDetail> productBatchDetails = mongoQueries.findByFields(filter, null, ProductBatchDetail.class);
            list.addAll(productBatchDetails);
        }
        return list;
    }

    private List<List<String>> getProductPackagingList(CustomerOrder customerOrder) {
        List<List<String>> list = new ArrayList<>();
        customerOrder.getProducts().forEach(orderedProduct -> {
            list.add(List.of(orderedProduct.getProduct().getId(), orderedProduct.getPackaging().getId()));
        });
        return list;
    }

    private Map<String, List<Object>> getFilterForCustomerOrderBook(OrderBook orderBook) {
        return Map.ofEntries(
                Map.entry("customerOrderBookId", List.of(orderBook.getId())),
                Map.entry("type", List.of(OBBatchType.ALLOCATED.getValue())),
                Map.entry("deleted", List.of(Boolean.FALSE))
        );
    }

}
