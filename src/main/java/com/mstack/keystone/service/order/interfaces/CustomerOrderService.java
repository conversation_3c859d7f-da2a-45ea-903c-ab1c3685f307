package com.mstack.keystone.service.order.interfaces;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.InvoiceNumberGenerationRequest;
import com.mstack.keystone.model.repository.order.CustomerOrder;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

public interface CustomerOrderService {
    List<CustomerOrder> getAllNonDeletedCustomerOrders();
    CustomerOrder getCustomerOrderById(String id);

    List<CustomerOrder> getCustomerOrderByOrderBookId(String id);

    CustomerOrder createCustomerOrder(CustomerOrder customerOrder);
    Page<CustomerOrder> filterCustomerOrders(FilterRequest filterRequest);
    CustomerOrder updateCustomerOrder(String id, CustomerOrder customerOrder);
    void deleteCustomerOrder(String id);
    List<CustomerOrder> getIncompletedOrdersForCustomer(String id);
    //    Optional<CustomerOrder> linkCustomerIds(String id, List<String> supplierOrderIds);
    Page<CustomerOrder> findCustomerOrderByPoNumberOrOrderNumber(String searchText, Pageable pageable);

    List<CustomerOrder> findCustomerOrderByPoNumber(String poNumber);

    void exportCompleteData();
    String getPlaceOfOrigin(CustomerOrder customerOrder);
    void updateDraftDocuments(String orderId, String fileName, MultipartFile file);
    void updateDraftDocuments(String orderId, String fileName, String fileId) throws ServiceException;

    HashMap<String, Object> validateProductQuantity(String poNumber, String productId, double productQuantity, String dispatchId, String customerId, String inventoryId);

    void generateDispatchOrderSummarySheet() throws IOException;
    void moveToInventory(HashMap<String,Object> body);

    void generateInvoiceNumber(InvoiceNumberGenerationRequest body) throws ServiceException;
}
