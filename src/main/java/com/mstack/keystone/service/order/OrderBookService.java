package com.mstack.keystone.service.order;

import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.ActivitiesRequest;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.HardAssignPoRequest;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.Audit;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.inventory.Inventory;
import com.mstack.keystone.model.repository.inventory.OBProductBatch;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.OrderBookEntry;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.ActivityRepository;
import com.mstack.keystone.repository.AuditRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.inventory.InventoryRepository;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.order.SupplierOrderBookRepo;
import com.mstack.keystone.service.criticalPath.CriticalPathService;
import com.mstack.keystone.service.order.interfaces.IOrderBookService;
import com.mstack.keystone.utils.CommonUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class OrderBookService implements IOrderBookService {

    @Autowired
    OrderBookRepository orderBookRepository;

    @Autowired
    InventoryRepository inventoryRepository;
    @Autowired
    SupplierOrderBookRepo supplierOrderBookRepo;

    @Autowired
    CustomerOrderRepository customerOrderRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    CriticalPathService criticalPathService;

    @Autowired
    AuditRepository auditRepository;

    @Autowired
    ActivityRepository activityRepository;

    private static final Map<String, Function<OrderBook, Date>> ORDER_BOOK_DUE_DATE_FIELDS = Map.of(
        "expectedDeliveryDate", orderBook ->
            orderBook.getProducts().isEmpty() ? null
                : orderBook.getProducts().get(0).getExpectedDeliveryDate(),

        "purchaseOrderDate", OrderBook::getPurchaseOrderDate
    );

    private static final List<String> WAREHOUSE_NAMES = List.of("US Warehouse","India Warehouse","China Warehouse");

    @Override
    @SneakyThrows
    public OrderBook createOrderBook(OrderBook orderBook) {
        String prefix = orderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "COB" : "SAMPLEOB";
        orderBook.setOrderBookId(commonUtils.CreateID(prefix, mongoQueries.getCount(orderBookRepository)));
        for (OrderBookEntry entry : orderBook.getProducts()) {
            entry.setId(UUID.randomUUID().toString());
        }
        //  check for inventoryid
        if (orderBook.getInventoryId() != null && orderBook.getCustomer() != null) {
            throw new ServiceException("Order Book Cannot be placed for both customer and inventory at the same time ", 400);
        }
        if (orderBook.getInventoryId() != null) {
            // if its any inventory order generate po number
            orderBook.setPurchaseOrderDate(new Date());
            orderBook.setPurchaseOrderNumber(getPurchaseOrderNumberForInventory());
        }
        if (orderBook.getOrderType().equals(OrderType.SAMPLE) && orderBook.getCustomer() != null) {
            if (WAREHOUSE_NAMES.contains(orderBook.getCustomer().getName())) {
                orderBook.setPurchaseOrderNumber(orderBook.getPurchaseOrderNumber() + "_WH");
            }
        }
        Map<String, Date> dueDateFields = new HashMap<>();
        ORDER_BOOK_DUE_DATE_FIELDS.forEach((key, extractor) -> {
            dueDateFields.put(key, extractor.apply(orderBook));
        });
        dueDateFields.put("poGenerationDate", new Date());
        String templateName =
            orderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "poFlow" : "samplePoFlow";
        ActivitiesRequest activitiesRequest = new ActivitiesRequest().builder()
            .orderId(orderBook.getOrderBookId())
            .categoryName("PURCHASE_ORDER")
            .entityId(orderBook.getOrderBookId())
            .templateName(templateName)
            .dueDateFields(dueDateFields)
            .entityName(AppConstants.ORDER_BOOK)
            .secondaryId(orderBook.getPurchaseOrderNumber())
            .orderType(orderBook.getOrderType().name())
                .category(orderBook.getCategory())
            .customerName(orderBook.getCustomer().getName())
            .productName(Optional.ofNullable(orderBook.getProducts().get(0)).map(
                OrderBookEntry::getProduct).map(Product::getTradeName).orElse(null))
            .build();
        criticalPathService.createOrUpdateTask(activitiesRequest);
        Audit audit = new Audit().builder().entity(orderBook).entityType("orderbook").build();
        mongoQueries.saveEntity(auditRepository, audit);
        return mongoQueries.saveEntity(orderBookRepository, orderBook);
    }

    private String getPurchaseOrderNumberForInventory(){
        // isn't work hence commenting out
//        long count=orderBookRepository.countOfInventoryOrderbook();
        return commonUtils.CreateID("POINV", mongoQueries.countEntityByCriteria(OrderBook.class, List.of(
                Criteria.where("inventoryId").exists(true).andOperator(Criteria.where("inventoryId").ne(null))
        )));
    }

    @Override
    @SneakyThrows
    public OrderBook getOrderBookById(String orderBookId) {
        OrderBook orderbook= mongoQueries.getEntity(orderBookRepository, orderBookId);
        if(orderbook.getInventoryId()!=null){
            Inventory inventory=mongoQueries.getEntity(inventoryRepository,orderbook.getInventoryId());
            orderbook.setCustomer(new Customer(inventory.getName()));
            orderbook.setInventory(inventory);
        }
        return  orderbook;
    }

    @Override
    public OrderBook getByPONumber(String poNumber) {
        return orderBookRepository.findByPONumber(poNumber);
    }

    @Override
    @SneakyThrows
    public OrderBook updateOrderBook(String orderBookId, OrderBook updatedOrderBook) {
        for (OrderBookEntry entry : updatedOrderBook.getProducts()) {
            if (entry.getId() == null) {
                entry.setId(UUID.randomUUID().toString());
            }
        }
        OrderBook existingOrderBook = orderBookRepository.findById(orderBookId).orElseThrow(
            () -> new ServiceException("OrderBook not found with id: " + orderBookId,
                HttpStatus.NOT_FOUND.value()));

        // Check if purchaseOrderNumber has changed
        if (!Objects.equals(existingOrderBook.getPurchaseOrderNumber(), updatedOrderBook.getPurchaseOrderNumber())) {
            String oldPoNumber = existingOrderBook.getPurchaseOrderNumber();
            String newPoNumber = updatedOrderBook.getPurchaseOrderNumber();
            
            // Update customer orders
            List<CustomerOrder> customerOrders = customerOrderRepository.findOrdersBycustomerIdAndPoNumber(
                existingOrderBook.getCustomer().getId(), 
                oldPoNumber
            );
            if (customerOrders != null && !customerOrders.isEmpty()) {
                customerOrders.forEach(customerOrder -> {
                    System.out.println("Updating customer order " + customerOrder.getId() + " from PO " + customerOrder.getPurchaseOrderNumber() + " to " + newPoNumber);
                    customerOrder.setPurchaseOrderNumber(newPoNumber);
                    mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
                });
                System.out.println("Successfully updated " + customerOrders.size() + " customer orders");
            }

            // Update activity records
            List<Activity> activities = activityRepository.findBySecondaryId(oldPoNumber);
            if (!activities.isEmpty()) {
                activities.forEach(activity -> {
                    System.out.println("Updating activity " + activity.getId() + " from secondaryId " + activity.getSecondaryId() + " to " + newPoNumber);
                    activity.setSecondaryId(newPoNumber);
                });
                List<Activity> savedActivities = activityRepository.saveAll(activities);
                System.out.println("Successfully saved " + savedActivities.size() + " activities");
            }
        }

        HashMap<String, Date> dueDateFields = new HashMap<>();

        // Iterate over the map and check for changes dynamically
        OrderBook finalUpdatedOrderBook = updatedOrderBook;
        ORDER_BOOK_DUE_DATE_FIELDS.forEach((key, extractor) -> {
            Date oldValue = extractor.apply(existingOrderBook);
            Date newValue = extractor.apply(finalUpdatedOrderBook);
            if (!Objects.equals(oldValue, newValue)) {
                dueDateFields.put(key, newValue);
            }
        });

        // Only create/update task if there are changes
        if (!dueDateFields.isEmpty()) {
            String templateName =
                updatedOrderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "poFlow"
                    : "samplePoFlow";
            ActivitiesRequest activitiesRequest = ActivitiesRequest.builder()
                .orderId(updatedOrderBook.getOrderBookId())
                .categoryName("PURCHASE_ORDER")
                .entityId(updatedOrderBook.getOrderBookId())
                .templateName(templateName)
                .dueDateFields(dueDateFields)
                .entityName(AppConstants.ORDER_BOOK)
                .secondaryId(updatedOrderBook.getPurchaseOrderNumber())
                .orderType(updatedOrderBook.getOrderType().name())
                .category(updatedOrderBook.getCategory())
                .build();
            criticalPathService.createOrUpdateTask(activitiesRequest);
        }

        Audit audit = new Audit().builder().entity(updatedOrderBook).entityType("orderbook").build();
        mongoQueries.saveEntity(auditRepository, audit);
        updatedOrderBook = mongoQueries.updateEntity(orderBookRepository, orderBookId, updatedOrderBook);
        
        // Check if isOnhold status has changed
        if (existingOrderBook.isOnHold() != updatedOrderBook.isOnHold()) {
            // Update all activities for this orderId
            List<Activity> activities = activityRepository.findByOrderId(updatedOrderBook.getOrderBookId());
            if (!activities.isEmpty()) {
                activities.forEach(activity -> {
                    activity.setOnHold(finalUpdatedOrderBook.isOnHold());
                });
                activityRepository.saveAll(activities);
            }
        }
        return updatedOrderBook;
    }

    @Override
    public void deleteOrderBook(String orderBookId) {
        validateDeleteRequest(orderBookId);
        mongoQueries.softDeleteById(orderBookRepository, orderBookId, OrderBook.class);
    }

    @SneakyThrows
    private void validateDeleteRequest(String id) {
        Pageable pageable = PageRequest.of(0, 10000);
        OrderBook orderBook = orderBookRepository.findById(id).orElse(null);
        if (orderBook == null) throw new ServiceException("Invalid order id provided ", 400);

        // find related customer orders
        Page<CustomerOrder> customerOrders = mongoQueries.findByFiltersAndSearch(null, new HashMap<>() {{
            put("purchaseOrderNumber", orderBook.getPurchaseOrderNumber());
        }}, pageable, CustomerOrder.class);
        if (!customerOrders.isEmpty())
            throw new ServiceException("Customer order's exist for given order book", 422);
        //  check if any supplier po's exist
        List<SupplierOrderBook> supplierOrderBooks = mongoQueries.findByFields(new HashMap<>() {{
            put("linkedCOBs", List.of(orderBook.getId()));
            put("deleted", List.of(false));
        }}, null, SupplierOrderBook.class);
        if (!supplierOrderBooks.isEmpty())
            throw new ServiceException("Supplier PO's exist for given order book", 422);
        if (orderBook.getInventoryId() == null) {
            // if order book is not an inventory order handle it
            // if any allocated inventory exists
            List<OBProductBatch> obProductBatches = mongoQueries.findByFields(Map.ofEntries(
                    Map.entry("customerOrderBookId", List.of(orderBook.getId())),
                    Map.entry("deleted", List.of(false))
            ), null, OBProductBatch.class);
            if (obProductBatches != null && !obProductBatches.isEmpty()) {
                SupplierOrderBook supplierOrderBook = supplierOrderBookRepo.findById(obProductBatches.get(0).getSupplierOrderBookId()).orElse(null);
                if (supplierOrderBook == null)
                    throw new ServiceException("cannot found supplier order book for ob batch id " + obProductBatches.get(0).getId(), 500);
                throw new ServiceException("Inventory is already allocated for given order with mapped supplier order  " + supplierOrderBook.getOrderBookId(), 400);
            }
        }

    }


    public Page<OrderBook> filterOrderBook(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<OrderBook> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            OrderBook.class
        );

        List<OrderBook> orderBookList=page.stream().toList() ;
        List<String> uniqueInventoryIds = orderBookList.stream()
                .filter(orderBook -> orderBook.getInventoryId() != null)
                .map(OrderBook::getInventoryId)
                .collect(Collectors.toList());
        List<Inventory> inventories=inventoryRepository.findAllById(uniqueInventoryIds);
        Map<String, Inventory> inventoryMap = inventories.stream()
                .collect(Collectors.toMap(
                        Inventory::getId,  // Key: Inventory ID
                        inventory -> inventory       // Value: Inventory object
                ));
        for (OrderBook orderbook:orderBookList) {
            if(orderbook.getInventoryId()!=null){
                // remove below line once tushar makes frontend changes
                orderbook.setInventory(inventoryMap.get(orderbook.getInventoryId()));
                orderbook.setCustomer(new Customer(inventoryMap.get(orderbook.getInventoryId()).getName()));
            }
        }
        page = new PageImpl<>(orderBookList, pageable, page.getTotalElements());
        return page;
    }

    @Override
    public OrderBook getByPONumberAndCustomerId(String poNumber, String customerId) {
        ObjectId customerObjId=new ObjectId(customerId);
        return orderBookRepository.findByPONumberAndCustomerId(poNumber,customerObjId);
    }

    @Override
    public List<OrderBook> getCustomerRelatedOrders(String customerId) {
        return orderBookRepository.findByCusotmerId(new ObjectId(customerId));
    }


    public void hardAssignPoNumber(HardAssignPoRequest hardAssignPoRequest) throws ServiceException {
        validateHardAssignRequest(hardAssignPoRequest);
        OrderBook orderBook = orderBookRepository.findByPONumberAndCustomerId(hardAssignPoRequest.getOldPoNumber(), new ObjectId(hardAssignPoRequest.getCustomerId()));
        OrderBook orderBook1 = orderBookRepository.findByPONumberAndCustomerId(hardAssignPoRequest.getUpdatedPoNumber(), new ObjectId(hardAssignPoRequest.getCustomerId()));
        if (orderBook1 != null)
            throw new ServiceException("New po number is already assigned to order book " + orderBook1.getOrderBookId() + " use a different po number", 400);
        List<CustomerOrder> customerOrders = customerOrderRepository.findOrdersBycustomerIdAndPoNumber(hardAssignPoRequest.getCustomerId(), hardAssignPoRequest.getOldPoNumber());
        if (orderBook == null) throw new ServiceException("No order book found for given po ", 400);
        orderBook.setPurchaseOrderNumber(hardAssignPoRequest.getUpdatedPoNumber());
        mongoQueries.updateEntity(orderBookRepository, orderBook.getId(), orderBook);
        if (!(customerOrders == null || customerOrders.isEmpty())) {
            // update inside customer orders
            customerOrders.forEach(customerOrder -> {
                customerOrder.setPurchaseOrderNumber(hardAssignPoRequest.getUpdatedPoNumber());
                mongoQueries.updateEntity(customerOrderRepository, customerOrder.getId(), customerOrder);
            });
        }
    }

    private void validateHardAssignRequest(HardAssignPoRequest hardAssignPoRequest) throws ServiceException {
        if (commonUtils.isNullOrEmpty(hardAssignPoRequest.getCustomerId()) || commonUtils.isNullOrEmpty(hardAssignPoRequest.getUpdatedPoNumber()) || commonUtils.isNullOrEmpty(hardAssignPoRequest.getOldPoNumber())) {
            throw new ServiceException("Customer id or old po number or new po number cannot be empty ", 400);
        }
    }
}
