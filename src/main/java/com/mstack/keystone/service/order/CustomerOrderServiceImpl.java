package com.mstack.keystone.service.order;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.enums.BatchType;
import com.mstack.keystone.model.enums.OrderStatus;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.Accounting.Invoice;
import com.mstack.keystone.model.repository.inventory.InventoryOutOrder;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.*;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.order.SupplierOrderRepository;
import com.mstack.keystone.service.accounting.InvoiceService;
import com.mstack.keystone.service.criticalPath.CriticalPathService;
import com.mstack.keystone.service.document.DocumentService;
import com.mstack.keystone.service.criticalPath.interfaces.ActivityService;
import com.mstack.keystone.service.inventory.InventoryProductService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.utils.CommonUtils;

import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;

import com.opencsv.CSVWriter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class CustomerOrderServiceImpl implements CustomerOrderService {

    @Autowired
    private CustomerOrderRepository customerOrderRepository;

    @Autowired
    private OrderBookRepository orderBookRepository;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;
    @Autowired
    OrderBookService orderBookService;

    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    SupplierOrderRepository supplierOrderRepository;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    RequestConfig requestConfig;
    @Autowired
    DocReviewService docReviewService;
    @Autowired
    DocumentService documentService;
    @Autowired
    InventoryProductService inventoryProductService;
    @Lazy
    @Autowired
    ActivityService activityService;
    @Lazy
    @Autowired
    InvoiceService invoiceService;

    @Autowired
    CriticalPathService criticalPathService;

    private static final Map<String, Function<CustomerOrder, Date>> CUSTOMER_ORDER_DUE_DATE_FIELDS = Map.of(
        "sampleDispatchedOn", CustomerOrder::getSampleDispatchedOn,
        "dispatchFromDestinationOn", CustomerOrder::getDispatchFromDestinationOn
    );


    @Override
    public List<CustomerOrder> getAllNonDeletedCustomerOrders() {
        return customerOrderRepository.findByDeletedFalse();
    }

    @Override
    @SneakyThrows
    public CustomerOrder getCustomerOrderById(String id) {
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, id);
        if (customerOrder == null) return null;
        if (customerOrder.getMeta() == null) {
            customerOrder.setMeta(new HashMap<>());
        }
        List<InventoryOutOrder> linkedInventoryOutOrders = getLinkedInventoryOutOrders(customerOrder);
        if (linkedInventoryOutOrders != null && !linkedInventoryOutOrders.isEmpty()) {
            customerOrder.getMeta().put("hasILO", true);
        } else customerOrder.getMeta().put("hasILO", false);
        return customerOrder;
    }

    @Override
    @SneakyThrows
    public List<CustomerOrder> getCustomerOrderByOrderBookId(String id) {
        Optional<OrderBook> orderBook = orderBookRepository.findById(id);
        if (orderBook.isEmpty()) {
            throw new ServiceException("order book id not found ", 404);
        }
        // get all orders with ponumber and customer order id
        if(orderBook.get().getInventoryId()==null){
            return customerOrderRepository.findOrdersBycustomerIdAndPoNumber(
                    orderBook.get().getCustomer().getId(),
                    orderBook.get().getPurchaseOrderNumber()
            );
        }
        return customerOrderRepository.findOrdersByInventoryIdAndPoNumber(
                orderBook.get().getInventoryId(),
                orderBook.get().getPurchaseOrderNumber()
        );
    }

    @Override
    @SneakyThrows
    public CustomerOrder createCustomerOrder(CustomerOrder customerOrder) {
        for (OrderedProduct product : customerOrder.getProducts()) {
            product.setStatus(OrderStatus.INITIATED);
            // Migrate otherPackagingDetails if present at product level
            if (product.getPackaging() != null && "Others".equalsIgnoreCase(product.getPackaging().getType())) {
                if (product.getOtherPackagingDetails() != null) {
                    product.getPackaging().setOtherPackagingDetails(product.getOtherPackagingDetails());
                    product.setOtherPackagingDetails(null);
                }
            }
        }
        customerOrder.setStatus(OrderStatus.INITIATED);
        ArrayList statusHistory = new ArrayList<>();
        HashMap map = new HashMap<>();
        map.put("status", OrderStatus.INITIATED);
        map.put("updatedBy", requestConfig.getEntityId());
        map.put("updatedAt", new Date());
        statusHistory.add(map);
        customerOrder.setStatusHistory(statusHistory);
        customerOrder.setLastUpdatedAt(new Date());
        // Check order book
        OrderBook orderBook = orderBookRepository.findByPONumber(customerOrder.getPurchaseOrderNumber());
        if (orderBook == null) throw new ServiceException("Invalid order id provided ", 400);
        customerOrder.setOrderId(commonUtils.CreateID("CDO", mongoQueries.getCount(customerOrderRepository)));
        if (orderBook.getInventoryId() != null) {
            customerOrder.setInventoryId(orderBook.getInventoryId());
        }
        //  check for inventoryid
        if (customerOrder.getInventoryId() != null && customerOrder.getCustomer() != null && customerOrder.getCustomer().getId() != null) {
            throw new ServiceException("Order Cannot be placed for both customer and inventory at the same time ", 400);
        }
        HashMap<String, Date> dueDateFields = new HashMap<>();
        dueDateFields.put("poGenerationDate", orderBook.getCreatedAt());
        dueDateFields.put("expectedDeliveryDate",  orderBook.getProducts().get(0).getExpectedDeliveryDate());
        CUSTOMER_ORDER_DUE_DATE_FIELDS.forEach((key, extractor) -> {
            dueDateFields.put(key, extractor.apply(customerOrder));
        });
        String templateName =
            orderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "cdoFlow" : "sampleCdoFlow";
        ActivitiesRequest activitiesRequest = new ActivitiesRequest().builder()
            .orderId(orderBook.getOrderBookId())
            .categoryName("CUSTOMER_DISPATCH_ORDER")
            .entityId(customerOrder.getOrderId())
            .templateName(templateName)
            .dueDateFields(dueDateFields)
            .entityName(AppConstants.CUSTOMER_ORDER)
            .orderType(orderBook.getOrderType().name())
            .secondaryId(orderBook.getPurchaseOrderNumber())
                .category(orderBook.getCategory())
            .build();
        criticalPathService.createOrUpdateTask(activitiesRequest);
        return mongoQueries.saveEntity(customerOrderRepository, customerOrder);
    }

    @Override
    public Page<CustomerOrder> filterCustomerOrders(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<CustomerOrder> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            CustomerOrder.class
        );
        return page;
    }

    @Override
    @SneakyThrows
    public CustomerOrder updateCustomerOrder(String id, CustomerOrder customerOrder) {
        for (OrderedProduct entry : customerOrder.getProducts()) {
            // Migrate otherPackagingDetails if present at product level
            if (entry.getPackaging() != null && "Others".equalsIgnoreCase(entry.getPackaging().getType())) {
                if (entry.getOtherPackagingDetails() != null) {
                    entry.getPackaging().setOtherPackagingDetails(entry.getOtherPackagingDetails());
                    entry.setOtherPackagingDetails(null);
                }
            }
            if (entry.getStatus()==null) {
                entry.setStatus(OrderStatus.INITIATED);
            }
        }
        CustomerOrder existingCustomerOrder = customerOrderRepository.findById(id).orElseThrow(()-> new ServiceException("Customer Order not exists with id"+ id, HttpStatus.NOT_FOUND.value()));
        HashMap<String,Date> dueDateFields = new HashMap<>();
        CUSTOMER_ORDER_DUE_DATE_FIELDS.forEach((key, extractor) -> {
            Date oldValue = extractor.apply(existingCustomerOrder);
            Date newValue = extractor.apply(customerOrder);
            if (!Objects.equals(oldValue, newValue)) {
                dueDateFields.put(key, newValue);
            }
        });
        if(!dueDateFields.isEmpty()){
            OrderBook orderBook = orderBookRepository.findByPONumber(customerOrder.getPurchaseOrderNumber());
            dueDateFields.put("expectedDeliveryDate",  orderBook.getProducts().get(0).getExpectedDeliveryDate());
            String templateName = orderBook.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "cdoFlow" : "sampleCdoFlow";
            ActivitiesRequest activitiesRequest = new ActivitiesRequest().builder().orderId(orderBook.getOrderBookId())
                    .categoryName("CUSTOMER_DISPATCH_ORDER")
                    .entityId(customerOrder.getOrderId())
                    .templateName(templateName)
                    .dueDateFields(dueDateFields)
                    .entityName(AppConstants.CUSTOMER_ORDER)
                    .orderType(orderBook.getOrderType().name())
                    .secondaryId(orderBook.getPurchaseOrderNumber())
                    .category(orderBook.getCategory())
                    .build();
            criticalPathService.createOrUpdateTask(activitiesRequest);
        }

        return mongoQueries.updateEntity(customerOrderRepository, id, customerOrder);
    }

    @Override
    public void deleteCustomerOrder(String id) {
        validateDeleteRequest(id);
        mongoQueries.softDeleteById(customerOrderRepository, id, CustomerOrder.class);
    }

    @SneakyThrows
    private void validateDeleteRequest(String id) {
        //  check if any supplier po's exist
        Pageable pageable = PageRequest.of(0, 10000);
        // find customer order book
        CustomerOrder customerOrder = customerOrderRepository.findById(id).orElse(null);
        if (customerOrder == null) throw new ServiceException("Invalid order id provided ", 400);
        OrderBook orderBook = orderBookRepository.findByPONumber(customerOrder.getPurchaseOrderNumber());
        // check if invoices are there
        Page<Invoice> invoices = mongoQueries.findByFiltersAndSearch(null, new HashMap<>() {{
            put("items.dispatchOrderId", customerOrder.getId());
            put("items.poNumber", customerOrder.getPurchaseOrderNumber());
        }}, pageable, Invoice.class);
        if (!invoices.isEmpty())
            throw new ServiceException("Invoices exist for given order", 422);

        List<SupplierOrder> supplierOrders = getLinkedSupplierOrders(customerOrder);
        if (supplierOrders != null && !supplierOrders.isEmpty())
            throw new ServiceException("Customer order is linked with supplier order " + supplierOrders.get(0).getOrderId(), 400);

        List<InventoryOutOrder> inventoryOutOrders = getLinkedInventoryOutOrders(customerOrder);
        if (inventoryOutOrders != null && !inventoryOutOrders.isEmpty())
            throw new ServiceException("Customer order is linked with Inventory out order " + inventoryOutOrders.get(0).getOrderId(), 400);
        // if any virtual ob batches are allocated
        List<ProductBatchDetail> productBatchDetails = mongoQueries.findByFields(getFilterQuery(customerOrder), null, ProductBatchDetail.class);
        for (ProductBatchDetail productBatchDetail : productBatchDetails) {
            List<SupplierOrder> mappedSupplierOrders = mongoQueries.findByFields(new HashMap<>() {{
                put("id", List.of(productBatchDetail.getSupplierOrderDispatchId()));
                put("deleted", List.of(false));
            }}, null, SupplierOrder.class);
//            if (!mappedSupplierOrders.isEmpty())
            throw new ServiceException("Customer order is linked with supplier order " + mappedSupplierOrders.get(0).getOrderId(), 400);
        }
    }

    private List<SupplierOrder> getLinkedSupplierOrders(CustomerOrder customerOrder) {
        return mongoQueries.findByFields(new HashMap<>() {{
            put("products.linkedOrders.orderId", List.of(customerOrder.getId()));
            put("deleted", List.of(false));
        }}, null, SupplierOrder.class);
    }

    private List<InventoryOutOrder> getLinkedInventoryOutOrders(CustomerOrder customerOrder) {
        return mongoQueries.findByFields(new HashMap<>() {{
            put("customerDispatchOrderId", List.of(customerOrder.getId()));
            put("deleted", List.of(false));
        }}, null, InventoryOutOrder.class);
    }

    private Map<String, List<Object>> getFilterQuery(CustomerOrder customerOrder) {
        if (customerOrder.getInventoryId() != null) {
            return Map.ofEntries(
                    Map.entry("inventoryInOrderId", List.of(customerOrder.getId())),
                    Map.entry("type", List.of(BatchType.VIRTUAL_ALLOCATED.getValue())),
                    Map.entry("deleted", List.of(false))
            );
        } else return Map.ofEntries(
                Map.entry("customerOrderDispatchId", List.of(customerOrder.getId())),
                Map.entry("type", List.of(BatchType.VIRTUAL_ALLOCATED.getValue())),
                Map.entry("deleted", List.of(false))
        );
    }

    @Override
    public List<CustomerOrder> getIncompletedOrdersForCustomer(String id) {
        List<CustomerOrder> orders=customerOrderRepository.findDispatchOrdersWithStatusNotCompletedAndCustomerId(new ObjectId(id));
        return orders;
    }

    @Override
    public Page<CustomerOrder> findCustomerOrderByPoNumberOrOrderNumber(String searchText,Pageable pageable) {
        return customerOrderRepository.findByOrderNumberOrPoNumber(searchText,pageable);
    }

    @Override
    public List<CustomerOrder> findCustomerOrderByPoNumber(String poNumber) {
        return customerOrderRepository.findOrdersByPoNumber(poNumber);
    }

    @Override
    public void exportCompleteData() {
        List<CustomerOrder> orders=customerOrderRepository.findByDeletedFalse();
        try (CSVWriter writer = new CSVWriter(new FileWriter("export.csv"))) {
            // Writing header
            String[] header = {"Name", "Age", "City"};
            writer.writeNext(header);

            // Writing data from the list of objects
            for (CustomerOrder order:orders) {
                String orderId=order.getOrderId();
                String poNumber=order.getPurchaseOrderNumber();
                String customerName=order.getCustomer().getName();
                String productNames="";
                Double totalValue=0.0;
                for (OrderedProduct product:order.getProducts()) {
                    productNames+=product.getProduct().getTradeName()+" |";
                    totalValue+=(product.getQuantity()*product.getPrice());
                }
                String incoterms=order.getIncoterms().getType().toString();
                if(incoterms.equals("CIF")){
                    incoterms+=(" -"+order.getIncoterms().getData().getOrDefault("portOfDischarge","").replace(",","|"));
                }
                else if(incoterms.equals("CFR")){
                    incoterms+=(" -"+order.getIncoterms().getData().getOrDefault("portOfDischarge","").replace(",","|"));

                }
                else if(incoterms.equals("DDP")){
                    incoterms+=(" -"+order.getIncoterms().getData().getOrDefault("city","").replace(",","|"));

                }
                else if (incoterms.equals("FOB")) {
                    incoterms+=(" -"+order.getIncoterms().getData().getOrDefault("portOfDischarge","").replace(",","|"));
                }
                // Specify the desired date format
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");

                // Format the Date object

                String shipmentDate ="";
                if(order.getShipmentDate()!=null){
                    shipmentDate=dateFormat.format(order.getShipmentDate());
                }
                String deliveryDate ="";
                if(order.getDeliveryDate()!=null){
                    deliveryDate=dateFormat.format(order.getDeliveryDate());
                }
                String invoiceDate="";
                if(order.getInvoiceDate()!=null){
                    invoiceDate=dateFormat.format(order.getInvoiceDate());
                }
                String status=order.getStatus().toString();
                String paymentTerms=order.getPaymentTerms().getCreditAmount()+"% ,"+
                        order.getPaymentTerms().getCreditorDays()+" days ,"+order.getPaymentTerms().getStartDate();
                String[] data = {orderId,poNumber,customerName,productNames,totalValue.toString(),incoterms,
                        shipmentDate,deliveryDate,status,invoiceDate,paymentTerms};
                writer.writeNext(data);
            }

            System.out.println("CSV file created successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getPlaceOfOrigin(CustomerOrder customerOrder) {
        List<SupplierOrder> orders=supplierOrderRepository.findAllOrdersWithDispatchOrderId(customerOrder.getId());
        if(orders!=null&&orders.size()>0){
            return orders.get(0).getSupplier().getAddress().getCountry();
        }
        return "";
    }

    //    TODO Remove if not required
    //    @Override
    //    @Transactional
    //    public Optional<CustomerOrder> linkCustomerIds(String customerOrderId, List<String> supplierOrderIds) {
    //        Optional<CustomerOrder> optionalCustomerOrder = customerOrderRepository.findById(customerOrderId);
    //
    //        if (optionalCustomerOrder.isPresent()) {
    //            CustomerOrder customerOrder = optionalCustomerOrder.get();
    //
    //            List<String> mergedSupplierOrderIds = new ArrayList<>(customerOrder.());
    //            mergedSupplierOrderIds.addAll(supplierOrderIds);
    //
    //            customerOrder.setSupplierDispatchIds(mergedSupplierOrderIds);
    //
    //            return Optional.of(customerOrderRepository.save(customerOrder));
    //        } else {
    //            return Optional.empty();
    //        }
    //    }
    @SneakyThrows
    public void updateDraftDocuments(String orderId, String fileName, MultipartFile file) {
        // check if order exists
        CustomerOrder order = customerOrderRepository.findById(orderId).orElse(null);
        if (order == null)
            throw new ServiceException("Order doesn't exist with given order id ", 400);
        // check if key with the given name exist
        if (!order.getDraftDocuments().containsKey(fileName))
            throw new ServiceException("File name doesn't exist ", 400);
        HashMap<String, Object> meta = new HashMap<>();
        HashMap<String, String> extraDetails = new HashMap<>();
        extraDetails.put("orderId", orderId);
        meta.put("meta", extraDetails);
        meta.put("category", "DraftDocuments");
        meta.put("name", file.getName());
        File uploadedFile = documentService.uploadFile(meta, file);
        // add in end of list
        order.getDraftDocuments().get(fileName).add(uploadedFile);
        // save dispatch order
        mongoQueries.saveEntity(customerOrderRepository, order);
        // mark comments as resolved
        int totalFilesSize = order.getDraftDocuments().get(fileName).size();
        docReviewService.markAllCommentsResolved(order.getDraftDocuments().get(fileName).get(totalFilesSize - 2).getFileId());
    }

    @Override
    public void updateDraftDocuments(String orderId, String fileName, String fileId) throws ServiceException {
        // check if order exists
        CustomerOrder order = customerOrderRepository.findById(orderId).orElse(null);
        if (order == null)
            throw new ServiceException("Order doesn't exist with given id ", 400);
        // check if key with the given name exist
        if (!order.getDraftDocuments().containsKey(fileName))
            throw new ServiceException("File name doesn't exist ", 400);
        File uploadedFile = new File();
        uploadedFile.setName(fileName);
        uploadedFile.setFileId(fileId);
        // add in end of list
        order.getDraftDocuments().get(fileName).add(uploadedFile);
        // save dispatch order
        mongoQueries.saveEntity(customerOrderRepository, order);
        // mark comments as resolved
        int totalFilesSize = order.getDraftDocuments().get(fileName).size();
        docReviewService.markAllCommentsResolved(order.getDraftDocuments().get(fileName).get(totalFilesSize - 2).getFileId());
    }

    @Override
    public HashMap<String, Object> validateProductQuantity(String poNumber, String productId, double productQuantity, String dispatchId, String customerId, String inventoryId) {
        List<CustomerOrder> orders = null;
        // get all the orders for the po number
        if (customerId == null)
            orders = customerOrderRepository.findOrdersByInventoryIdAndPoNumber(inventoryId, poNumber);
        else
            orders = customerOrderRepository.findByOrderNumberOrPoNumber(poNumber, PageRequest.ofSize(10000)).stream().toList();
        // calculate the product amount
        double usedAmount = 0;
        for (CustomerOrder order : orders) {
            for (OrderedProduct orderedProduct : order.getProducts()) {
                if (orderedProduct.getId().equals(productId) && !order.getId().equals(dispatchId)) {
                    usedAmount += orderedProduct.getQuantity();
                }
            }
        }
        double totalAmount = 0;
        OrderBook orderBook = customerId == null ? orderBookRepository.findByPONumberAndInventoryId(poNumber, inventoryId) : orderBookRepository.findByPONumberAndCustomerId(poNumber, new ObjectId(customerId));
        for (OrderBookEntry orderedProduct : orderBook.getProducts()) {
            if (orderedProduct.getId().equals(productId))
                totalAmount += orderedProduct.getQuantity();
        }
        HashMap<String, Object> result = new HashMap<>();
        double availableQuantity = totalAmount - usedAmount;
        // adding exemption of po quantity six percent
        boolean isValid = !(productQuantity > (availableQuantity + totalAmount * 0.06));
        result.put("isValid", isValid);
        result.put("availableQuantity", availableQuantity);
        return result;
    }

    @Override
    public void generateDispatchOrderSummarySheet() throws IOException {
        List<CustomerOrder> orders = getAllNonDeletedCustomerOrders();
        // create csv file
        String filePath = "dispatch_order_summary_sheet.csv";
        java.io.File file = new java.io.File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = {"Purchase order number", "Dispatch order number", "Product name", "Product id", "Packaging"};
        // Purchase order number(From master)	dispatch ordernumber(From master)	Product name(From master)	Product Id(From master)	Packaging(From master)
        writer.writeNext(headerKeys);
        for (CustomerOrder order : orders) {
            for (OrderedProduct orderedProduct : order.getProducts()) {
                writer.writeNext(new String[]{
                        order.getPurchaseOrderNumber() != null ? order.getPurchaseOrderNumber() : "",
                        order.getOrderId() != null ? order.getOrderId() : "",
                        orderedProduct.getProduct() != null && orderedProduct.getProduct().getTradeName() != null ? orderedProduct.getProduct().getTradeName() : "",
                        orderedProduct.getId() != null ? orderedProduct.getId() : "",
                        (orderedProduct.getPackaging() != null) ? orderedProduct.getPackaging().convertToString() : ""
                });
            }
        }
        writer.flush();
        writer.close();
        // add the break point or comment below code to not delete the file
        if (file.exists()) {
            file.delete();
        }
    }

    @Override
    public void moveToInventory(HashMap<String, Object> body) {
        String dispatchOrderId= (String) body.getOrDefault("customerOrderId","");
        String reason= (String) body.getOrDefault("reasonForCancellation","");
        String inventoryId= (String) body.getOrDefault("inventoryId","");
        CustomerOrder oldCustomerOrder=mongoQueries.getEntity(customerOrderRepository,dispatchOrderId);
        OrderBook oldOrderBook=orderBookService.getByPONumberAndCustomerId(oldCustomerOrder.getPurchaseOrderNumber(),oldCustomerOrder.getCustomer().getId());
        OrderBook newOrderBook=createInventoryOrderBook(oldOrderBook,inventoryId);
        oldCustomerOrder.setPurchaseOrderNumber(newOrderBook.getPurchaseOrderNumber());
        CustomerOrder newCustomerOrder=createInventoryDispatchOrder(oldCustomerOrder,inventoryId);
        oldCustomerOrder=mongoQueries.getEntity(customerOrderRepository,dispatchOrderId);
        oldOrderBook=orderBookService.getByPONumberAndCustomerId(oldCustomerOrder.getPurchaseOrderNumber(),oldCustomerOrder.getCustomer().getId());
        //new Customer Dispatch Order for Inventory
        //new OrderBook for Inventory
        String oldOrderBookId= oldOrderBook.getId();
        String newOrderBookId=newOrderBook.getId();
        boolean dispatchFromInventory=checkIfFromInventory(oldCustomerOrder.getId());
        //mark as deleted
        mongoQueries.softDeleteById(customerOrderRepository,dispatchOrderId,CustomerOrder.class);
        //unallocate orderbook
        inventoryProductService.moveToInventory(oldCustomerOrder,oldOrderBookId,newCustomerOrder,newOrderBookId,dispatchFromInventory);
        activityService.transferCriticalPath(oldCustomerOrder.getId(),newCustomerOrder.getId(),dispatchFromInventory);
    }

    private CustomerOrder createInventoryDispatchOrder(CustomerOrder oldOrder,String inventoryId){
        oldOrder.setCustomer(null);
        oldOrder.setInventoryId(inventoryId);
        oldOrder.setId(null);
        oldOrder=createCustomerOrder(oldOrder);
        return oldOrder;
    }

    private OrderBook createInventoryOrderBook(OrderBook oldOrderBook,String inventoryId){
        oldOrderBook.setCustomer(null);
        oldOrderBook.setInventoryId(inventoryId);
        oldOrderBook.setId(null);
        oldOrderBook=orderBookService.createOrderBook(oldOrderBook);
        return oldOrderBook;
    }

    private boolean checkIfFromInventory(String customerDispatchId){
        List<ProductBatchDetail> productBatchDetailList=productBatchDetailRepository.findAllocatedBatches(customerDispatchId);
        for (ProductBatchDetail productBatch:productBatchDetailList) {
            if(productBatch.getInventoryInOrderId()!=null){
                return true;
            }
        }
        return false;
    }

    @Override
    public void generateInvoiceNumber(InvoiceNumberGenerationRequest body) throws ServiceException {
        String invoiceType = body.getInvoiceType();
        String customerOrderId = body.getEntityId();
        if (customerOrderId == null) throw new ServiceException("entity id cannot be null ", 400);
        if (invoiceType == null) throw new ServiceException("invoice type cannot be null ", 400);
        CustomerOrder customerOrder = getCustomerOrderById(customerOrderId);
        if (invoiceType.equals("MSTACK_INVOICE")) {
            if (customerOrder.getMstackInvoiceNumber() != null)
                throw new ServiceException("Mstack Invoice number already generated for given order ", 400);
            String invoiceNumber = invoiceService.generateInvoiceNumber(body);
            CustomerOrder existingOrder = customerOrderRepository.findByMstackInvoiceNumber(invoiceNumber);
            if (existingOrder != null)
                throw new ServiceException("Same Invoice number already exists for order " + existingOrder.getOrderId(), 400);
            log.info("invoice number generated {} for order with id {}", invoiceNumber, customerOrder.getOrderId());
            customerOrder.setMstackInvoiceNumber(invoiceNumber);
            if (requestConfig != null && requestConfig.getEntityId() != null) {
                customerOrder.setInvoiceNumberGeneratedBy(requestConfig.getEntityId());
            }
            customerOrderRepository.save(customerOrder);
        } else if (invoiceType.equals("CHEMSTACK_TAX_INVOICE")) {
            if (customerOrder.getChemstackInvoiceNumber() != null)
                throw new ServiceException("Chemstack invoice number already generated for given order ", 400);
            String invoiceNumber = invoiceService.generateInvoiceNumber(body);
            CustomerOrder existingOrder = customerOrderRepository.findByChemstackInvoiceNumber(invoiceNumber);
            if (existingOrder != null)
                throw new ServiceException("Same Invoice number already exists for order " + existingOrder.getOrderId(), 400);
            log.info("invoice number generated {} for order with id {}", invoiceNumber, customerOrder.getOrderId());
            customerOrder.setChemstackInvoiceNumber(invoiceNumber);
            customerOrderRepository.save(customerOrder);
        } else {
            throw new ServiceException("invalid invoice type provided", 400);
        }
    }

}