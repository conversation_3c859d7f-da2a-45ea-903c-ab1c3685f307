package com.mstack.keystone.service.order.interfaces;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface ISupplierOrderBook {
    SupplierOrderBook createOrderBook(SupplierOrderBook orderBook);
    SupplierOrderBook getOrderBookById(String orderBookId);
    SupplierOrderBook updateOrderBook(String orderBookId, SupplierOrderBook updatedOrderBook);
    SupplierOrderBook getByPONumber(String poNumber);
    void deleteOrderBook(String orderBookId);
    Page<SupplierOrderBook> filterOrderBook(FilterRequest filterRequest);

    SupplierOrderBook getByPONumberAndCustomerId(String poNumber,String supplierId);

    List<SupplierOrderBook> getBySupplierId(String supplierId);

    List<SupplierOrderBook> getOrderBooksByCustomerOrderBookNumber(String orderBookId);

    File updatePOFile(String orderBookId, File file);

    public List<Map<String, Object>> getOrdersBySupplierOrCustomerName(String supplierName, String customerName);
}
