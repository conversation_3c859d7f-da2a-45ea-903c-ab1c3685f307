package com.mstack.keystone.service.order.interfaces;

import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.order.SupplierOrder;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

public interface SupplierOrderService {
    List<SupplierOrder> getAllNonDeletedSupplierOrders();

    Optional<SupplierOrder> getSupplierOrderById(String id);

    List<SupplierOrder> getSupplierOrdersByCustomerOrderBookId(String id);

    SupplierOrder createSupplierOrder(SupplierOrder supplierOrder);

    SupplierOrder updateSupplierOrder(String id, SupplierOrder supplierOrder);

    Page<SupplierOrder> filterSupplierOrders(FilterRequest filterRequest);

    void deleteSupplierOrder(String id);

    HashMap<String, Object> validateProductQuantity(String poNumber, String productId, double productQuantity, String dispatchId);

    List<SupplierOrder> findAllLinkedOrders(String orderId);

    List<SupplierOrder> getLinkedSupplierOrders(String customerOrderDispatchId);

    List<SupplierOrder> getAllSupplierOrdersBySOBId(String supplierOrderBookId);
}
