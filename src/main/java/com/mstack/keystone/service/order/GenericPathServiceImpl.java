package com.mstack.keystone.service.order;

import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.model.repository.*;
import com.mstack.keystone.model.repository.order.*;
import com.mstack.keystone.repository.AuditRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.service.order.interfaces.GenericPatchService;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GenericPathServiceImpl implements GenericPatchService {

  @Autowired
  private MongoTemplate mongoTemplate;

  @Autowired
  private MongoQueries mongoQueries;

  @Autowired
  private AuditRepository auditRepository;

  @Override
  @SneakyThrows
  public Object patchEntity(String entityName, String id, Map<String, Object> updates) {
    if (!ObjectId.isValid(id)) {
      throw new IllegalArgumentException("Invalid ObjectId format: " + id);
    }
    Query query = new Query(Criteria.where(AppConstants.ID).is(new ObjectId(id)));
    Update update = new Update();
    Class<?> entityClass = resolveEntityClass(entityName);
    updates.entrySet().stream()
        .map(entry -> (entry.getValue() instanceof Map)
            ? flattenNestedMap(entry.getKey(), (Map<String, Object>) entry.getValue())
            : Map.of(entry.getKey(),
                convertIfDateField(entityClass, entry.getKey(), entry.getValue())))
        .flatMap(map -> map.entrySet().stream())
        .forEach(entry -> update.set(entry.getKey(), entry.getValue()));
    Object updatedEntity = mongoTemplate.findAndModify(query, update,
        FindAndModifyOptions.options().returnNew(true), entityClass);
    saveAudit(entityName, updates);
    return updatedEntity;
  }

  private Class<?> resolveEntityClass(String entityName) {
    return switch (entityName.toLowerCase()) {
      case AppConstants.CUSTOMER -> Customer.class;
      case AppConstants.CUSTOMER_ORDER -> CustomerOrder.class;
      case AppConstants.SUPPLIER_ORDER -> SupplierOrder.class;
      case AppConstants.ORDER_BOOK -> OrderBook.class;
      case AppConstants.SUPPLIER_ORDER_BOOK -> SupplierOrderBook.class;
      case AppConstants.PRODUCT -> Product.class;
      default -> throw new IllegalArgumentException("Unknown entity: " + entityName);
    };
  }

  private Map<String, Object> flattenNestedMap(String prefix, Map<String, Object> nestedMap) {
    return nestedMap.entrySet().stream()
        .flatMap(entry -> {
          String newKey = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
          return (entry.getValue() instanceof Map)
              ? flattenNestedMap(newKey, (Map<String, Object>) entry.getValue()).entrySet().stream()
              : Map.of(newKey, entry.getValue()).entrySet().stream();
        })
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  private Object convertIfDateField(Class<?> entityClass, String fieldName, Object value) {
    if (value instanceof String && isDateField(entityClass, fieldName)) {
      return parseDate((String) value);
    }
    return value;
  }

  private boolean isDateField(Class<?> entityClass, String fieldName) {
    try {
      Field field = entityClass.getDeclaredField(fieldName);
      return field.getType().equals(Date.class) || field.getType().equals(LocalDate.class);
    } catch (NoSuchFieldException e) {
      return false; // Field not found, assume it's not a date field
    }
  }

  private Date parseDate(String dateStr) {
    return Date.from(Instant.parse(dateStr));
  }


  private void saveAudit(String entityName, Object entity) {
    Audit audit = Audit.builder()
        .entityType(entityName)
        .entity(entity)
        .build();
    mongoQueries.saveEntity(auditRepository, audit);
  }
}