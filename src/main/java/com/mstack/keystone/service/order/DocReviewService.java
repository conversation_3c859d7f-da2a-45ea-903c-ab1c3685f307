package com.mstack.keystone.service.order;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.DocComment;
import com.mstack.keystone.model.dto.Event;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.docEngine.DocStatus;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.DocumentComments;
import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.repository.DocCommentsRepo;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.docEngine.DocMetaRepository;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.user.CustomerRepository;
import com.mstack.keystone.service.accounting.InvoiceService;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.document.DocumentService;
import com.mstack.keystone.service.entity.CustomerService;
import com.mstack.keystone.service.entity.EmployeeService;
import com.mstack.keystone.utils.CommonUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.util.*;

@Service
public class DocReviewService {
    @Autowired
    DocCommentsRepo docCommentsRepo;
    @Autowired
    RequestConfig requestConfig;
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    DocMetaRepository docMetaRepository;
    @Autowired
    CustomerOrderRepository customerOrderRepository;

    @Autowired
    CustomerService customerService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    DocumentService documentService;
    @Autowired
    AwsSesService awsSesService;
    @Autowired
    CustomerRepository customerRepository;
    @Autowired
    CommonUtils commonUtils;

    public DocumentComments getCommentsForFile(String fileId){
        List<DocComment> commentsList=docCommentsRepo.findByFileId(fileId);
        DocumentComments documentComments=new DocumentComments();
        documentComments.setFileId(fileId);
        documentComments.setComments(commentsList);
        return documentComments;
    }

    public DocComment addComment(String fileId, DocComment comment){
        comment.setFileId(fileId);
        comment.setEntityType(requestConfig.getEntityType());
        return  mongoQueries.saveEntity(docCommentsRepo,comment);
    }
    public DocComment updateDocument(String id, DocComment comment){
        return  mongoQueries.updateEntity(docCommentsRepo,id,comment);
    }
    public void deleteComment(String id){
        mongoQueries.softDeleteById(docCommentsRepo,id,DocComment.class);
    }

    @SneakyThrows
    public void approveDocument(String orderId, String docType, String fileId, String docMetaId) {
        DocMeta docMeta = null;
        if (docMetaId != null) {
            docMeta = getDocMetaById(docMetaId);
        } else docMeta = docMetaRepository.getFileByOrderId(orderId, docType, fileId);
        if(docMeta==null){
            throw new ServiceException("No file exists",404);
        }
        if (!docMeta.getStatus().equals(DocStatus.PENDING_FOR_APPROVAL))
            throw new ServiceException("Only documents pending for approval can be approved ", 400);
        validateDocumentApproveRequest(orderId == null ? docMeta.getEntityId() : orderId, docMeta.getDocType(), docMeta.getFileId(), docMeta);
        docMeta.setApproved(true);
        docMeta.getEventsHistory().add(Event.createApiEvent(
                "DOCUMENT_APPROVED",
                "Document has been approved",
                requestConfig.getEntityId()
        ));
        // move to final if generated
        if (docMeta.getState().equals("Draft") && docMeta.getMode().equals("Generated")) {
            // deepclone docmeta
            DocMeta updatedDocMeta = DocMeta.getCopy(docMeta);
            // set state to final
            updatedDocMeta.setState("Final");
            // set status to approved
            updatedDocMeta.setStatus(DocStatus.APPROVED);
            // save updatedDocMeta
            mongoQueries.saveEntity(docMetaRepository, updatedDocMeta);
            // soft delete current doc Meta
            mongoQueries.softDeleteById(docMetaRepository, docMeta.getId(), DocMeta.class);
        } else docMetaRepository.save(docMeta);
        // send approved mail
        CustomerOrder order = getCustomerOrder(docMeta);
        Customer customer = getCustomer(order);
        List<String> recieverMailIds = new ArrayList<>();
        if (customer != null && customer.getL1Reviewers() != null) {
            customer.getL1Reviewers().forEach(reviewerId -> {
                String emailId = getEmployeeMailIdFromId(reviewerId);
                if (emailId != null) recieverMailIds.add(emailId);
            });
            String subject = "Invoice related to po number has been approved" + order.getPurchaseOrderNumber();
            String body = "Hi invoice related po number " + order.getPurchaseOrderNumber() + " has been approved";
            sendInvoiceViaEmail(docMeta.getFileId(), order.getPurchaseOrderNumber() + ".pdf", subject, body, recieverMailIds);
        }
    }

    private Customer getCustomer(CustomerOrder customerOrder) {
        if (customerOrder.getCustomer() == null || customerOrder.getCustomer().getId() == null) return null;
        return customerRepository.findById(customerOrder.getCustomer().getId()).orElse(null);
    }

    @SneakyThrows
    private void validateDocumentApproveRequest(String orderId, String docType, String fileId, DocMeta docMeta) {
        if (!(docType.equals("MSTACK_INVOICE") || docType.equals("CHEMSTACK_INVOICE"))) return;
        CustomerOrder order = customerOrderRepository.findById(orderId).orElse(null);
        if (order == null) throw new ServiceException("Invalid order id provided", 400);
        if (order.getIncoterms() == null) throw new ServiceException("Incoterms cannot be empty ", 400);
        if (order.getIncoterms().getType().equals(IncoType.DDP) && order.getDeliveryDate() == null)
            throw new ServiceException("Delivery date cannot be empty", 400);
        if (order.getMstackInvoiceNumber() == null && docType.equals("MSTACK_INVOICE"))
            throw new ServiceException("mstack invoice number cannot be empty ", 400);
        if (order.getChemstackInvoiceNumber() == null && docType.equals("CHEMSTACK_INVOICE"))
            throw new ServiceException("chemstack invoice number cannot be empty ", 400);
    }

    public void deleteAllComments(String fileId){
        List<DocComment> commentsList=docCommentsRepo.findByFileId(fileId);
        for (DocComment comment: commentsList) {
            mongoQueries.softDeleteById(docCommentsRepo,comment.getId(),DocComment.class);
        }
    }

    public void markAllCommentsResolved(String fileId) {
        List<DocComment> commentsList = docCommentsRepo.findByFileId(fileId);
        commentsList.forEach(docComment -> {
            docComment.setResolved(true);
            mongoQueries.updateEntity(docCommentsRepo, docComment.getId(), docComment);
        });
    }

    @SneakyThrows
    public void sendForReview(String id) {
        DocMeta docMeta = getDocMetaById(id);
        if (!docMeta.getStatus().equals(DocStatus.PENDING_FOR_REVIEW))
            throw new ServiceException("Only documents pending for review can be sent for review ", 400);
        // 	set doc meta status to pending_for_approval
        docMeta.setStatus(DocStatus.PENDING_FOR_APPROVAL);
        docMeta.getEventsHistory().add(Event.createApiEvent(
                "DOCUMENT_SENT_FOR_REVIEW",
                "Document has been sent for review",
                requestConfig.getEntityId()
        ));
        docMeta = docMetaRepository.save(docMeta);
        // update history
        sendReminderToL2(docMeta);
    }

    @SneakyThrows
    public void approveAndSend(String id) {
        DocMeta docMeta = getDocMetaById(id);
        if (!docMeta.getStatus().equals(DocStatus.PENDING_FOR_APPROVAL))
            throw new ServiceException("Only documents pending for approval can be approved & sent", 400);
        approveDocument(null, null, null, id);
        sendToCustomer(id);
    }

    @SneakyThrows
    public void sendForRevision(String id) {
        DocMeta docMeta = getDocMetaById(id);
        if (!docMeta.getStatus().equals(DocStatus.PENDING_FOR_APPROVAL))
            throw new ServiceException("Only documents pending for approval can be sent for revision", 400);
        docMeta.setStatus(DocStatus.PENDING_FOR_REVIEW);
        docMeta.getEventsHistory().add(Event.createApiEvent(
                "DOCUMENT_SENT_FOR_REVISION",
                "Document has been sent for revision",
                requestConfig.getEntityId()
        ));
        docMeta = docMetaRepository.save(docMeta);
        // send reminder to l1
        sendReminderToL1(docMeta);
    }

    private void sendReminderToL1(DocMeta docMeta) throws ServiceException {
        CustomerOrder order = getCustomerOrder(docMeta);
        Customer customer = getCustomer(order);
        List<String> recieverMailIds = new ArrayList<>();
        if (customer != null && customer.getL1Reviewers() != null) {
            customer.getL1Reviewers().forEach(reviewerId -> {
                String emailId = getEmployeeMailIdFromId(reviewerId);
                if (emailId != null) recieverMailIds.add(emailId);
            });
            String subject = "Recieved revision request for invoice related to po number " + order.getPurchaseOrderNumber();
            String body = "Hi ,new revision request is receieved for invoice related po number " + order.getPurchaseOrderNumber();
            sendInvoiceViaEmail(docMeta.getFileId(), order.getPurchaseOrderNumber() + ".pdf", subject, body, recieverMailIds);
        }
    }

    private void sendReminderToL2(DocMeta docMeta) throws ServiceException {
        CustomerOrder order = getCustomerOrder(docMeta);
        Customer customer = getCustomer(order);
        List<String> recieverMailIds = new ArrayList<>();
        if (customer != null && customer.getL2Reviewers() != null) {
            customer.getL2Reviewers().forEach(reviewerId -> {
                String emailId = getEmployeeMailIdFromId(reviewerId);
                if (emailId != null) recieverMailIds.add(emailId);
            });
            String subject = "Recieved approval request for invoice related to po number " + order.getPurchaseOrderNumber();
            String body = "Hi ,new approval request is receieved for invoice related to po number " + order.getPurchaseOrderNumber();
            sendInvoiceViaEmail(docMeta.getFileId(), order.getPurchaseOrderNumber() + ".pdf", subject, body, recieverMailIds);
        }
    }

    @SneakyThrows
    private DocMeta getDocMetaById(String id) {
        DocMeta docMeta = docMetaRepository.findById(id).orElse(null);
        if (docMeta == null) throw new ServiceException("Invalid doc meta id provided ", 400);
        return docMeta;
    }

    public void sendToCustomer(String id) throws ServiceException {
        DocMeta docMeta = getDocMetaById(id);
        List<String> recieverMailIds = getRecieverMailIds(docMeta);
        CustomerOrder order = getCustomerOrder(docMeta);
        Customer customer = getCustomer(order);
        if (customer != null && customer.getL2Reviewers() != null) {
            customer.getL2Reviewers().forEach(reviewerId -> {
                String emailId = getEmployeeMailIdFromId(reviewerId);
                if (emailId != null) recieverMailIds.add(emailId);
            });
        }
        String subject = "Your invoice related to po number " + order.getPurchaseOrderNumber() + " is ready";
        String body = "Hi , your invoice related po number " + order.getPurchaseOrderNumber() + " is ready";
        sendInvoiceViaEmail(docMeta.getFileId(), order.getPurchaseOrderNumber() + ".pdf", subject, body, recieverMailIds);
        docMeta.getEventsHistory().add(Event.createApiEvent(
                "DOCUMENT_SENT_TO_CUSTOMER",
                "Document has been sent to customer",
                requestConfig.getEntityId()
        ));
        docMetaRepository.save(docMeta);
    }

    @SneakyThrows
    private List<String> getRecieverMailIds(DocMeta docMeta) {
        List<String> recieverMailIds = new ArrayList<>();
        // GET DISPATCH ORDER FROM DOC META
        CustomerOrder order = getCustomerOrder(docMeta);
        Customer customer = getCustomer(order);
        if (customer != null && customer.getL2Reviewers() != null) {
            customer.getL2Reviewers().forEach(reviewerId -> {
                String emailId = getEmployeeMailIdFromId(reviewerId);
                if (emailId != null) recieverMailIds.add(emailId);
            });
        }
        // GET CUSTOMER DETAIL
        if (order.getCustomer() != null && customer != null && order.getCustomer().getId() != null) {
//            Customer customer = customerService.getCustomerById(order.getCustomer().getId());
            recieverMailIds.add(customer.getEmail());
            return commonUtils.combineEmailIds(customer.getEmailInfo(), recieverMailIds);
        }
        else{
            return recieverMailIds;
        }
    }

    private CustomerOrder getCustomerOrder(DocMeta docMeta) throws ServiceException {
        CustomerOrder order = customerOrderRepository.findById(docMeta.getEntityId()).orElse(null);
        if (order == null) throw new ServiceException("Invalid customer order id ", 400);
        return order;
    }

    private String getEmployeeMailIdFromId(String reviewerId) {
        Employee employee = employeeService.getEmployee(reviewerId);
        return employee != null ? employee.getEmail() : null;
    }

    public void sendInvoiceViaEmail(String invoiceFileId, String invoiceFileName, String subject, String body, List<String> recieverMailIds) {

        // download invoice from file id
        File file = documentService.downloadFileFromSignedUrl(documentService.getSignedUrlFromFileId(invoiceFileId), invoiceFileName);
        try {
            // send mail
            awsSesService.sendEmailWithAttachments(
                    "<EMAIL>",
                    recieverMailIds,
                    subject,
                    body,
                    List.of(file.getAbsolutePath())
            );
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        if (file.exists()) file.delete();
    }

}
