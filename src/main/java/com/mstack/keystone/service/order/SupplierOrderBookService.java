package com.mstack.keystone.service.order;

import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.ActivitiesRequest;
import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.enums.OBBatchType;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.inventory.OBProductBatch;
import com.mstack.keystone.model.repository.order.*;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.order.SupplierOrderBookRepo;
import com.mstack.keystone.service.PdfMergerService;
import com.mstack.keystone.service.batchDetails.OBProductBatchService;
import com.mstack.keystone.service.criticalPath.CriticalPathService;
import com.mstack.keystone.service.criticalPath.interfaces.ActivityService;
import com.mstack.keystone.service.inventory.InventoryProductService;
import com.mstack.keystone.service.order.interfaces.ISupplierOrderBook;
import com.mstack.keystone.utils.CommonUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.bson.Document;
import java.util.List;
import java.util.Map;

@Service
public class SupplierOrderBookService implements ISupplierOrderBook {

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    OrderBookRepository orderBookRepository;

    @Autowired
    SupplierOrderBookRepo supplierOrderBookRepo;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    OrderBookService orderBookService;

    @Autowired
    OBProductBatchService obProductBatchService;
    @Autowired
    InventoryProductService inventoryProductService;

    @Autowired
    PdfMergerService pdfMergerService;

    @Autowired
    CriticalPathService criticalPathService;

    @Autowired
    ActivityService activityService;

    private static final Map<String, Function<SupplierOrderBook, Date>> ORDER_BOOK_DUE_DATE_FIELDS_MAP = Map.of(
            "mrd", SupplierOrderBook::getMrd
    );

    @Override
    @SneakyThrows
    public SupplierOrderBook createOrderBook(SupplierOrderBook orderBook) {
        if (orderBook.getLinkedCOBs() == null || orderBook.getLinkedCOBs().isEmpty()) {
            throw new ServiceException("Linked cob list cannot be empty ", 400);
        }
        List<OrderBook> customerOrderBooks =  validateCOBs(orderBook.getLinkedCOBs());
        for (OrderBookEntry entry : orderBook.getProducts()) {
            entry.setId(UUID.randomUUID().toString());
            // Migrate otherPackagingDetails if present at product level
            if (entry.getPackaging() != null && entry.getOtherPackagingDetails() != null) {
                entry.getPackaging().setOtherPackagingDetails(entry.getOtherPackagingDetails());
                entry.setOtherPackagingDetails(null);
            }
        }
        // AUTO GENERATE PURCHASE ORDER NUMBER AND DATE
        orderBook.setPurchaseOrderDate(new Date());
        orderBook.setOrderBookId(commonUtils.CreateID("SOB", mongoQueries.getCount(supplierOrderBookRepo)));
        orderBook.setPurchaseOrderNumber("PO" + orderBook.getOrderBookId());
        orderBook.setPurchaseOrderFile(null);
        orderBook.setLinkedCOBId(customerOrderBooks.get(0).getOrderBookId());
        SupplierOrderBook generatedOrder = mongoQueries.saveEntity(supplierOrderBookRepo, orderBook);
        try{
            createOBProductBatches(generatedOrder);
            createInventoryProductTransaction(orderBook);
        }
        catch (Exception e){
            System.out.println(e.getMessage());
        }
        HashMap<String, Date> dueDateFields = new HashMap<>();
        //  dueDateFields.put("mrd", generatedOrder.getMrd());
        ORDER_BOOK_DUE_DATE_FIELDS_MAP.forEach((key, exractor) -> {
            dueDateFields.put(key, exractor.apply(generatedOrder));
        });
        OrderBook purchaseOrder = orderBookRepository
            .findByOrderBookId(orderBook.getLinkedCOBId())
            .stream()
            .findFirst()
            .orElseThrow(() -> new ServiceException(
                "OrderBook not found with id: " + orderBook.getLinkedCOBId(),
                HttpStatus.NOT_FOUND.value()));
        String templateName =
            purchaseOrder.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "cdoFlow"
                : "sampleCdoFlow";
        ActivitiesRequest activitiesRequest = new ActivitiesRequest().builder()
            .poGenrationRequired(orderBook.getPoGenrationRequired())
            .orderId(orderBook.getLinkedCOBId())
            .categoryName("SUPPLIER_ORDER_BOOK")
            .entityId(generatedOrder.getOrderBookId())
            .templateName(templateName)
            .dueDateFields(dueDateFields)
            .entityName(AppConstants.SUPPLIER_ORDER)
            .secondaryId(purchaseOrder.getPurchaseOrderNumber())
            .orderType(purchaseOrder.getOrderType().name())
            .build();
        criticalPathService.createOrUpdateTask(activitiesRequest);

        return generatedOrder;
    }

    private void createInventoryProductTransaction(SupplierOrderBook supplierOrderBook){
        // if even one dispatch order has inventory not == null it means orderbook under which SDO is being created has to create a transaction for inventory
        String orderbookId=supplierOrderBook.getLinkedCOBs().get(0);
        OrderBook orderBook=orderBookService.getOrderBookById(orderbookId);
        if(orderBook.getInventoryId()==null||orderBook.getInventoryId().isEmpty()){
            return;
        }
        List<OrderBookEntry> orderedProducts=supplierOrderBook.getProducts();
        for (OrderBookEntry orderedProduct:orderedProducts) {
            inventoryProductService.addProductQuantityToInventory(orderedProduct,orderBook.getInventoryId(),orderBook,supplierOrderBook);
        }
    }

    private void createOBProductBatches(SupplierOrderBook orderBook) {
        // Get related customer order book
        OrderBook customerOrderBook = orderBookService.getOrderBookById(orderBook.getLinkedCOBs().get(0));
        OBProductBatch obProductBatch = getObProductBatch(orderBook, customerOrderBook);
        for (OrderBookEntry orderBookEntry : orderBook.getProducts()) {
            // create batch for each product
            OBProductBatch clonedBatch = obProductBatch.getDeepClone();
            clonedBatch.setProductId(orderBookEntry.getProduct().getId());
            clonedBatch.setPackagingId(orderBookEntry.getPackaging().getId());
            clonedBatch.setUnits(orderBookEntry.getUnits());
            obProductBatchService.createBatch(clonedBatch);
        }
    }

    private void deleteOBProductBatches(String orderBookId) throws ServiceException {
        List<OBProductBatch> obProductBatches = obProductBatchService.getBatchesBySOBId(orderBookId);
        // delete each batch
        obProductBatches.forEach(obProductBatch -> obProductBatchService.deleteBatch(obProductBatch.getId()));
    }

    private static OBProductBatch getObProductBatch(SupplierOrderBook orderBook, OrderBook customerOrderBook) {
        OBProductBatch obProductBatch = new OBProductBatch();
        obProductBatch.setSupplierOrderBookId(orderBook.getId());
        // mts case
        if (customerOrderBook.getInventoryId() != null) {
            obProductBatch.setInventoryId(customerOrderBook.getInventoryId());
            obProductBatch.setInventoryInOrderId(customerOrderBook.getId());
            // if it's an inventory order
            obProductBatch.setType(OBBatchType.UNALLOCATED.getValue());
        } else {
            // mto
            obProductBatch.setCustomerOrderBookId(customerOrderBook.getId());
            obProductBatch.setType(OBBatchType.ALLOCATED.getValue());
        }
        return obProductBatch;
    }

    @SneakyThrows
    private List<OrderBook> validateCOBs(List<String> linkedCOBs) {
        List<OrderBook> orderBooks = new ArrayList<>();
        for (String linkedCOB : linkedCOBs) {
            Optional<OrderBook> orderBook = orderBookRepository.findById(linkedCOB);
            if (orderBook.isEmpty())
                throw new ServiceException("Invalid order book id provided ", 400);
            else{
                orderBooks.add(orderBook.orElse(null));
            }
        }
        return orderBooks;
    }

    @Override
    @SneakyThrows
    public SupplierOrderBook getOrderBookById(String orderBookId) {
        SupplierOrderBook supplierOrderBook = mongoQueries.getEntity(supplierOrderBookRepo, orderBookId);
        if (supplierOrderBook == null) throw new ServiceException("Invalid order id provide", 400);
        updateAssignedQuantity(supplierOrderBook);
        return supplierOrderBook;
    }

    private void updateAssignedQuantity(SupplierOrderBook supplierOrderBook) {
        for (OrderBookEntry orderBookEntry : supplierOrderBook.getProducts()) {
            if (orderBookEntry.getPackaging().getId() == null) continue;
            List<SupplierOrder> supplierOrders = getSupplierOrderByProductAndPackaging(orderBookEntry.getProduct().getId(), orderBookEntry.getPackaging().getId());
            if (supplierOrders == null) continue;
            double assignedQuantity = getAssignedQuantity(orderBookEntry, supplierOrders);
            if (orderBookEntry.getMeta() == null) {
                orderBookEntry.setMeta(new HashMap<>());
            }
            orderBookEntry.getMeta().put("assignedQuantity", assignedQuantity);
        }
    }

    private static double getAssignedQuantity(OrderBookEntry orderBookEntry, List<SupplierOrder> supplierOrders) {
        double assignedQuantity = 0;
        for (SupplierOrder supplierOrder : supplierOrders) {
            for (OrderedProduct orderedProduct : supplierOrder.getProducts()) {
                if (orderedProduct.getProduct().getId().equals(orderBookEntry.getProduct().getId())
                        && orderedProduct.getPackaging().getId().equals(orderBookEntry.getPackaging().getId())) {
                    assignedQuantity += orderedProduct.getQuantity();
                }
            }
        }
        return assignedQuantity;
    }

    private List<SupplierOrder> getSupplierOrderByProductAndPackaging(String productId, String packagingId) {
        return mongoQueries.findByFields(Map.ofEntries(
                Map.entry("products.product._id", List.of(productId)),
                Map.entry("products.packaging._id", List.of(packagingId)),
                Map.entry("deleted", List.of(false))
        ), null, SupplierOrder.class);
    }

    @Override
    @SneakyThrows
    public SupplierOrderBook updateOrderBook(String orderBookId, SupplierOrderBook updatedOrderBook) {
        SupplierOrderBook oldOrderBook=mongoQueries.getEntity(supplierOrderBookRepo,orderBookId);
        if(updatedOrderBook.getProducts()!=null){
            updatedOrderBook.getProducts().forEach(item->{
                if(item.getId()==null || item.getId().isBlank()){
                    item.setId(UUID.randomUUID().toString());
                }
                // Migrate otherPackagingDetails if present at product level
                if (item.getPackaging() != null && item.getOtherPackagingDetails() != null) {
                    item.getPackaging().setOtherPackagingDetails(item.getOtherPackagingDetails());
                    item.setOtherPackagingDetails(null);
                }
            });
        }
        boolean forInventory=isForInventoryOrdrBook(oldOrderBook);
        if(forInventory){
            validateUpdateRequest(updatedOrderBook,oldOrderBook);
        }
        updateOBProductBatches(updatedOrderBook,forInventory,oldOrderBook);
        updatedOrderBook = mongoQueries.updateEntity(supplierOrderBookRepo, orderBookId, updatedOrderBook);
        HashMap<String, Date> dueDateFields = new HashMap<>();
        SupplierOrderBook finalUpdatedOrderBook = updatedOrderBook;
        ORDER_BOOK_DUE_DATE_FIELDS_MAP.forEach((key, getter) -> {
            Date oldValue = getter.apply(oldOrderBook);
            Date newValue = getter.apply(finalUpdatedOrderBook);
            if (!Objects.equals(oldValue, newValue)) {
                dueDateFields.put(key, newValue);
            }
        });
        if (!dueDateFields.isEmpty()) {
            OrderBook purchaseOrder = orderBookRepository
                .findByOrderBookId(updatedOrderBook.getLinkedCOBId())
                .stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException(
                    "OrderBook not found with id: " + finalUpdatedOrderBook.getLinkedCOBId(),
                    HttpStatus.NOT_FOUND.value()));
            String templateName =
                purchaseOrder.getOrderType().equals(OrderType.CUSTOMER_ORDER) ? "cdoFlow"
                    : "sampleCdoFlow";
            ActivitiesRequest activitiesRequest = ActivitiesRequest.builder()
                .orderId(updatedOrderBook.getLinkedCOBId())
                .categoryName("SUPPLIER_ORDER_BOOK")
                .entityId(updatedOrderBook.getOrderBookId())
                .templateName(templateName)
                .dueDateFields(dueDateFields)
                .entityName(AppConstants.SUPPLIER_ORDER)
                .secondaryId(purchaseOrder.getPurchaseOrderNumber())
                .orderType(purchaseOrder.getOrderType().name())
                    .category(purchaseOrder.getCategory())
                .build();
            criticalPathService.createOrUpdateTask(activitiesRequest);
        }

        return updatedOrderBook;
    }

    private boolean isForInventoryOrdrBook(SupplierOrderBook oldSupplierOrderbook){
        String orderbookId=oldSupplierOrderbook.getLinkedCOBs().get(0);
        OrderBook orderBook=mongoQueries.getEntity(orderBookRepository,orderbookId);
        if(orderBook.getInventoryId()==null){
            return false;
        }
        return true;
    }

    private void validateUpdateRequest(SupplierOrderBook newOrderBook,SupplierOrderBook oldOrderBook){
        //checlk if any product has been deleted
        // if yes check if OB batches have been assigned to any customer order
        // throw exception if yes
        // check if target quantity
    }

    private void updateOBProductBatches(SupplierOrderBook orderBook, boolean forInventory,SupplierOrderBook oldOrderBook) throws ServiceException {
        HashMap<String,Double> oldUnitsMap=new HashMap<>();
        for (OrderBookEntry orderBookEntry : oldOrderBook.getProducts()) {
            oldUnitsMap.put(orderBookEntry.getId(),orderBookEntry.getUnits());
        }
            for (OrderBookEntry orderBookEntry : orderBook.getProducts()) {
            // compare units allocated vs current units
            OBProductBatch productBatch=null;
            if(forInventory){
                //fetch unallocated batch
                productBatch=obProductBatchService.getUnAllocatedBatchesBySOBIdAndProductId(oldOrderBook.getId(),orderBookEntry.getProduct().getId(),orderBookEntry.getPackaging().getId());
            } else {
                //fetch allocated batch
                List<OBProductBatch> productBatches = obProductBatchService.getAllocatedBatchesBySOBIdAndProductId(
                    oldOrderBook.getId(),
                    orderBookEntry.getProduct().getId(),
                    orderBookEntry.getPackaging().getId());

                productBatch = productBatches.stream()
                    .findFirst()
                    .orElse(null);
            }
            if(productBatch==null){
//                create a new batch for the above order
                productBatch=new OBProductBatch();
                productBatch.setProductId(orderBookEntry.getProduct().getId());
                productBatch.setPackagingId(orderBookEntry.getPackaging().getId());
                productBatch.setSupplierOrderBookId(oldOrderBook.getId());
            }
            else {
                double oldUnits=oldUnitsMap.getOrDefault(orderBookEntry.getId(),0.0);
                double diff= orderBookEntry.getUnits()-oldUnits;
                productBatch.setUnits(productBatch.getUnits()+diff);
            }
            if(productBatch.getId()==null){
                obProductBatchService.createBatch(productBatch);
            }
            else
            {
                obProductBatchService.updateBatch(productBatch.getId(),productBatch);
            }
        }
    }

    private double getAllocatedUnits(OrderBookEntry orderBookEntry, SupplierOrderBook supplierOrderBook) {
        List<OBProductBatch> obProductBatches = obProductBatchService.getAllocatedBatchesBySOBIdAndProductId(orderBookEntry.getProduct().getId(), supplierOrderBook.getId(),orderBookEntry.getPackaging().getId());
        double units = 0;
        for (OBProductBatch productBatch : obProductBatches) units += productBatch.getUnits();
        return units;
    }


    @Override
    public SupplierOrderBook getByPONumber(String poNumber) {
        return supplierOrderBookRepo.findByPONumber(poNumber);
    }

    @Override
    @SneakyThrows
    public void deleteOrderBook(String orderBookId) {
        validateDeleteRequest(orderBookId);
        // process order book deletion
        deleteOBProductBatches(orderBookId);
        mongoQueries.softDeleteById(supplierOrderBookRepo, orderBookId, SupplierOrderBook.class);
    }

    private void validateDeleteRequest(String orderBookId) throws ServiceException {
        Pageable pageable = PageRequest.of(0, 10000);
        // check if any supplier order exists
        SupplierOrderBook supplierOrderBook = supplierOrderBookRepo.findById(orderBookId).orElse(null);
        if (supplierOrderBook == null) throw new ServiceException("Invalid supplier order book id provided", 400);
        Page<SupplierOrder> supplierOrders = mongoQueries.findByFiltersAndSearch(null, new HashMap<>() {{
            put("purchaseOrderNumber", supplierOrderBook.getPurchaseOrderNumber());
        }}, pageable, SupplierOrder.class);
        if (!supplierOrders.isEmpty())
            throw new ServiceException("Supplier orders exist for given order book", 422);
        Map<String, List<Object>> query = Map.ofEntries(
                Map.entry("supplierOrderBookId", List.of(orderBookId)),
                Map.entry("type", List.of(OBBatchType.ALLOCATED.getValue())),
                Map.entry("deleted", List.of(false))
        );
        for (String customerOrderBookId : supplierOrderBook.getLinkedCOBs()) {
            OrderBook orderBook = orderBookService.getOrderBookById(customerOrderBookId);
            if (orderBook.getInventoryId() != null) {
                // if its an inventory order check if any ob batch maping is present
                List<OBProductBatch> obProductBatches = mongoQueries.findByFields(query, null, OBProductBatch.class);
                for (OBProductBatch obProductBatch : obProductBatches) {
                    OrderBook mappedOrderBook = orderBookService.getOrderBookById(obProductBatch.getCustomerOrderBookId());
                    throw new ServiceException("Supplier order book has been mapped with customer order book " + mappedOrderBook.getOrderBookId(), 400);
                }
            }
        }

    }

    @Override
    public Page<SupplierOrderBook> filterOrderBook(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<SupplierOrderBook> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            SupplierOrderBook.class
        );
        return page;
    }

    @Override
    public SupplierOrderBook getByPONumberAndCustomerId(String poNumber, String supplierId) {
        return supplierOrderBookRepo.findByPONumberAndSupplierId(poNumber, new ObjectId(supplierId));
    }

    @Override
    public List<SupplierOrderBook> getBySupplierId(String supplierId) {
        return supplierOrderBookRepo.findBySupplierId(new ObjectId(supplierId));
    }

    @Override
    public List<SupplierOrderBook> getOrderBooksByCustomerOrderBookNumber(String orderBookId) {
        return supplierOrderBookRepo.findByCustomerOrderBookId(orderBookId);
    }

    @SneakyThrows
    public File updatePOFile(String orderBookId, File file) {
        SupplierOrderBook orderBook = getOrderBookById(orderBookId);
        if (orderBook == null) throw new ServiceException("Invalid order id provided ", 400);
        if (orderBook.getMaterialSpecFile() == null || orderBook.getMaterialSpecFile().isEmpty())
            throw new ServiceException("PO cannot be generated without uploading material spec file ", 400);
        if (file == null) throw new ServiceException("file cannot be generated", 500);
        file.setName(orderBook.getPurchaseOrderNumber() + ".pdf");
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("meta", new HashMap<>() {{
            put("poId", orderBook.getPurchaseOrderNumber());
            put("documentType", "SUPPLIER_ORDER_BOOK_MERGED_PO");
        }});
        meta.put("category", "Orders");
        meta.put("name", file.getName());
        File mergedPo = pdfMergerService.mergeFiles(List.of(file, orderBook.getMaterialSpecFile().get(0)), meta);
        orderBook.setPurchaseOrderFile(List.of(mergedPo));
        mongoQueries.updateEntity(supplierOrderBookRepo, orderBook.getId(), orderBook);
        return mergedPo;
    }

    @Override
    public List<Map<String, Object>> getOrdersBySupplierOrCustomerName(String supplierName, String customerName) {
        List<Document> pipeline = new ArrayList<>();

        if (supplierName != null && !supplierName.trim().isEmpty()) {
            return mongoQueries.findBySupplierNameOrBoth(pipeline, supplierName, customerName);
        } else {

            List<Activity> activities = activityService.findPendingInvoiceUploadActivitiesByCustomer(customerName);

            // Map of orderId -> list of matching activities
            Map<String, List<Activity>> orderIdToActivities = activities.stream()
                    .filter(doc -> doc.getOrderId() != null)
                    .collect(Collectors.groupingBy(doc -> doc.getOrderId()));

            List<String> orderIds = new ArrayList<>(orderIdToActivities.keySet());

            if (orderIds.isEmpty()) {
                return Collections.emptyList();
            }

            List<Document> supplierOrders = supplierOrderBookRepo.findByLinkedCOBIdIn(orderIds);

            // Enrich each supplier order with its matching activities
            return supplierOrders.stream()
                    .map(order -> {
                        String cobId = order.getString("linkedCOBId");
                        List<Activity> matchedActivities = orderIdToActivities.getOrDefault(cobId, Collections.emptyList());
                        order.put("matchedActivities", matchedActivities);
                        return (Map<String, Object>) order;
                    })
                    .collect(Collectors.toList());
        }
    }

    private void findBySupplierNameOrBoth(List<Document> pipeline, String supplierName, String customerName) {
        pipeline.add(new Document("$match", new Document("supplier.name", supplierName)));
        pipeline.add(new Document("$lookup", new Document()
                .append("from", "activity")
                .append("let", new Document("orderId", "$linkedCOBId"))
                .append("pipeline", List.of(
                        new Document("$match", new Document("$expr", new Document("$and", List.of(
                                new Document("$eq", List.of("$orderId", "$$orderId")),
                                new Document("$eq", List.of("$name", "Invoice Upload")),
                                new Document("$in", List.of("$status", List.of("TODO", "IN_PROGRESS")))
                        ))))
                ))
                .append("as", "pendingActivities")
        ));

        pipeline.add(new Document("$match", new Document("$expr",
                new Document("$gt", List.of(
                        new Document("$size", new Document("$ifNull", List.of("$pendingActivities", List.of()))),
                        0
                ))
        )));

        if (customerName != null && !customerName.trim().isEmpty()) {
            pipeline.add(new Document("$match", new Document("pendingActivities.customerName", customerName)));
        }

        addCommonProjectionStage(pipeline);
    }

    private void addCommonProjectionStage(List<Document> pipeline) {
        pipeline.add(new Document("$addFields", new Document("matchedActivities", "$pendingActivities")));
        pipeline.add(new Document("$project", new Document("pendingActivities", 0)));
    }

}
