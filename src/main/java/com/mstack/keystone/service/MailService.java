package com.mstack.keystone.service;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.model.dto.CustomerMailRequest;
import com.mstack.keystone.model.dto.MailRequest;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.MailStatus;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.MailRecord;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.user.MailRepository;
import com.mstack.keystone.repository.user.UserRepository;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.docEngine.DocEngineTemplateService;
import com.mstack.keystone.service.docEngine.DocMetaService;
import com.mstack.keystone.service.document.DocumentService;
import com.mstack.keystone.service.entity.CustomerService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Service;
import com.mstack.keystone.utils.CommonUtils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MailService {
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    DocMetaService docMetaService;

    @Autowired
    DocEngineTemplateService docEngineTemplateService;

    @Autowired
    CustomerService customerService;
    @Autowired
    DocumentService documentService;

    @Autowired
    AwsSesService awsSesService;

    @Autowired
    RequestConfig requestConfig;

    @Autowired
    UserRepository userRepository;
    @Autowired
    MailRepository mailRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    CommonUtils commonUtils;

    public void sendMailToCustomer(CustomerMailRequest customerMailRequest) {
        if (customerMailRequest.getMailTemplate().equals("MSTACK_INVOICE_SUMMARY")) {
            generateMstackInvoiceSummary(customerMailRequest);
        }
    }

    private void generateMstackInvoiceSummary(CustomerMailRequest customerMailRequest) {
        List<InvoiceMailSummary> invoiceMailSummaries = new ArrayList<>();
        List<String> recieverMailIds = new ArrayList<>();
        List<String> poNumberList = new ArrayList<>();
        String userId = requestConfig.getEntityId();
        User user = userRepository.findByEntityId(userId);
        if (user != null && user.getUsername() != null) {
            if (customerMailRequest.getCcs() == null) customerMailRequest.setCcs(new ArrayList<>());
            customerMailRequest.getCcs().add(user.getUsername());
        }
        HashMap<String, String> fileMap = new HashMap<>();
        Customer customer = customerService.getCustomerById(customerMailRequest.getCustomerId());
        if (customerMailRequest.getSalesEmail() != null) recieverMailIds.add(customerMailRequest.getSalesEmail());
        if (customerMailRequest.getAccountEmail() != null) recieverMailIds.add(customerMailRequest.getAccountEmail());
        if (customerMailRequest.getOptionalEmails() != null) recieverMailIds.addAll(customerMailRequest.getOptionalEmails());
        if (customer != null && customer.getEmail() != null) recieverMailIds.add(customer.getEmail());
        recieverMailIds = commonUtils.combineEmailIds(customer.getEmailInfo(), recieverMailIds);
        // iterate through each attachement
        customerMailRequest.getAttachments().forEach((k, v) -> {
            CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(k);
            if (!v.isEmpty()) poNumberList.add(customerOrder.getPurchaseOrderNumber());
            for (String attachmentId : v) {
                DocMeta docMeta = docMetaService.getDocMetaById(attachmentId).orElse(null);
                if (docMeta == null) continue;
                if (docMeta.getDocType().equals("MSTACK_INVOICE")) {
                    List<TemplatePlaceholder> templatePlaceholders = docEngineTemplateService.aggregateDataForDocument(getSystemRequestMstackInvoiceHashMap(k));
                    //TOTAL_INVOICE_AMOUNT
                    Double totalInvoiceAmount = (Double) templatePlaceholders.stream()
                            .filter(templatePlaceholder -> templatePlaceholder.getKey().equals("TOTAL_INVOICE_AMOUNT")).findFirst().get().getValue();
                    //DUE_DATE
                    String dueDate = (String) templatePlaceholders.stream()
                            .filter(templatePlaceholder -> templatePlaceholder.getKey().equals("DUE_DATE")).findFirst().get().getValue();
                    //CURRENCY
                    String currency = (String) templatePlaceholders.stream()
                            .filter(templatePlaceholder -> templatePlaceholder.getKey().equals("CURRENCY")).findFirst().get().getValue();
                    //INVOICE_NUMBER
                    String invoiceNumber = (String) templatePlaceholders.stream()
                            .filter(templatePlaceholder -> templatePlaceholder.getKey().equals("INVOICE_NUMBER")).findFirst().get().getValue();
                    InvoiceMailSummary invoiceMailSummary = new InvoiceMailSummary(invoiceNumber, currency + " " + Double.toString(totalInvoiceAmount), dueDate, "MSTACK_INVOICE", customerOrder.getPurchaseOrderNumber());
                    invoiceMailSummaries.add(invoiceMailSummary);
                    fileMap.put(invoiceNumber + ".pdf", docMeta.getFileId());
                } else {
                    // only add file map
                    fileMap.put(docMeta.getFileName(), docMeta.getFileId());
                }
            }
        });
        StringBuilder poLabel = new StringBuilder();
        if (poNumberList.size() == 1) {
            poLabel.append(poNumberList.get(0));
        } else {
            for (int i = 0; i < poNumberList.size(); i++) {
                if (i == poNumberList.size() - 1) poLabel.append(poNumberList.get(i));
                else {
                    poLabel.append(poNumberList.get(i)).append(" , ");
                }
            }
        }
        String mailBody = getMstackInvoiceMailHeader(poLabel.toString()) + generateHtmlForMstackInvoiceSummary(invoiceMailSummaries) + getMstackInvoiceMailFooter();
        String subject = "Summary of invoices issued by Mstack Inc against po numbers : " + poLabel.toString();
        List<File> files = new ArrayList<>();
        fileMap.forEach((k, v) -> {
            File file = documentService.downloadFileViaSignedUrl(v, k.replace("/", "-"));
            files.add(file);
        });
        if (customerMailRequest.getTos() != null) recieverMailIds.addAll(customerMailRequest.getTos());
        MailRequest mailRequest = MailRequest.builder()
                .from("<EMAIL>")
                .tos(recieverMailIds)
                .ccs(customerMailRequest.getCcs())
                .contentType("text/html")
                .subject(subject)
                .attachments(files.stream().map(File::getAbsolutePath).collect(Collectors.toList()))
                .bodyText(mailBody)
                .build();

        try {
            awsSesService.sendEmailWithAttachmentsV2(mailRequest);
            log.info("Mail sent sucessfully ");
            log.info("adding to mail history ");
            mongoQueries.saveEntity(mailRepository, MailRecord.builder()
                    .fileMap(replateDotForKeys(fileMap))
                    .mailTemplate(customerMailRequest.getMailTemplate())
                    .senderId("<EMAIL>")
                    .tos(recieverMailIds)
                    .ccs(customerMailRequest.getCcs())
                    .body(mailBody)
                    .subject(subject)
                    .mailStatus(MailStatus.SENT)
                    .build());
            log.info("added in mail history ");
        } catch (Exception ex) {
            log.info("Mail sent failed ");
            log.info("adding to mail history ");
            String errMessage = ex.getMessage();
            mongoQueries.saveEntity(mailRepository, MailRecord.builder()
                    .fileMap(replateDotForKeys(fileMap))
                    .mailTemplate(customerMailRequest.getMailTemplate())
                    .senderId("<EMAIL>")
                    .tos(recieverMailIds)
                    .ccs(customerMailRequest.getCcs())
                    .body(mailBody)
                    .subject(subject)
                    .mailStatus(MailStatus.FAILED)
                    .meta(new HashMap<String, Object>() {{
                        put("err", errMessage);
                    }})
                    .build());
            log.info("added in mail history ");
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }
        log.info("cleaning up ");
        files.forEach(file -> {
            if (file.exists())
                file.delete();
        });
    }

    private HashMap<String, String> replateDotForKeys(HashMap<String, String> fileMap) {
        HashMap<String, String> map = new HashMap<>();
        for (String key : fileMap.keySet()) {
            map.put(key.replace(".", "-DOT-"), fileMap.get(key));
        }
        return map;
    }

    private String generateHtmlForMstackInvoiceSummary(List<InvoiceMailSummary> invoiceMailSummaries) {
        StringBuilder body = new StringBuilder();
        for (InvoiceMailSummary invoiceMailSummary : invoiceMailSummaries) {
            body.append("<tr>");
            body.append("<td>").append(invoiceMailSummary.getPoNumber()).append("</td>");
            body.append("<td>").append(invoiceMailSummary.getInvoiceNumber()).append("</td>");
            body.append("<td>").append(invoiceMailSummary.getAmountDue()).append("</td>");
            body.append("<td>").append(invoiceMailSummary.getDueDate()).append("</td>");
            body.append("</tr>");
        }
        return body.toString();
    }

    private String getMstackInvoiceMailHeader(String poLabel) {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>Invoice Email</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            font-family: Arial, sans-serif;\n" +
                "            color: #333333;\n" +
                "            line-height: 1.6;\n" +
                "            margin: 0;\n" +
                "            padding: 20px;\n" +
                "            background-color: #f4f4f4;\n" +
                "        }\n" +
                "        .container {\n" +
                "            background-color: #ffffff;\n" +
                "            padding: 20px;\n" +
                "            border-radius: 5px;\n" +
                "            max-width: 600px;\n" +
                "            margin: 0 auto;\n" +
                "            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n" +
                "        }\n" +
                "        .header {\n" +
                "            background-color: #007bff;\n" +
                "            color: #ffffff;\n" +
                "            padding: 10px;\n" +
                "            text-align: center;\n" +
                "            border-radius: 5px 5px 0 0;\n" +
                "        }\n" +
                "        .header h1 {\n" +
                "            margin: 0;\n" +
                "            font-size: 24px;\n" +
                "        }\n" +
                "        .content {\n" +
                "            padding: 20px;\n" +
                "        }\n" +
                "        .content p {\n" +
                "            margin-bottom: 10px;\n" +
                "        }\n" +
                "        .content table {\n" +
                "            width: 100%;\n" +
                "            border-collapse: collapse;\n" +
                "            margin-bottom: 20px;\n" +
                "        }\n" +
                "        .content table, .content th, .content td {\n" +
                "            border: 1px solid #dddddd;\n" +
                "        }\n" +
                "        .content th, .content td {\n" +
                "            padding: 8px;\n" +
                "            text-align: left;\n" +
                "        }\n" +
                "        .footer {\n" +
                "            text-align: center;\n" +
                "            font-size: 12px;\n" +
                "            color: #666666;\n" +
                "        }\n" +
                "        .footer p {\n" +
                "            margin: 5px 0;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <div class=\"header\">\n" +
                "            <h1>MSTACK INVOICE</h1>\n" +
                "        </div>\n" +
                "        <div class=\"content\">\n" +
                "            <p>Hi, <br/> Please find attached the invoice(s) for po number(s) : " + poLabel + " . Below are the details:</p>\n" +
                "            <table>\n" +
                "                <tr>\n" +
                "                    <th>PO Number</th>\n" +
                "                    <th>Invoice Number</th>\n" +
                "                    <th>Amount Due </th>\n" +
                "                    <th>Due Date</th>\n" +
                "                </tr>";
    }

    private String getMstackInvoiceMailFooter() {
        return "</table>\n" +
                "            <p>You are requested to review the attached documents and proceed with the payment by the due date.</p>\n" +
                "            <p>For any questions or further clarification, please contact us at <a href=\"mailto:<EMAIL>\"><EMAIL></a> or call at <a href=\"tel:******6494706\">****** 649 4706</a>.</p>\n" +
                "            <p>Thank you for your continued partnership.</p>\n" +
                "        </div>\n" +
                "        <div class=\"footer\">\n" +
                "            <p>Best regards,</p>\n" +
                "            <p><strong>Mstack Inc.</strong></p>\n" +
                "            <p>Email: <a href=\"mailto:<EMAIL>\"><EMAIL></a></p>\n" +
                "            <p>Phone: <a href=\"tel:+19706494706\">****** 649 4706</a></p>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>\n";
    }

    public HashMap<String, Object> getSystemRequestMstackInvoiceHashMap(String customerOrderId) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("docType", "MSTACK_INVOICE");
        request.put("meta", new HashMap<>(
                Map.ofEntries(
                        Map.entry("customerOrderId", customerOrderId),
                        Map.entry("requestedBy", "system")
                )
        ));
        return request;
    }


}
