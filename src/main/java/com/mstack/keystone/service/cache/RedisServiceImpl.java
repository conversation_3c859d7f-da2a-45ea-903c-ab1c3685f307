package com.mstack.keystone.service.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mstack.keystone.config.RedisConfig;
import com.mstack.keystone.model.dto.SendMailRequest;
import com.mstack.keystone.model.repository.email.Email;
import com.mstack.keystone.service.email.interfaces.IEmailCreator;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Service
public class RedisServiceImpl implements IRedisService {

    private static String REDIS_HOST = "localhost";
    private static int REDIS_PORT = 6379;

    private JedisPool jedisPool;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    IEmailCreator emailCreator;

    RedisConfig redisConfig;

    @Autowired
    public RedisServiceImpl(RedisConfig redisConfig) {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        this.jedisPool = new JedisPool(redisConfig.getUrl());
        this.redisConfig = redisConfig;
    }

    @Override
    public void cacheValue(String key, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.set(key, value);
        }
    }

    @Override
    public String getCachedValue(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        }
    }

    @Override
    public void enqueueMessage(String message) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.lpush(redisConfig.getEmailQueue(), message);
        }
    }

    @Override
    public String dequeueMessage() {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.rpop(redisConfig.getEmailQueue());
        }
    }

    @PostConstruct
    public void consumeMessages() {
        Thread myThread = new Thread(this::pollConsumer);
        myThread.start();
    }

    private void pollConsumer() {
        while (true) {
            try (Jedis jedis = jedisPool.getResource()) {
                long queueLength = jedis.llen(redisConfig.getEmailQueue());

                if (queueLength > 0) {
                    for (int i = 0; i < queueLength; i++) {
                        String message = jedis.rpop(redisConfig.getEmailQueue());
                        if (message != null) {
                            try {
                                // Do processing here
                                System.out.println("Processing message: " + message);
                                SendMailRequest emailData = objectMapper.readValue(message, SendMailRequest.class);
                                Email email = new Email(
                                    emailData.getFrom(),
                                    emailData.getTo(),
                                    emailData.getEmailType(),
                                    emailData.getAdditionalInfo(),
                                    emailData.getTemplateInfo(),
                                    emailData.getSentBy()
                                );
                                if (emailData.getAttachments() != null && !emailData.getAttachments().isEmpty()) {
                                    email.setAttachments(email.getAttachments());
                                }
                                emailCreator.createEmail(email);
                                System.out.println("Processing completed successfully.");
                                Thread.sleep(2000);
                            } catch (Exception e) {
                                System.err.println("Error processing message: " + message);
                                System.err.println(e);
                                // TODO- retry emails for some specific errors
                                // jedis.rpush(QUEUE_NAME, message);
                            }
                        }
                    }
                }
            }

            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void close() {
        jedisPool.close();
    }
}
