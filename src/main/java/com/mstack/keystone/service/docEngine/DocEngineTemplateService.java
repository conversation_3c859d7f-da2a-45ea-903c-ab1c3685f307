package com.mstack.keystone.service.docEngine;


import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.model.repository.docEngine.DocEngineTemplate;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.docEngine.DocEngineTemplateRepository;
import com.mstack.keystone.repository.docEngine.DocMetaRepository;
import com.mstack.keystone.repository.order.SupplierOrderRepository;
import com.mstack.keystone.service.documentAggregator.DocumentAggregatorFactory;
import com.mstack.keystone.service.documentAggregator.IDocumentAggregator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;

@Service
public class DocEngineTemplateService {

    @Autowired
    DocEngineTemplateRepository templateRepository;
    @Autowired
    DocMetaRepository docMetaRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    DocumentAggregatorFactory aggregatorFactory;
    @Autowired
    SupplierOrderRepository supplierOrderRepo;

    public DocEngineTemplate createTemplate(DocEngineTemplate template) {
        return mongoQueries.saveEntity(templateRepository, template);
    }

    public Optional<DocEngineTemplate> getTemplateById(String id) {
        return templateRepository.findById(id);
    }

    public Optional<DocEngineTemplate> getTemplateByDocType(String docType) {
        return Optional.ofNullable(templateRepository.findTemplateByDocType(docType));
    }

    public DocEngineTemplate updateTemplate(DocEngineTemplate existingTemplate, DocEngineTemplate updatedTemplate) {
        return mongoQueries.updateEntity(templateRepository, existingTemplate.getId(), existingTemplate);
    }

    public List<DocEngineTemplate> getAllTemplates() {
        return templateRepository.findAll();
    }

    public void deleteTemplate(String id) {
        mongoQueries.softDeleteById(templateRepository, id, DocEngineTemplate.class);
    }

    public List<TemplatePlaceholder> aggregateDataForDocument(HashMap<String, Object> request) {
        String docType=request.get("docType").toString();
        IDocumentAggregator documentAggregator=aggregatorFactory.getDocumentAggregator(docType);
        return documentAggregator.getDocumentData((HashMap) request.get("meta"));
    }
    public List getValidFileTypesForOrder(String id){
        List<DocEngineTemplate> templates=templateRepository.findGenerationTemplates();
        return generateDocMetaListFromTemplateList(templates, id);
    }

    public List getTemplatesByCategory(String category, String id) {
        List<DocEngineTemplate> templates = templateRepository.findTemplatesByCategoryType(category);
        if (templates == null || templates.isEmpty()) return templates;
        return generateDocMetaListFromTemplateList(templates, id);
    }

    private List<DocMeta> generateDocMetaListFromTemplateList(List<DocEngineTemplate> templates, String id) {
        HashMap<String, DocMeta> filesMap = new HashMap<>();
        for (DocEngineTemplate template : templates) {
            HashMap map = new HashMap<>();
            map.put("customerOrderId", id);
            template.setMeta(map);
            filesMap.put(template.getDisplayName(), new DocMeta(template.getDocType(), template.getDisplayName(), template.getViewers(), template.getCategory(), template.getState(), EntityType.CUSTOMER_ORDER, id, map));
        }
        //check if supplier outside India.
        List<SupplierOrder> supplierOrders = supplierOrderRepo.findAllOrdersWithDispatchOrderId(id);
        boolean isOutsideIndia = false;
        for (SupplierOrder supplierOrder : supplierOrders) {
            if (!supplierOrder.getSupplier().getAddress().getCountry().equals("India")) {
                isOutsideIndia = true;
            }
        }
        if (isOutsideIndia) {
            addDistinctBLNames(supplierOrders, filesMap);
            filesMap.remove("MSTACK TO CHEMSTACK PO");
            filesMap.remove("CHEMSTACK TAX INVOICE");
            filesMap.remove("BILL OF LADING");
        }
        filesMap.remove("SUPPLIER PURCHASE ORDER");
        List<DocMeta> docMetaList = docMetaRepository.getFilesByDocType(new ArrayList<>(filesMap.keySet()), id);
        docMetaList.sort(Comparator.comparing(DocMeta::getCreatedAt));
        for (DocMeta docMeta : docMetaList) {
            filesMap.put(docMeta.getLabel(), docMeta);
        }
        //        remove files already created once
        return new ArrayList<>(filesMap.values());
    }
    private void addDistinctBLNames(List<SupplierOrder> supplierOrders,HashMap<String,DocMeta> filesMap){
        Map<Supplier, List<OrderedProduct>> productsBySupplier=getProductsBySupplier(supplierOrders);
        DocMeta docMeta=filesMap.remove("BILL OF LADING");
        List<String> blName=new ArrayList<>();
        for (Supplier supplier:productsBySupplier.keySet()) {
            List<OrderedProduct> products=productsBySupplier.get(supplier);
            String s="";
            for (OrderedProduct product:products) {
                s+=product.getProduct().getTradeName()+" ";
            }
            filesMap.put(docMeta.getLabel()+s,new DocMeta(docMeta.getDocType(),docMeta.getLabel(),docMeta.getViewers(),docMeta.getCategory(),docMeta.getState(), docMeta.getEntityType(),docMeta.getEntityId(),docMeta.getMeta()));
        }
    }


    private   Map<Supplier, List<OrderedProduct>> getProductsBySupplier(List<SupplierOrder> supplierOrders) {
        Map<Supplier, List<OrderedProduct>> productsBySupplier = new HashMap<>();
        supplierOrders.sort(Comparator.comparing(SupplierOrder::getId));
        for (SupplierOrder supplierOrder : supplierOrders) {
            Supplier supplier = supplierOrder.getSupplier();
            List<OrderedProduct> products = supplierOrder.getProducts();
            products.sort(Comparator.comparing(OrderedProduct::getId));
            // If the supplier is not present in the map, add a new entry
            // Otherwise, append the products to the existing list
            productsBySupplier.computeIfAbsent(supplier, k -> new ArrayList<>()).addAll(products);
        }
        return productsBySupplier;
    }
}
