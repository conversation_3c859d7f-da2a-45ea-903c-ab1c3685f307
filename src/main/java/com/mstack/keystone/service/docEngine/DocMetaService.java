package com.mstack.keystone.service.docEngine;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.DocComment;
import com.mstack.keystone.model.dto.Event;
import com.mstack.keystone.model.dto.docEngine.DocMetaDto;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.enums.docEngine.DocStatus;
import com.mstack.keystone.model.repository.docEngine.DocEngineTemplate;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.repository.DocCommentsRepo;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.docEngine.DocMetaRepository;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.service.document.DocumentService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class DocMetaService {
    
    @Autowired
    DocMetaRepository docMetaRepository;

    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    DocCommentsRepo docCommentsRepo;

    @Autowired
    DocEngineTemplateService docEngineTemplateService;

    @Autowired
    DocumentService documentService;

    @Autowired
    CustomerOrderRepository customerOrderRepository;

    @Autowired
    RequestConfig requestConfig;

    public DocMeta createDocMeta(DocMeta docMeta) {
        updatedMetaByTemplateDetails(docMeta);
        if(docMeta.getMode()==null){
            docMeta.setMode("Uploaded");
        }
        if (docMeta.getMeta() == null) {
            docMeta.setMeta(new HashMap<>());
            if (docMeta.getEntityType().equals(EntityType.CUSTOMER_ORDER)) {
                docMeta.getMeta().put("customerOrderId", docMeta.getEntityId());
            }
        }
        if (docMeta.getEventsHistory() == null) {
            docMeta.setEventsHistory(new ArrayList<>());
        }
        List<DocMeta> oldDocMetaList=docMetaRepository.getFileByDocTypeAndEntityId(docMeta.getDocType(),docMeta.getEntityId());
        DocStatus oldStatus = null;
        if(oldDocMetaList!=null){
            // case of regeneration
            // clone old history
            for (DocMeta oldDocMeta:oldDocMetaList) {
                if (oldStatus == null) oldStatus = oldDocMeta.getStatus();
                if (oldDocMeta.getEventsHistory() != null && !oldDocMeta.getEventsHistory().isEmpty()) {
                    docMeta.getEventsHistory().addAll(oldDocMeta.getEventsHistory());
                }
                mongoQueries.softDeleteById(docMetaRepository, oldDocMeta.getId(), DocMeta.class);
            }
            // add regenerate event
            docMeta.getEventsHistory().add(Event.createApiEvent(
                    "DOCUMENT_REGENERATED",
                    "Document generated by user",
                    requestConfig.getEntityId()
            ));
        }
        // no doc meta status have been updated
        if (oldStatus == null) docMeta.setStatus(DocStatus.PENDING_FOR_REVIEW);
        else docMeta.setStatus(oldStatus);
        return mongoQueries.saveEntity(docMetaRepository, docMeta);
    }

    public Optional<DocMeta> getDocMetaById(String id) {
        return docMetaRepository.findById(id);
    }

    public DocMeta getDocMetaByFileId(String fileId) {
        return docMetaRepository.getFileByFileId(fileId);
    }


    public DocMeta updateDocMeta(DocMeta existingDocMeta, DocMeta updatedDocMeta) {
        return mongoQueries.updateEntity(docMetaRepository, existingDocMeta.getId(), existingDocMeta);
    }

    public List<DocMeta> getAllDocMeta() {
        return docMetaRepository.findAll();
    }

    public void deleteDocMeta(String id) {
        mongoQueries.softDeleteById(docMetaRepository, id, DocMeta.class);
    }


    @SneakyThrows
    private void updatedMetaByTemplateDetails(DocMeta docMeta) {
        Optional<DocEngineTemplate> optionalDocEngineTemplate = docEngineTemplateService.getTemplateByDocType(docMeta.getDocType());
        if (optionalDocEngineTemplate.isPresent()) {
            DocEngineTemplate template = optionalDocEngineTemplate.get();
            if (template.getCategory() != null)
                docMeta.setCategory(template.getCategory());
            if (template.getState() != null)
                docMeta.setState(template.getState());
            if (template.getViewers() != null)
                docMeta.setViewers(template.getViewers());
        } else {
            throw new ServiceException("No document category with given docType : "+docMeta.getDocType(), 404);
        }
    }

    public List<DocMetaDto> getAllFiles(String id){
        List<DocMeta> docList= docMetaRepository.getAllFiles(id);
        List<DocMetaDto> docMetaList=new ArrayList<>();
        List<String> fileIdList = docList.stream()
                .map(DocMeta::getFileId)
                .collect(Collectors.toList());
        List<DocComment> commentList=docCommentsRepo.findByFileIds(fileIdList);
        Map<String, Integer> commentsMap = new HashMap<>();
        for (DocComment comment : commentList) {
            commentsMap.put(comment.getFileId(), commentsMap.getOrDefault(comment.getFileId(),0)+1);
        }
        for (DocMeta docMeta:docList) {
            DocMetaDto docMetaDto=new DocMetaDto(docMeta,commentsMap.getOrDefault(docMeta.getFileId(),0),docMeta.isApproved());
            docMetaList.add(docMetaDto);
        }
        return docMetaList;
    }

    public byte[] getAllFilesAsZip(String id) {
        List<DocMeta> docList = docMetaRepository.getAllFiles(id);
        HashMap<String, List<File>> filesMap = new HashMap<>();
        docList.forEach(docMeta -> {
            File downloadedFile = documentService.downloadFileViaSignedUrl(docMeta.getFileId(), docMeta.getFileName());
            if (downloadedFile != null) {
                filesMap.computeIfAbsent(docMeta.getCategory(), k -> new ArrayList<>())
                        .add(downloadedFile);
            }
        });
        Path tempFolderPath = createTempFolder();
        filesMap.forEach((category, files) -> {
            Path categoryFolder = tempFolderPath.resolve(category);
            createFolder(categoryFolder);
            files.forEach(file -> {
                try {
                    Files.copy(file.toPath(), categoryFolder.resolve(file.getName()));
                } catch (IOException e) {
                    e.printStackTrace(); // Handle the exception appropriately
                }
            });
        });

        // Create a ZIP archive from the contents of the temporary folder
        byte[] zipBytes = createZipArchive(tempFolderPath);
        // Cleanup: Delete temporary folder
        deleteFolder(tempFolderPath);
        // Cleanup : Delete files
        filesMap.forEach((category, files) -> {
            deleteFiles(files);
        });
        return zipBytes;

    }

    public HashMap<String,List<DocMeta>> getAllCustomerFiles(String id){
        List<DocMeta> docList= docMetaRepository.getAllDraftFiles(id);
        HashMap<String,List<DocMeta>> labelFileMap=new HashMap();
        for (DocMeta docMeta:docList) {
            if(docMeta.getLabel()!=null){
                if(labelFileMap.get(docMeta.getLabel())==null){
                    labelFileMap.put(docMeta.getLabel(),new ArrayList<>());
                }
                List<DocMeta> currentList=labelFileMap.get(docMeta.getLabel());
                currentList.add(docMeta);
            }
        }
        return labelFileMap;
    }


    public HashMap<String,List<DocMeta>> getAllDraftFiles(String id){
        List<DocMeta> docList= docMetaRepository.getAllDraftFiles(id);
        HashMap<String,List<DocMeta>> labelFileMap=new HashMap();
        for (DocMeta docMeta:docList) {
            if(docMeta.getLabel()!=null){
                if(labelFileMap.get(docMeta.getLabel())==null){
                    labelFileMap.put(docMeta.getLabel(),new ArrayList<>());
                }
                List<DocMeta> currentList=labelFileMap.get(docMeta.getLabel());
                currentList.add(docMeta);
            }
        }
        return labelFileMap;
    }


    private Path createTempFolder() {
        try {
            Path folderPath = Path.of("./tempFiles");
            if (Files.exists(folderPath)) {
                // delete it to remove any prexisting files
                Files.deleteIfExists(folderPath);
            }
            return Files.createDirectory(folderPath);
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
            return null;
        }
    }

    private void createFolder(Path folderPath) {
        try {
            Files.createDirectories(folderPath);
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
        }
    }

    private void deleteFolder(Path folderPath) {
        try {
            Files.walk(folderPath)
                    .sorted((p1, p2) -> -p1.compareTo(p2)) // Reverse order for deleting files first
                    .map(Path::toFile)
                    .forEach(File::delete);
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
        }
    }

    private void deleteFiles(List<File> files) {
        for (File file : files) {
            if (file.exists()) {
                if (file.delete()) {
                    System.out.println("Deleted file: " + file.getAbsolutePath());
                } else {
                    System.err.println("Failed to delete file: " + file.getAbsolutePath());
                }
            } else {
                System.err.println("File not found: " + file.getAbsolutePath());
            }
        }
    }

    private byte[] createZipArchive(Path sourceFolderPath) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {

            Files.walk(sourceFolderPath)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            ZipEntry zipEntry = new ZipEntry(sourceFolderPath.relativize(path).toString());
                            zipOutputStream.putNextEntry(zipEntry);
                            Files.copy(path, zipOutputStream);
                            zipOutputStream.closeEntry();
                        } catch (IOException e) {
                            e.printStackTrace(); // Handle the exception appropriately
                        }
                    });

            zipOutputStream.finish();
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace(); // Handle the exception appropriately
            return null;
        }
    }

    public HashMap<String, String> generateDocMetaForOrders() throws ServiceException {
        List<CustomerOrder> customerOrders = customerOrderRepository.findAll();
        // perform doc type check
        validateDocTypes();
        HashMap<String, String> map = new HashMap<>();
        // iterate over all customer orders
        for (CustomerOrder order : customerOrders) {
            HashMap<String, String> meta = new HashMap<>();
            meta.put("customerOrderId", order.getId());
            if (order.getFinalDocuments() != null) {
                for (String docType : order.getFinalDocuments().keySet()) {
                    List<com.mstack.keystone.model.dto.File> documents = order.getFinalDocuments().get(docType);
                    documents.forEach(document -> {
                        generateDocMetaForDocuments(docType, docType.replace("_", " "), "Final", document, order.getId(), meta);
                    });
                }
            }
            if (order.getDraftDocuments() != null) {
                for (String docType : order.getDraftDocuments().keySet()) {
                    List<com.mstack.keystone.model.dto.File> documents = order.getDraftDocuments().get(docType);
                    documents.forEach(document -> {
                        generateDocMetaForDocuments(docType, docType.replace("_", " "), "Draft", document, order.getId(), meta);
                    });
                }
            }
        }
        map.put("Status", "all records updated successfully ");
        return map;
    }


    private void generateDocMetaForDocuments(String docType, String displayName, String state, com.mstack.keystone.model.dto.File file, String entityId, HashMap meta) {
        DocMeta docMeta = new DocMeta(docType, displayName, null, null, state, EntityType.CUSTOMER_ORDER, entityId, meta, file.getFileId(), file.getName());
        // only create doc meta if it doesn't exist earlier
        if (getDocMetaByFileId(file.getFileId()) == null) {
            createDocMeta(docMeta);
        } else System.out.println("Doc Meta already present for file id " + file.getFileId());
    }

    private void validateDocTypes() throws ServiceException {
        HashSet<String> docTypeSet = new HashSet<>();
        List<CustomerOrder> customerOrders = customerOrderRepository.findAll();
        customerOrders.forEach(customerOrder -> {
            if (customerOrder.getDraftDocuments() != null) {
                docTypeSet.addAll(customerOrder.getDraftDocuments().keySet());
            }
            if (customerOrder.getFinalDocuments() != null) {
                docTypeSet.addAll(customerOrder.getFinalDocuments().keySet());
            }
        });
        for (String docType : docTypeSet) {
            if (docEngineTemplateService.getTemplateByDocType(docType).isEmpty()) {
                throw new ServiceException("Template doesn't exist for doctpye " + docType, 500);
            }
        }
    }


    public List<DocMeta> findByCustomerOrderIdAndDocType(String customerOrderId, String docType) {
        return mongoQueries.findByFields(Map.ofEntries(
                Map.entry("entityType", List.of("CUSTOMER_ORDER")),
                Map.entry("entityId", List.of(customerOrderId)),
                Map.entry("docType", List.of(docType)),
                Map.entry("deleted", List.of(false))
        ), null, DocMeta.class);
    }

    public List<DocMeta> getFilesByCustomerOrderId(String customerOrderId) {
        return docMetaRepository.getFileByEntityTypeAndEntityId("CUSTOMER_ORDER", customerOrderId);
    }
}
