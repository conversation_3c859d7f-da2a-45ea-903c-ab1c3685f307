package com.mstack.keystone.service.catalogue;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.order.OrderBook;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

public interface IProductService {
    Product createProduct(Product product);
    Product getProductById(String productId) throws IllegalAccessException;
    Product updateProduct(String productId, Product updatedProduct);

    void deleteProduct(String productId) throws ServiceException;
    Page<Product> filterProducts(FilterRequest filterRequest);
    Product getProductByProductName(String productName);
    void processCsv(MultipartFile file);
}
