package com.mstack.keystone.service.catalogue;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.catalogue.ProductRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.utils.CommonUtils;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class ProductService  implements IProductService {

    @Autowired
    ProductRepository productRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    CommonUtils commonUtils;

    @Override
    @SneakyThrows
    public Product createProduct(Product product) {
        // trim product name
        product.setTradeName(product.getTradeName().trim());
        if (productRepository.findByExactTradeName(product.getTradeName()) != null) {
            throw new ServiceException("Failed to create product ,product with given product name already exists "+product.getTradeName(),400);
        }
        product.setProductId(commonUtils.CreateID("PR", mongoQueries.getCount(productRepository)));
        return mongoQueries.saveEntity(productRepository, product);
    }

    @Override
    public Product getProductById(String id) throws IllegalAccessException {
        return mongoQueries.getEntity(productRepository, id);
    }

    @Override
    @SneakyThrows
    public Product updateProduct(String productId, Product updatedProduct) {
        return mongoQueries.updateEntity(productRepository, productId, updatedProduct);
    }

    @Override
    public void deleteProduct(String productId) throws ServiceException {
        validateDeleteRequest(productId);
        mongoQueries.softDeleteById(productRepository, productId, Product.class);
    }

    private void validateDeleteRequest(String productId) throws ServiceException {
        Pageable pageable = PageRequest.of(0, 10000);
        Page<OrderBook> orderBooks = mongoQueries.findBySearch(new HashMap<>() {{
            put("products.product._id", new ObjectId(productId));
        }}, pageable, OrderBook.class);
        if (!orderBooks.isEmpty()) throw new ServiceException("Customer PO's still include this proudct ", 422);
        Page<CustomerOrder> customerOrders = mongoQueries.findBySearch(new HashMap<>() {{
            put("products.product._id", productId);
        }}, pageable, CustomerOrder.class);
        if (!customerOrders.isEmpty()) throw new ServiceException("Customer DO's still include this proudct ", 422);
        Page<SupplierOrderBook> supplierOrderBooks = mongoQueries.findBySearch(new HashMap<>() {{
            put("products.product._id", productId);
        }}, pageable, SupplierOrderBook.class);
        if (!supplierOrderBooks.isEmpty()) throw new ServiceException("Supplier PO's still include this proudct ", 422);
        Page<SupplierOrder> supplierOrders = mongoQueries.findBySearch(new HashMap<>() {{
            put("products.product._id", productId);
        }}, pageable, SupplierOrder.class);
        if (!supplierOrders.isEmpty()) throw new ServiceException("Supplier DO's still include this proudct ", 422);
    }

    @Override
    public Page<Product> filterProducts(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<Product> page = mongoQueries.findByFiltersAndSearch(
            request.getFilters(),
            request.getSearch(),
            pageable,
            Product.class
        );
        return page;
    }

    @Override
    public Product getProductByProductName(String productName) {
        productName=productName.trim();
        return productRepository.findByTradeName(productName);
    }

    @Override
    @SneakyThrows
    public void processCsv(MultipartFile file) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            // Assuming CSV structure: header line followed by data lines
            String header = reader.readLine();
            String line;
            while ((line = reader.readLine()) != null) {
                String[] data = line.split(",");
                // Process and save data to the database
                Product product=new Product();
                product.setTradeName(data[0].toString());
                product.setGrade(data[1].toString());
                product.setCasNumber(data[2].toString());
                HashMap<String,String> map=new HashMap<>();
                map.put("category",data[3].toString().trim());
                List list=new ArrayList<>();
                list.add(map);
                product.setCategories(list);
                try{
                    createProduct(product);
                }
                catch (Exception e){
                    System.out.println(e);
                }
            }
        }
    }
}
