package com.mstack.keystone.service.inventory;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.dto.AllocatedInventoryItem.*;
import com.mstack.keystone.model.enums.BatchType;
import com.mstack.keystone.model.enums.OBBatchType;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.model.repository.inventory.*;
import com.mstack.keystone.model.repository.order.*;
import com.mstack.keystone.repository.OBProductBatchRepository;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.catalogue.ProductRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.inventory.InventoryProductRepository;
import com.mstack.keystone.repository.inventory.InventoryRepository;
import com.mstack.keystone.repository.inventory.InventoryTransactionRepo;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.SupplierOrderBookRepo;
import com.mstack.keystone.repository.order.SupplierOrderRepository;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.service.batchDetails.OBProductBatchService;
import com.mstack.keystone.service.order.OrderBookService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InventoryProductService {
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    InventoryProductRepository inventoryProductRepository;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;
    @Autowired
    InventoryTransactionRepo inventoryTransactionRepo;
    @Autowired
    SupplierOrderRepository supplierOrderRepository;
    @Autowired
    PackagingRepository packagingRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    OBProductBatchRepository obProductBatchRepository;
    @Autowired
    SupplierOrderBookRepo supplierOrderBookRepo;
    @Autowired
    OrderBookService orderBookService;
    @Autowired
    InventoryRepository inventoryRepository;


    @Autowired
    OBProductBatchService obProductBatchService;

    @SneakyThrows
    public InventoryProduct addProductToInventory(InventoryProduct inventoryProduct) {
        // Implementation to add a product to the inventory and return it
        InventoryProduct product = inventoryProductRepository.findByPackagingIdAndProductIdAndInventoryId(inventoryProduct.getPackagingId(), inventoryProduct.getProductId(), inventoryProduct.getInventoryId());
        if (product != null) {
            throw new ServiceException("Cannot create the inventory product as this product already exists", 500);
        }
        return mongoQueries.saveEntity(inventoryProductRepository, inventoryProduct);
    }


    public InventoryProduct addProductQuantityToInventory(OrderBookEntry orderedProduct, String inventoryId, OrderBook orderBook, SupplierOrderBook supplierOrderBook) {
        String productId = orderedProduct.getProduct().getId();
        String packagingId = orderedProduct.getPackaging().getId();
        InventoryProduct inventoryProduct = inventoryProductRepository.findByPackagingIdAndProductIdAndInventoryId(packagingId, productId, inventoryId);
        if (inventoryProduct == null) {
            inventoryProduct = new InventoryProduct();
            inventoryProduct.setProductName(orderedProduct.getProduct().getTradeName());
            inventoryProduct.setProductId(productId);
            inventoryProduct.setPackagingId(packagingId);
            inventoryProduct.setPackagingName(orderedProduct.getPackaging().getPackagingName());
            inventoryProduct.setInventoryId(inventoryId);
            inventoryProduct.setUnits(0.00);
            addProductToInventory(inventoryProduct);
        }
        String transactionId = "TRA" + mongoQueries.getCount(inventoryTransactionRepo);
        // TODO verify for customerDispatchOrderId
        inventoryProduct.setUnits(inventoryProduct.getUnits() + orderedProduct.getUnits());
        InventoryProductTransaction inventoryProductTransaction = new InventoryProductTransaction(transactionId, inventoryProduct.getId(), orderedProduct.getUnits(), "IN",
                orderBook.getId(), supplierOrderBook.getId(), orderBook.getPurchaseOrderNumber(), orderedProduct.getId());
        mongoQueries.saveEntity(inventoryTransactionRepo, inventoryProductTransaction);
        // Implementation to add a product to the inventory and return it
        InventoryProduct inventoryProduct1 = mongoQueries.updateEntity(inventoryProductRepository, inventoryProduct.getId(), inventoryProduct);
        return inventoryProduct1;
    }


    @SneakyThrows
    public InventoryProduct removeProductQuantityFromInventory(InventoryOutOrder order, InventoryOutOrderProduct inventoryOutOrderProduct) {
        // TODO GB PLEASE CHECK FLOW
        InventoryProduct inventoryProduct = inventoryProductRepository.findByPackagingIdAndProductIdAndInventoryId(inventoryOutOrderProduct.getPackagingId(), inventoryOutOrderProduct.getProductId(), order.getInventoryId());
        double numberOfUnits = inventoryOutOrderProduct.getUnits();
        if (numberOfUnits > inventoryProduct.getUnits()) {
            throw new ServiceException("The quantity in inventory is less than required", 500);
        }
        inventoryProduct.setUnits(inventoryProduct.getUnits() - numberOfUnits);
        String transactionId = "TRA" + mongoQueries.getCount(inventoryTransactionRepo);
//        TODO - PS pass right parameters
        InventoryProductTransaction inventoryProductTransaction = new InventoryProductTransaction(transactionId, inventoryOutOrderProduct.getProductId(), inventoryOutOrderProduct.getUnits(), "OUT", order.getCustomerDispatchOrderId(), order.getId(), null, null);
        inventoryTransactionRepo.save(inventoryProductTransaction);
        return mongoQueries.updateEntity(inventoryProductRepository, inventoryProduct.getId(), inventoryProduct);
    }

    @SneakyThrows
    public void updateProductUnits(String orderBookId, List<InventoryProductAllocationDto> dtoList) {
        // check all first
        for (InventoryProductAllocationDto ipdto:dtoList) {
            checkEditPayload(orderBookId, ipdto);
        }
        for (InventoryProductAllocationDto ipdto:dtoList) {
            updateEditPayload(orderBookId, ipdto);
        }
    }

    @SneakyThrows
    private void updateEditPayload(String orderBookId, InventoryProductAllocationDto ipdto) {
        String packagingId = ipdto.getPackagingId();
        String productId = ipdto.getProductId();
        List<OBProductBatch> oldOBProductBatches = obProductBatchRepository.findAllocatedBatchForOrderBookID(productId, packagingId, orderBookId);
        // get and delete oldSupplier Ids
        for (AvailableSOB sob : ipdto.getAvailableSOBs()) {
            String inventoryId = sob.getInventoryId();
            OBProductBatch prevAllocated = obProductBatchRepository.findAllocatedBatchForOrderBookAndSupplier(productId, packagingId, inventoryId, orderBookId, sob.getId());
            OBProductBatch unAllocatedBatch = obProductBatchRepository.findUnAllocatedBatchForSO(productId, packagingId, "UN_ALLOCATED", sob.getId());
            double prevAllocatedUnits = prevAllocated != null ? prevAllocated.getUnits() : 0.0;
            double unAllocatedUnits = unAllocatedBatch != null ? unAllocatedBatch.getUnits() : 0.0;
            if (sob.getUnits() > 0) {
                if (prevAllocatedUnits == 0.0) {
                    //create new allocated
                    prevAllocated = new OBProductBatch();
                    prevAllocated.setSupplierOrderBookId(unAllocatedBatch.getSupplierOrderBookId());
                    prevAllocated.setInventoryId(unAllocatedBatch.getInventoryId());
                    prevAllocated.setInventoryInOrderId(unAllocatedBatch.getInventoryInOrderId());
                    prevAllocated.setCustomerOrderBookId(orderBookId);
                    prevAllocated.setType(OBBatchType.ALLOCATED.getValue());
                    prevAllocated.setUnits(sob.getUnits());
                    if (unAllocatedUnits - sob.getUnits() == 0) {
                        unAllocatedBatch.setUnits(0);
                        unAllocatedBatch.setDeleted(true);
                    } else {
                        unAllocatedBatch.setUnits(unAllocatedUnits - sob.getUnits());
                    }
                    prevAllocated=mongoQueries.saveEntity(obProductBatchRepository,prevAllocated);
                } else {
                    // set current units
                    prevAllocated.setUnits(sob.getUnits());
                    // add difference
                    unAllocatedBatch.setUnits(unAllocatedUnits - (sob.getUnits() - prevAllocatedUnits));
                }
                // save prev allocated
                mongoQueries.updateEntity(obProductBatchRepository, prevAllocated.getId(), prevAllocated);
                mongoQueries.updateEntity(obProductBatchRepository, unAllocatedBatch.getId(), unAllocatedBatch);
                // save unallocated
            }
        }

        List<String> oldSupplierIds = oldOBProductBatches.stream().map(OBProductBatch::getSupplierOrderBookId).collect(Collectors.toList());
        List<String> newSupplierIds = ipdto.getAvailableSOBs().stream().map(AvailableSOB::getId).collect(Collectors.toList());
        List<String> missingSupplierIds = new ArrayList<>(oldSupplierIds);
        missingSupplierIds.removeAll(newSupplierIds);
        oldOBProductBatches = oldOBProductBatches.stream().filter(batch -> missingSupplierIds.contains(batch.getSupplierOrderBookId())).collect(Collectors.toList());
        for (OBProductBatch ob : oldOBProductBatches) {
            OBProductBatch unAllocated = obProductBatchRepository.findUnAllocatedBatchForSO(ob.getProductId(), ob.getPackagingId(), "UN_ALLOCATED", ob.getSupplierOrderBookId());
            if (unAllocated != null) {
                unAllocated.setUnits(ob.getUnits() + unAllocated.getUnits());
                mongoQueries.updateEntity(obProductBatchRepository, unAllocated.getId(), unAllocated);
                ob.setDeleted(true);
            } else {
                ob.setType("UN_ALLOCATED");
            }
            mongoQueries.updateEntity(obProductBatchRepository, ob.getId(), ob);
        }
    }

    @SneakyThrows
    private void checkEditPayload(String orderBookId, InventoryProductAllocationDto ipdto) {
        //TODO- put checks if quantity is reduced shouldnot be reduced from the dispatch orders quantity mapped
        String packagingId = ipdto.getPackagingId();
        String productId = ipdto.getProductId();
        for (AvailableSOB sob : ipdto.getAvailableSOBs()) {
            String inventoryId = sob.getInventoryId();
            OBProductBatch prevAllocated = obProductBatchRepository.findAllocatedBatchForOrderBookAndSupplier(productId, packagingId, inventoryId, orderBookId, sob.getId());
            OBProductBatch unAllocatedBatch = obProductBatchRepository.findUnAllocatedBatchForSO(productId, packagingId, "UN_ALLOCATED", sob.getId());
            double prevAllocatedUnits = 0.0;
            double unAllocatedUnits = 0.0;
            if (prevAllocated != null) {
                prevAllocatedUnits = prevAllocated.getUnits();
            }
            if (unAllocatedBatch != null) {
                unAllocatedUnits = unAllocatedBatch.getUnits();
            }
            if (unAllocatedUnits < sob.getUnits() - prevAllocatedUnits) {
                throw new ServiceException("Not sufficient units for " + sob.getSupplierName() + " " + sob.getUnits(), 500);
            }
        }
    }

    public InventoryProduct getProductDetailFromInventory(String id) {
        // Implementation to retrieve and return a product from the inventory
        return mongoQueries.getEntity(inventoryProductRepository, id);
    }

    public InventoryProduct getProductDetailFromInventory(String inventoryId, String packagingId, String productId) {
        Map<String, List<Object>> filters = Map.ofEntries(
                Map.entry("inventoryId", List.of(inventoryId)),
                Map.entry("productId", List.of(productId)),
                Map.entry("packagingId", List.of(packagingId)),
                Map.entry("deleted", List.of(false))
        );
        List<InventoryProduct> inventoryProductList = mongoQueries.findByFields(filters, null, InventoryProduct.class);
        return inventoryProductList.get(0);
    }

//    public InventoryProduct updateProductInInventory(String id, InventoryProduct inventoryProduct) {
//        // Implementation to update and return the updated product in the inventory
//        return mongoQueries.updateEntity(inventoryProductRepository, id, inventoryProduct);
//    }

    public void removeProductFromInventory(String id) {
        // Implementation to remove a product from the inventory
        mongoQueries.softDeleteById(inventoryProductRepository, id, InventoryProduct.class);
    }


    public List<InventoryProduct> getAllProductsOfInventory(String inventoryId) {
        return inventoryProductRepository.findByInventoryId(inventoryId);
    }

    public List<ProductBatchDetail> getBatchDetails(String orderId, String inventoryId) {
        return productBatchDetailRepository.findByInventoryOrderId(orderId);
    }

    public Page<InventoryProductDto> filterInventoryProduct(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<InventoryProduct> page = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                InventoryProduct.class
        );
        List<InventoryProduct> products = page.stream().toList();
        List<InventoryProductDto> productsDtoList = new ArrayList<>();
        for (InventoryProduct inventoryProduct : products) {
            if (inventoryProduct.getUnits() == 0) continue;
            Product product = mongoQueries.getEntity(productRepository, inventoryProduct.getProductId());
            Packaging packaging = (inventoryProduct.getPackagingId() == null) ? null : mongoQueries.getEntity(packagingRepository, inventoryProduct.getPackagingId());
            Inventory inventory = mongoQueries.getEntity(inventoryRepository, inventoryProduct.getInventoryId());
            InventoryProductDto productDto = new InventoryProductDto(inventory, inventoryProduct.getId(),
                    product, packaging, inventoryProduct.getUnits());
            productsDtoList.add(productDto);
        }

        return new PageImpl<>(productsDtoList, pageable, page.getTotalElements());
    }

    @SneakyThrows
    public Page<ProductBatchDetail> getAllBatches(HashMap request) {
        Pageable pageable = PageRequest.of((Integer) request.getOrDefault("number", 0), (Integer) request.getOrDefault("size", 10));
        InventoryProduct inventoryProduct = mongoQueries.getEntity(inventoryProductRepository, request.get("inventoryProductId").toString());
        //TODO PS this is only for actual batches add type as key
        Page<ProductBatchDetail> page = productBatchDetailRepository.findUnAllocatedBatchForPackagingAndProductAndInventory(inventoryProduct.getPackagingId(), inventoryProduct.getProductId(), inventoryProduct.getInventoryId(), BatchType.UN_ALLOCATED.getValue(), pageable);
        return page;
    }

    @SneakyThrows
    public Page<InventoryTransactionDto> getAllTransactions(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<InventoryProductTransaction> page = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                InventoryProductTransaction.class
        );
        List<InventoryProductTransaction> list = page.stream().toList();
        List<InventoryTransactionDto> dtoList = new ArrayList<>();
        for (InventoryProductTransaction inventoryProductTransaction : list) {
            InventoryTransactionDto inventoryTransactionDto = new InventoryTransactionDto();
            inventoryTransactionDto.setTransactionId(inventoryProductTransaction.getTransactionId());
            inventoryTransactionDto.setTransactionType(inventoryProductTransaction.getTransactionType());
            inventoryTransactionDto.setId(inventoryProductTransaction.getId());
            inventoryTransactionDto.setUnits(inventoryProductTransaction.getUnits());
            inventoryTransactionDto.setMeta(inventoryProductTransaction.getMeta());
            inventoryTransactionDto.setEntity(getEntityName(inventoryProductTransaction));
            inventoryTransactionDto.setCreatedAt(inventoryProductTransaction.getCreatedAt());
            dtoList.add(inventoryTransactionDto);
        }
        return new PageImpl<>(dtoList, pageable, page.getTotalPages());
    }

    private String getEntityName(InventoryProductTransaction inventoryProductTransaction) {
        if (inventoryProductTransaction.getTransactionType().equals("IN")) {
            String supplierDispatchOrderId = inventoryProductTransaction.getMeta().getOrDefault("supplierDispatchOrderId", "").toString();
            if (supplierDispatchOrderId == null || supplierDispatchOrderId.equals("")) {
                return "";
            }
            SupplierOrder order = mongoQueries.getEntity(supplierOrderRepository, supplierDispatchOrderId);
            return order.getSupplier() != null ? order.getSupplier().getName() : "";
        }
        String customerDispatchOrderId = inventoryProductTransaction.getMeta().getOrDefault("customerDispatchOrderId", "").toString();
        if (customerDispatchOrderId == null || customerDispatchOrderId.equals("")) {
            return "";
        }
        CustomerOrder customerOrder = mongoQueries.getEntity(customerOrderRepository, customerDispatchOrderId);
        return customerOrder != null ? customerOrder.getCustomer().getName() : "";
    }

    public HashMap getInventoryProductStockDetail(String inventoryProductId) {
        InventoryProduct product = mongoQueries.getEntity(inventoryProductRepository, inventoryProductId);
        String packagingId = product.getPackagingId();
        String productId = product.getProductId();
        // get all batches which are unallocated
        List<OBProductBatch> batches = obProductBatchRepository.findUnAllocatedBatchesByProductIdAndInventoryIdAndType(productId, product.getInventoryId(), packagingId, "UN_ALLOCATED");
        // get all supplierIds
        List<String> uniqueSupplierOrderBookIds = batches.stream()
                .map(OBProductBatch::getSupplierOrderBookId)
                .distinct()
                .collect(Collectors.toList());
        // get all PO numbers
        List<SupplierOrderBook> supplierOrderBooks = supplierOrderBookRepo.findAllById(uniqueSupplierOrderBookIds);
        List<String> poNumbers = supplierOrderBooks.stream()
                .map(SupplierOrderBook::getPurchaseOrderNumber)
                .distinct()
                .collect(Collectors.toList());
        List<SupplierOrder> supplierDispatchOrders = supplierOrderRepository.findAllBySupplierPos(poNumbers);
        List<String> uniqueSupplierDispatchOrderIds = supplierDispatchOrders.stream()
                .map(SupplierOrder::getId)
                .distinct()
                .collect(Collectors.toList());
        List<SupplierOrder> supplierOrders = supplierOrderRepository.findAllById(uniqueSupplierDispatchOrderIds);
        Map<String, SupplierOrder> supplierOrderMap = supplierOrders.stream()
                .collect(Collectors.toMap(SupplierOrder::getId, supplierOrder -> supplierOrder));

        HashMap<String, Double> result = new HashMap<>();
        result.put("stockPORaised", 0.0);
        result.put("stockUnderProduction", 0.0);
        result.put("stockInTransit", 0.0);
        result.put("stockInInventory", 0.0);
        result.put("totalStock", 0.0);

        for (OBProductBatch ob:batches) {
            result.put("totalStock",result.get("totalStock")+ob.getUnits());
        }
        //TODO stock detail
//        for (ProductBatchDetail productBatchDetail : batches) {
//            OrderStatus status = supplierOrderMap.get(productBatchDetail.getSupplierOrderDispatchId()).getStatus();
//            if (status.getValue() == OrderStatus.INITIATED.getValue()) {
//                result.put("stockPORaised", result.getOrDefault("stockPORaised", 0.0) + productBatchDetail.getUnits());
//            } else if (status.getValue() < OrderStatus.DISPATCHED.getValue()&&status.getValue() >= OrderStatus.UNDER_PRODUCTION.getValue()) {
//                result.put("stockUnderProduction", result.getOrDefault("stockUnderProduction", 0.0) + productBatchDetail.getUnits());
//            } else if (status.getValue() == OrderStatus.DISPATCHED.getValue()) {
//                result.put("stockInTransit", result.getOrDefault("stockInTransit", 0.0) + productBatchDetail.getUnits());
//            } else {
//                result.put("stockInInventory", result.getOrDefault("stockInInventory", 0.0) + productBatchDetail.getUnits());
//            }
//            result.put("totalStock", result.getOrDefault("totalStock", 0.0) + productBatchDetail.getUnits());
//        }
        return result;
    }

//    public void addOrUpdateProductFromOrder(ProductBatchDetail productBatchDetail, String inventoryId) {
//        // update inventory details
//        // check if inventory has product
//        InventoryProduct inventoryProduct = getProductDetailFromInventory(inventoryId, productBatchDetail.getPackagingId(), productBatchDetail.getProductId());
//        if (inventoryProduct != null) {
//            addBatchToProduct(inventoryProduct, productBatchDetail);
//        } else {
//            inventoryProduct = createInventoryProduct(inventoryId, productBatchDetail);
//        }
//        productBatchDetail.setInventoryProductId(inventoryProduct.getId());
//        // update product batch details
//        mongoQueries.updateEntity(productBatchDetailRepository, productBatchDetail.getId(), productBatchDetail);
//    }

//    private InventoryProduct createInventoryProduct(String inventoryId, ProductBatchDetail productBatchDetail) {
//        InventoryProduct product = new InventoryProduct();
//        product.setInventoryId(inventoryId);
//        product.setProductId(productBatchDetail.getProductId());
//        product.setPackingId(productBatchDetail.getPackagingId());
//        product.setUnits((long) productBatchDetail.getUnits());
//        return addProductToInventory(product);
//    }

//    private void addBatchToProduct(InventoryProduct inventoryProduct, ProductBatchDetail productBatchDetail) {
//        // update total count
//        inventoryProduct.setUnits((long) (inventoryProduct.getUnits() + productBatchDetail.getUnits()));
//        updateProductInInventory(inventoryProduct.getId(), inventoryProduct);
//    }


    public HashMap checkInventory(String orderBookId) {
        HashMap map = new HashMap<>();
        OrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        List<InventoryProduct> inventoryProducts = new ArrayList<>();
//        HashMap<String, List<SupplierOrderBook>> supplierOrderbookQuantity = new HashMap<>();
        for (OrderBookEntry orderbookEntry : orderBook.getProducts()) {
            List<OBProductBatch> obProductBatch = obProductBatchRepository.findAllocatedBatchForOrderBookID(orderbookEntry.getProduct().getId(), orderbookEntry.getPackaging().getId(), orderBookId);
            if (obProductBatch != null && obProductBatch.size() > 0) {
                continue;
            }
            List<InventoryProduct> inventoryProductList = inventoryProductRepository.findByPackagingIdAndProductId(orderbookEntry.getPackaging().getId(), orderbookEntry.getProduct().getId());
            for (InventoryProduct ip : inventoryProductList) {
                if (ip.getUnits() > 0) {
                    inventoryProducts.add(ip);
                }
            }
        }
        List<InventoryProductDto> productsDtoList = new ArrayList<>();
        for (InventoryProduct inventoryProduct : inventoryProducts) {
            Product product = mongoQueries.getEntity(productRepository, inventoryProduct.getProductId());
            Packaging packaging = (inventoryProduct.getPackagingId() == null) ? null : mongoQueries.getEntity(packagingRepository, inventoryProduct.getPackagingId());
            Inventory inventory = mongoQueries.getEntity(inventoryRepository, inventoryProduct.getInventoryId());
            InventoryProductDto productDto = new InventoryProductDto(inventory, inventoryProduct.getId(),
                    product, packaging, inventoryProduct.getUnits());
            productsDtoList.add(productDto);
        }
        map.put("inventoryProductList", productsDtoList);
//        map.put("supplierOrderBook")
        return map;
    }

    public List<InventoryProductAllocationDto> getAvaialableUnitsForOrderBookId(String orderBookId) {
        OrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        List<InventoryProductAllocationDto> res = new ArrayList<>();
        for (OrderBookEntry orderBookEntry : orderBook.getProducts()) {
            // find avialable units per supplier
            InventoryProductAllocationDto productAllocationDto = new InventoryProductAllocationDto();
            productAllocationDto.setProductName(orderBookEntry.getProduct().getTradeName());
            productAllocationDto.setProductId(orderBookEntry.getProduct().getId());
            productAllocationDto.setPackagingName(orderBookEntry.getPackaging().getPackagingName());
            productAllocationDto.setPackagingId(orderBookEntry.getPackaging().getId());
            List<OBProductBatch> unAllocatedBatches = obProductBatchService.getUnAllocatedBatchesByProductIdAndPackagingId(orderBookEntry.getProduct().getId(), orderBookEntry.getPackaging().getId());
            List<OBProductBatch> allocatedBatches = obProductBatchService.getAllocatedBatchesByOBIdAndProductIdAndPackagingId(orderBookId, orderBookEntry.getProduct().getId(), orderBookEntry.getPackaging().getId());
            productAllocationDto.setAvailableSOBs(getAvaialbeSOBS(unAllocatedBatches, allocatedBatches));
            productAllocationDto.setUnits(getTotalUnitsFromSOBS(productAllocationDto.getAvailableSOBs()));
            res.add(productAllocationDto);
        }
        return res;
    }

    private double getTotalUnitsFromSOBS(List<AvailableSOB> availableSOBs) {
        double totalUnits = 0;
        for (AvailableSOB availableSOB : availableSOBs) totalUnits += availableSOB.getUnits();
        return totalUnits;
    }

    private List<AvailableSOB> getAvaialbeSOBS(List<OBProductBatch> unAllocatedBatches, List<OBProductBatch> allocatedBatches) {
        List<AvailableSOB> list = new ArrayList<>();
        HashMap<String, Double> orderBookUnitsMap = new HashMap<String, Double>();
        unAllocatedBatches.forEach(obProductBatch -> {
            orderBookUnitsMap.put(obProductBatch.getSupplierOrderBookId(), orderBookUnitsMap.getOrDefault(obProductBatch.getSupplierOrderBookId(), 0.00) + obProductBatch.getUnits());
        });
        allocatedBatches.forEach(obProductBatch -> {
            orderBookUnitsMap.put(obProductBatch.getSupplierOrderBookId(), orderBookUnitsMap.getOrDefault(obProductBatch.getSupplierOrderBookId(), 0.00) + obProductBatch.getUnits());
        });
        for (String sobId : orderBookUnitsMap.keySet()) {
            SupplierOrderBook sob = supplierOrderBookRepo.findById(sobId).orElse(null);
            AvailableSOB availableSOB = new AvailableSOB();
            if (sob.getSupplier() != null)
                availableSOB.setSupplierName(sob.getSupplier().getName());
            availableSOB.setOrderBookId(sob.getOrderBookId());
            availableSOB.setId(sob.getId());
            availableSOB.setUnits(orderBookUnitsMap.get(sobId));
            list.add(availableSOB);
        }
        return list;
    }

    public List<AllocatedInventoryItem> getAllocatedInventory(String orderbookId) {
        OrderBook orderBook = orderBookService.getOrderBookById(orderbookId);
        List<AllocatedInventoryItem> allocatedInventoryItems = new ArrayList<>();
        for (OrderBookEntry orderbookEntry : orderBook.getProducts()) {
            List<OBProductBatch> productBatches = new ArrayList<>();
            if(orderbookEntry.getPackaging() !=null) {
                 productBatches = obProductBatchRepository.findAllocatedBatchForOrderBookIDViaInventory(
                    orderbookEntry.getProduct().getId(), orderbookEntry.getPackaging().getId(),
                    orderbookId);
            }
            if(productBatches.size()==0){
                continue;
            }
            AllocatedInventoryItem allocatedInventoryItem = new AllocatedInventoryItem();
            // TODO GB WHICH CREATED DATE SHOULD IT BE ?
            allocatedInventoryItem.setCreatedAt(orderBook.getCreatedAt());
            allocatedInventoryItem.setProudctDetail(new ProudctDetail(orderbookEntry.getProduct().getTradeName(), orderbookEntry.getProduct().getId()));
            if(orderbookEntry.getPackaging()!=null) {
                allocatedInventoryItem.setPackagingDetail(
                    new PackagingDetail(orderbookEntry.getPackaging().getPackagingName(),
                        orderbookEntry.getPackaging().getId()));
            }
            allocatedInventoryItem.setAllocatedUnitDetails(new ArrayList<>());
            if (!productBatches.isEmpty()) {
                for (OBProductBatch obProductBatch : productBatches) {
                    SupplierOrderBook supplierOrderBook = supplierOrderBookRepo.findById(obProductBatch.getSupplierOrderBookId()).orElse(null);
                    AllocatedUnitDetail allocatedUnitDetail = getAllocatedUnitDetail(obProductBatch, supplierOrderBook);
                    allocatedInventoryItem.getAllocatedUnitDetails().add(allocatedUnitDetail);
                }
            }
            allocatedInventoryItems.add(allocatedInventoryItem);
        }
        return allocatedInventoryItems;
    }

    private static AllocatedUnitDetail getAllocatedUnitDetail(OBProductBatch obProductBatch, SupplierOrderBook supplierOrderBook) {
        AllocatedUnitDetail allocatedUnitDetail = new AllocatedUnitDetail();
        allocatedUnitDetail.setOrderBookId(supplierOrderBook.getOrderBookId());
        allocatedUnitDetail.setOrderBookUID(supplierOrderBook.getId());
        allocatedUnitDetail.setUnitsAllocated(obProductBatch.getUnits());
        allocatedUnitDetail.setObBatchId(obProductBatch.getId());
        Supplier supplier = supplierOrderBook.getSupplier();
        allocatedUnitDetail.setSupplierName(supplier.getName());
        allocatedUnitDetail.setSupplierUID(supplier.getId());
        return allocatedUnitDetail;
    }


    public InventoryProductAllocationDto getAllocatedInventoryForProduct(String orderBookId, String productId, String packagingId) {
        OrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        InventoryProductAllocationDto inventoryProductAllocationDto = new InventoryProductAllocationDto();
        List<OBProductBatch> unAllocated = obProductBatchRepository.findUnAllocatedBatchesByProductIdAndPackagingId(productId, packagingId, "UN_ALLOCATED");
        List<OBProductBatch> allocated = obProductBatchRepository.findAllocatedBatchForOrderBookID(productId, packagingId, orderBookId);
        HashMap<String, OBProductBatch> map = new HashMap<>();
        for (OBProductBatch batch : unAllocated) {
            if (map.get(batch.getSupplierOrderBookId()) == null) {
                map.put(batch.getSupplierOrderBookId(), batch);
            } else {
                OBProductBatch existing = map.get(batch.getSupplierOrderBookId());
                existing.setUnits(batch.getUnits() + existing.getUnits());
            }
        }
        HashMap<String,OBProductBatch> allocatedMap=new HashMap<>();
        for (OBProductBatch batch : allocated) {
            allocatedMap.put(batch.getSupplierOrderBookId(),batch);
            if (map.get(batch.getSupplierOrderBookId()) == null) {
                map.put(batch.getSupplierOrderBookId(), batch);
            } else {
                OBProductBatch existing = map.get(batch.getSupplierOrderBookId());
                existing.setUnits(batch.getUnits() + existing.getUnits());
            }
        }
        List<OrderBookEntry> orderbookEntry = orderBook.getProducts().stream().filter(ob -> ob.getPackaging().getId().equals(packagingId) && ob.getProduct().getId().equals(productId)).collect(Collectors.toList());
        inventoryProductAllocationDto.setProductName(orderbookEntry.get(0).getProduct().getTradeName());
        inventoryProductAllocationDto.setProductId(orderbookEntry.get(0).getProduct().getId());
        inventoryProductAllocationDto.setPackagingName(orderbookEntry.get(0).getPackaging().getPackagingName());
        inventoryProductAllocationDto.setPackagingId(orderbookEntry.get(0).getPackaging().getId());
        List<AvailableSOB> availableSOBS = new ArrayList<>();
        for (String supplierOrderId : map.keySet()) {
            SupplierOrderBook supplierOrderBook = mongoQueries.getEntity(supplierOrderBookRepo, map.get(supplierOrderId).getSupplierOrderBookId());
            AvailableSOB availableSOB = new AvailableSOB();
            if (supplierOrderBook.getSupplier() != null)
                availableSOB.setSupplierName(supplierOrderBook.getSupplier().getName());
            availableSOB.setOrderBookId(supplierOrderBook.getOrderBookId());
            availableSOB.setId(supplierOrderBook.getId());
            availableSOB.setUnits(map.get(supplierOrderId).getUnits());
            availableSOB.setSelectedUnits(allocatedMap.get(supplierOrderId)!=null?allocatedMap.get(supplierOrderId).getUnits():0.0);
            availableSOBS.add(availableSOB);
        }
        inventoryProductAllocationDto.setAvailableSOBs(availableSOBS);
        inventoryProductAllocationDto.setUnits(getTotalUnitsFromSOBS(availableSOBS));
        return inventoryProductAllocationDto;
    }

    public void allocateAvaialableUnitsForOrderBookId(String orderBookId, List<InventoryProductAllocationDto> inventoryProductAllocations) throws ServiceException {
        for (InventoryProductAllocationDto product : inventoryProductAllocations) {
            for (AvailableSOB availableSOB : product.getAvailableSOBs()) {
                //TODO - verify this PS
                List<OBProductBatch> obProductBatch = obProductBatchRepository.findAllocatedBatchForOrderBookID(product.getProductId(), product.getPackagingId(), orderBookId);
                if (obProductBatch != null && obProductBatch.size() > 0) {
                    continue;
                }
                // only one unallocated batch is assumed per supplier ,package ,product
                OBProductBatch unAllocatedBatch = obProductBatchService.getBatches(Map.ofEntries(
                        Map.entry("supplierOrderBookId", List.of(availableSOB.getId())),
                        Map.entry("packagingId", List.of(product.getPackagingId())),
                        Map.entry("productId", List.of(product.getProductId())),
                        Map.entry("type", List.of(OBBatchType.UNALLOCATED.getValue())),
                        Map.entry("deleted", List.of(false))
                )).get(0);
                if (availableSOB.getSelectedUnits() == 0) throw new ServiceException("Cannot allocate 0 units ", 400);
                obProductBatchService.allocateBatch(unAllocatedBatch.getId(), orderBookId, availableSOB.getSelectedUnits());
                // reduce inventory product count
                removeProductQuantityFromInventory(orderBookId, unAllocatedBatch.getInventoryId(), unAllocatedBatch.getProductId(), unAllocatedBatch.getPackagingId(), availableSOB.getSelectedUnits(), availableSOB.getId());
            }
        }
    }

    private void removeProductQuantityFromInventory(String orderBookId, String inventoryId, String productId, String packagingId, double units, String supplerOrderBookId) {
        InventoryProduct product = getProductDetailFromInventory(inventoryId, packagingId, productId);
        product.setUnits(product.getUnits() - units);
        OrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        // create product transaction
        InventoryProductTransaction inventoryProductTransaction = new InventoryProductTransaction(generateTransactionId(), product.getId(), units, "OUT", orderBook.getId(), supplerOrderBookId, orderBook.getPurchaseOrderNumber(), null);
        mongoQueries.saveEntity(inventoryTransactionRepo, inventoryProductTransaction);
        mongoQueries.updateEntity(inventoryProductRepository, product.getId(), product);
    }

    private String generateTransactionId() {
        return "TRA" + mongoQueries.getCount(inventoryTransactionRepo);
    }

    public void moveToInventory(CustomerOrder customerOrder,String oldOrderBookId,CustomerOrder newCustomerOrder,String newOrderBookId, boolean fromInventory) {
        if(fromInventory){
            moveToInventoryFromMTS(customerOrder,oldOrderBookId,newCustomerOrder,newOrderBookId);
        }
        else{
            moveToInventoryFromMTO(customerOrder,oldOrderBookId,newCustomerOrder,newOrderBookId);
        }
    }

    public void moveToInventoryFromMTS(CustomerOrder customerOrder,String oldOrderBookId,CustomerOrder newCustomerOrder,String newOrderBokkId){
        //TODO -fetch actual batch
        List<ProductBatchDetail> actualBatches=null;
        if(actualBatches!=null&actualBatches.size()>0){
            for (ProductBatchDetail batch:actualBatches) {
                ProductBatchDetail productBatchDetail=productBatchDetailRepository.findUnAllocatedProduct(batch.getBatchUID());
                if(productBatchDetail==null){
                    batch.setType("UN_ALLOCATED");
                }
                else {
                    productBatchDetail.setUnits(productBatchDetail.getUnits()+batch.getUnits());
                    batch.setDeleted(true);
                    productBatchDetailRepository.save(productBatchDetail);
                }
            }
            productBatchDetailRepository.saveAll(actualBatches);
        }
        //TODO -fetch virtual batch
        List<ProductBatchDetail> virtualBatches=null;
        if(virtualBatches!=null&virtualBatches.size()>0){
            for (ProductBatchDetail batch:virtualBatches) {
                ProductBatchDetail unAllocatedVirtualBatch=null;//get virtual batch unallocated
                if(unAllocatedVirtualBatch==null){
                    batch.setType("VIRTUAL_UN_ALLOCATED");
                }
                else{
                    batch.setDeleted(true);
                    unAllocatedVirtualBatch.setUnits(unAllocatedVirtualBatch.getUnits()+batch.getUnits());
                }
                batch.setCustomerOrderDispatchId(null);
                batch.setInventoryInOrderId(newCustomerOrder.getId());
                batch.setInventoryOutOrderId(null);
                SupplierOrder supplierOrder=mongoQueries.getEntity(supplierOrderRepository, batch.getSupplierOrderDispatchId());
                SupplierOrderBook supplierOrderBook=supplierOrderRepository.findByPONumber(supplierOrder.getPurchaseOrderNumber());
                OBProductBatch allocatedObProductBatch=obProductBatchRepository.findAllocatedBatchForOrderBookAndSupplier(batch.getProductId(), batch.getInventoryId(), batch.getPackagingId(),oldOrderBookId,supplierOrderBook.getId());
                OBProductBatch unallocatedObProductBatch=obProductBatchRepository.findUnAllocatedBatchForSupplierOrderBook(batch.getProductId(), batch.getInventoryId(), batch.getPackagingId(),supplierOrderBook.getId());
                if(unallocatedObProductBatch==null){
                    allocatedObProductBatch.setCustomerOrderBookId(null);
                    allocatedObProductBatch.setType("UN_ALLOCATED");
                }
                else{
                    unallocatedObProductBatch.setUnits(unallocatedObProductBatch.getUnits()+allocatedObProductBatch.getUnits());
                    allocatedObProductBatch.setDeleted(true);
                }
                obProductBatchRepository.save(allocatedObProductBatch);
                obProductBatchRepository.save(unallocatedObProductBatch);
            }
            productBatchDetailRepository.saveAll(virtualBatches);
        }
    }

    public void moveToInventoryFromMTO(CustomerOrder customerOrder,String oldOrderBookId,CustomerOrder newCustomerOrder,String newOrderBokkId){
        // replace all actual batch with customerOrderId to newCustomerOrderId
        List<ProductBatchDetail> actualBatches=null;
        if(actualBatches!=null&actualBatches.size()>0){
            for (ProductBatchDetail batch:actualBatches) {
                batch.setCustomerOrderDispatchId(null);
                batch.setInventoryInOrderId(newCustomerOrder.getId());
                batch.setInventoryOutOrderId(null);
            }
            productBatchDetailRepository.saveAll(actualBatches);
        }
        //TODO get Virtual Batches
        List<ProductBatchDetail> virtualBatches=null;
        if(virtualBatches!=null&virtualBatches.size()>0){
            for (ProductBatchDetail batch:virtualBatches) {
                batch.setCustomerOrderDispatchId(null);
                batch.setInventoryInOrderId(newCustomerOrder.getId());
                batch.setInventoryOutOrderId(null);
                SupplierOrder supplierOrder=mongoQueries.getEntity(supplierOrderRepository, batch.getSupplierOrderDispatchId());
                SupplierOrderBook supplierOrderBook=supplierOrderRepository.findByPONumber(supplierOrder.getPurchaseOrderNumber());
                OBProductBatch obProductBatch=obProductBatchRepository.findAllocatedBatchForOrderBookAndSupplier(batch.getProductId(), batch.getInventoryId(), batch.getPackagingId(),oldOrderBookId,supplierOrderBook.getId());
                if(obProductBatch!=null){
                    obProductBatch.setUnits(obProductBatch.getUnits()-batch.getUnits());
                    OBProductBatch newObProductBatch=new OBProductBatch();
                    newObProductBatch.setUnits(batch.getUnits());
                    newObProductBatch.setProductId(batch.getProductId());
                    newObProductBatch.setPackagingId(batch.getPackagingId());
                    newObProductBatch.setInventoryId(batch.getInventoryId());
                    newObProductBatch.setSupplierOrderBookId(batch.getSupplierOrderDispatchId());
                    newObProductBatch.setInventoryInOrderId(newOrderBokkId);
                    obProductBatchRepository.save(newObProductBatch);
                }
            }
            productBatchDetailRepository.saveAll(virtualBatches);
        }
        //replace all obproduct batch with orderBookId to new Orderbook id
        // replace all virtual batch with customerOrderId to newCustomerOrderId
        // add new transaction if required and add units to inventory product
    }

    public void unAllocateInventory(List<String> obBatchIds) throws ServiceException {
        validateUnAllocateRequest(obBatchIds);
        for (String batchId : obBatchIds) {
            OBProductBatch obProductBatch = obProductBatchRepository.findById(batchId).orElse(null);
            if (obProductBatch == null) {
                throw new ServiceException("Invalid batch id provided ", 400);
            }
            obProductBatchService.unAllocateBatch(batchId);
            addProductQuantityToInventory(obProductBatch.getCustomerOrderBookId(), obProductBatch.getInventoryId(), obProductBatch.getProductId(), obProductBatch.getPackagingId(), obProductBatch.getUnits(), obProductBatch.getSupplierOrderBookId());
        }
    }

    private void validateUnAllocateRequest(List<String> obBatchIds) throws ServiceException {
        for (String batchId : obBatchIds) {
            obProductBatchService.canUnAllocateBatch(batchId);
        }
    }

    private void addProductQuantityToInventory(String orderBookId, String inventoryId, String productId, String packagingId, double units, String supplerOrderBookId) {
        InventoryProduct product = getProductDetailFromInventory(inventoryId, packagingId, productId);
        product.setUnits(product.getUnits() + units);
        OrderBook orderBook = orderBookService.getOrderBookById(orderBookId);
        // create product transaction
        InventoryProductTransaction inventoryProductTransaction = new InventoryProductTransaction(generateTransactionId(), product.getId(), units, "IN", orderBook.getId(), supplerOrderBookId, orderBook.getPurchaseOrderNumber(), null);
        mongoQueries.saveEntity(inventoryTransactionRepo, inventoryProductTransaction);
        mongoQueries.updateEntity(inventoryProductRepository, product.getId(), product);
    }
}

