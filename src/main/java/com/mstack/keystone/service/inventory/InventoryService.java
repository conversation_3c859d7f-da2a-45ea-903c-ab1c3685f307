package com.mstack.keystone.service.inventory;

import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.repository.inventory.Inventory;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.inventory.InventoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class InventoryService {
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    InventoryRepository inventoryRepository;

    public Inventory createInventory(Inventory inventory) {
        // Implementation to create and return the inventory
        return mongoQueries.saveEntity(inventoryRepository, inventory);
    }

    public Inventory getInventoryById(String inventoryId) {
        // Implementation to retrieve and return the inventory by ID
        return mongoQueries.getEntity(inventoryRepository, inventoryId);
    }

    public Inventory updateInventory(String inventoryId, Inventory inventory) {
        // Implementation to update and return the updated inventory
        return mongoQueries.updateEntity(inventoryRepository, inventoryId, inventory);
    }

    public void deleteInventory(String inventoryId) {
        // Implementation to delete the inventory by ID
        mongoQueries.softDeleteById(inventoryRepository, inventoryId, Inventory.class);
    }

    public Page<Inventory> filterByInventory(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        Page<Inventory> page = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                Inventory.class
        );
        return page;
    }
}
