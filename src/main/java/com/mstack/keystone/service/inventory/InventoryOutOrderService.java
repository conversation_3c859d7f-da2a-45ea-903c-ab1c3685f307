package com.mstack.keystone.service.inventory;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.SDOVirtualProductBatchDto;
import com.mstack.keystone.model.enums.BatchType;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.inventory.*;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.repository.InventoryOutOrderRepository;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.inventory.InventoryRepository;
import com.mstack.keystone.repository.inventory.InventoryTransactionRepo;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.service.batchDetails.ProductBatchDetailService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InventoryOutOrderService {
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    InventoryProductService inventoryProductService;
    @Autowired
    InventoryOutOrderRepository inventoryOutOrderRepository;
    @Autowired
    InventoryTransactionRepo inventoryTransactionRepo;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;
    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    OrderBookRepository orderBookRepository;
    @Autowired
    InventoryRepository inventoryRepository;

    @Autowired
    ProductBatchDetailService productBatchDetailService;

    @Autowired
    CustomerOrderService customerOrderService;
//
@Transactional
@SneakyThrows
    public InventoryOutOrder createInventoryOutOrder(InventoryOutOrder inventoryOutOrder) {
    CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(inventoryOutOrder.getCustomerDispatchOrderId());
    if (customerOrder == null) throw new ServiceException("Invalid dispatch order id provied ", 400);

    if (customerOrder.getInventoryId() != null)
        throw new ServiceException("Inventory out order cannot be created for inventory orders ", 400);
    inventoryOutOrder.getProducts().forEach(inventoryOutOrderProduct -> {
        inventoryOutOrderProduct.getProductBatches().forEach(batch -> {
            batch.setProductId(inventoryOutOrderProduct.getProductId());
            batch.setPackagingId(inventoryOutOrderProduct.getPackagingId());
        });
    });
    inventoryOutOrder.setOrderId("IOU" + mongoQueries.getCount(inventoryOutOrderRepository));
    Inventory inventory = mongoQueries.getEntity(inventoryRepository, inventoryOutOrder.getInventoryId());
    if (inventory == null) throw new ServiceException("Invalid inventory id provided ", 400);
    inventoryOutOrder.setInventoryDetail(inventory);
    inventoryOutOrder.setCustomerDispatchOrderNumber(customerOrder.getOrderId());

    // save order
    InventoryOutOrder order = mongoQueries.saveEntity(inventoryOutOrderRepository, inventoryOutOrder);
    // allocate virtual batches for each order product
    order.getProducts().forEach(inventoryOutOrderProduct -> {
        productBatchDetailService.allocateVirutalBatches(order, inventoryOutOrderProduct.getProductBatches(), inventoryOutOrderProduct);
    });
    // update product batch ids in order
    mongoQueries.updateEntity(inventoryOutOrderRepository, order.getId(), order);
//        inventoryProductService.addOrUpdateProductFromOrder(order);
         return order;
    }

    public InventoryOutOrder getInventoryOutOrderById(String inventoryOutOrderId) {
        return mongoQueries.getEntity(inventoryOutOrderRepository, inventoryOutOrderId);
    }

    public InventoryOutOrder updateInventoryOutOrder(String inventoryOutOrderId, InventoryOutOrder inventoryOutOrder) {
        return mongoQueries.updateEntity(inventoryOutOrderRepository, inventoryOutOrderId, inventoryOutOrder);
    }

    public void deleteInventoryOutOrder(String inventoryOutOrderId) throws ServiceException {
        InventoryOutOrder inventoryOutOrder = inventoryOutOrderRepository.findById(inventoryOutOrderId).orElse(null);
        if (inventoryOutOrder == null || inventoryOutOrder.isDeleted())
            throw new ServiceException("Invalind IOU id provided ", 400);
        validateDeleteRequest(inventoryOutOrder);
        unAllocateBatches(inventoryOutOrder);
        mongoQueries.softDeleteById(inventoryOutOrderRepository, inventoryOutOrderId, InventoryOutOrder.class);
    }

    @SneakyThrows
    private void unAllocateBatches(InventoryOutOrder inventoryOutOrder) {
        // traverse through each product
        for (InventoryOutOrderProduct inventoryOutOrderProduct : inventoryOutOrder.getProducts()) {
            // traverse through each batch
            for (SDOVirtualProductBatchDto sdoVirtualProductBatchDto : inventoryOutOrderProduct.getProductBatches()) {
                productBatchDetailService.unAllocateVirtualBatch(sdoVirtualProductBatchDto.getProductBatchId());
            }
        }
    }

    private void validateDeleteRequest(InventoryOutOrder inventoryOutOrder) throws ServiceException {
        for (InventoryOutOrderProduct inventoryOutOrderProduct : inventoryOutOrder.getProducts()) {
            Map<String, List<Object>> filters = Map.ofEntries(
                    Map.entry("type", List.of(BatchType.ALLOCATED.getValue())),
                    Map.entry("productId", List.of(inventoryOutOrderProduct.getProductId())),
                    Map.entry("packagingId", List.of(inventoryOutOrderProduct.getPackagingId())),
                    Map.entry("customerOrderDispatchId", List.of(inventoryOutOrder.getCustomerDispatchOrderId())),
                    Map.entry("deleted", List.of(false))
            );
            // if any allocated batches present then throw error
            List<ProductBatchDetail> productBatchDetails = mongoQueries.findByFields(filters, null, ProductBatchDetail.class);
            if (productBatchDetails != null && !productBatchDetails.isEmpty())
                throw new ServiceException("Customer order is already mapped with inventory order ", 400);
        }

    }

    public List<InventoryOutOrder> getInventoryOutOrderFromOrderbookId(String orderbookId) {
        OrderBook purchaseOrder = mongoQueries.getEntity(orderBookRepository, orderbookId);
        if(purchaseOrder.getInventoryId()!=null){
            return new ArrayList<>();
        }
        String customerId = purchaseOrder.getCustomer().getId();
        String purchaseOrderNumber = purchaseOrder.getPurchaseOrderNumber();
        List<CustomerOrder> customerOrders = customerOrderRepository.findOrdersBycustomerIdAndPoNumber(customerId, purchaseOrderNumber);
        List<String> customerDispatchIds = customerOrders.stream().map(CustomerOrder::getId).distinct().collect(Collectors.toList());
        List<InventoryOutOrder> inventoryOutOrders = inventoryOutOrderRepository.getInventoryOutOrdersByCustomerOrderId(customerDispatchIds);
        HashMap <String,Object>  map=new HashMap<>();
        for (CustomerOrder order:customerOrders) {
            for (OrderedProduct product:order.getProducts()) {
                map.put(product.getProduct().getId(),product.getProduct());
                if(product.getPackaging()==null) continue;
                map.put(product.getPackaging().getId(),product.getPackaging());
            }
        }
        for (InventoryOutOrder outOrder:inventoryOutOrders) {
            for(InventoryOutOrderProduct product:outOrder.getProducts()){
                product.setProduct((Product) map.getOrDefault(product.getProductId(),null));
                product.setPackaging((Packaging) map.getOrDefault(product.getPackagingId(),null));
            }
        }
        return inventoryOutOrders;
    }

    public List<InventoryOutOrder> findByCustomerDispatchId(String orderId) {
        return inventoryOutOrderRepository.getInventoryOutOrdersByCustomerOrderId(List.of(orderId));
    }
}

