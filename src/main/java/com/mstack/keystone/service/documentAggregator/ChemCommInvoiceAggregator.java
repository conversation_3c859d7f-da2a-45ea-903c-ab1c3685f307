package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.config.AssetConfig;
import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import java.util.Objects;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.mstack.keystone.constants.AppConstants.INVOICE_DATE_ERROR;

/* Chemcstack Commercial Invoice Aggregator */
@Component
public class ChemCommInvoiceAggregator implements IDocumentAggregator {
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    OrderBookService orderBookService;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    SupplierOrderService supplierOrderService;
    @Autowired
    AssetConfig assetConfig;

    @Autowired
    DocGenRuleService docGenRuleService;

    @SneakyThrows
    @Override
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        List<TemplatePlaceholder> values = new ArrayList<>();
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
//        OrderBook orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
        List<SupplierOrder> supplierOrders = supplierOrderService.findAllLinkedOrders(customerOrder.getId());
        values.add(new TemplatePlaceholder("DOCUMENT_NAME", "DOCUMENT_NAME", "text", "Chemstack Commercial Invoice"));
        values.add(new TemplatePlaceholder("CHEMSTACK_LOGO", "CHEMSTACK_LOGO", "image", assetConfig.getChemstackLogo()));
        values.add(new TemplatePlaceholder("CHEMSTACK_ADDRESS", "CHEMSTACK_ADDRESS", "text", AddressType.CHEMSTACK_MUMBAI.getAddressValue()));

        values.add(new TemplatePlaceholder("MOBILE_NUMBER", "MOBILE_NUMBER", "text", "+91-9925138848"));
        // TODO VERFIY : ASSUMING DISPTACH ORDER ID AS INVOICE NUMBER
//        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text",generateInvoiceNumber(customerOrder)));
        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text", customerOrder.getChemstackInvoiceNumber()));
//        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", customerOrder.getInvoiceDate()==null?"":commonUtils.formatDate(customerOrder.getInvoiceDate())));
        if (customerOrder.getChemstackInvoiceDate() == null) {
            throw new ServiceException(INVOICE_DATE_ERROR, 400);
        }
        docGenRuleService.validateInvoiceDate(customerOrder.getChemstackInvoiceDate());

        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", commonUtils.formatDate(customerOrder.getChemstackInvoiceDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_DATE", "BUYER_ORDER_DATE", "text", commonUtils.formatDate(customerOrder.getPurchaseOrderDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_NO", "BUYER_ORDER_NO", "text", customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("CONSIGNEE_ADDRESS", "CONSIGNEE_ADDRESS", "text", customerOrder.getConsignee()));
        values.add(new TemplatePlaceholder("NOTIFY_PARTY", "NOTIFY_PARTY", "text", customerOrder.getNotifyParty()));
        values.add(new TemplatePlaceholder("IEC_NO", "IEC_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("PAN_NO", "PAN_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("GSTIN_NO", "GSTIN_NO", "text", "27**********1ZS"));
        values.add(new TemplatePlaceholder("CIN_NO", "CIN_NO", "text", "U24290MH2022FTC385523"));
        values.add(new TemplatePlaceholder("TRADE_CODE", "TRADE_CODE", "text", customerOrder.getTradeAgreementCode()));
        values.add(new TemplatePlaceholder("MSTACK_ADDR", "MSTACK_ADDR", "text", AddressType.MSTACK.getAddressValue()));
        values.add(new TemplatePlaceholder("RECIEPT_CITY", "RECIEPT_CITY", "text", "Mumbai"));
        values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN_OF_GOODS", "COUNTRY_OF_ORIGIN_OF_GOODS", "text", customerOrder.getCountryOfOrigin()));
        values.add(new TemplatePlaceholder("COUNTRY_OF_FINAL_DESTINATION", "COUNTRY_OF_FINAL_DESTINATION", "text", customerOrder.getIncoterms().getCountry()));
        values.add(new TemplatePlaceholder("PLACE_OF_DELIVERY", "PLACE_OF_DELIVERY", "text", customerOrder.getIncoterms().getCountry()));

        values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN", "COUNTRY_OF_ORIGIN", "text", customerOrder.getCountryOfOrigin()));
        values.add(new TemplatePlaceholder("COUNTRY_OF_DESTINATION", "COUNTRY_OF_DESTINATION", "text", customerOrder.getIncoterms().getCountry()));
        values.add(new TemplatePlaceholder("DELIVERY_TERMS", "DELIVERY_TERMS", "text", customerOrder.getIncoterms().getType()));
        values.add(new TemplatePlaceholder("STATE_OF_ORIGIN", "STATE_OF_ORIGIN", "text", "24"));
        values.add(new TemplatePlaceholder("DISTRICT_OF_ORIGIN", "DISTRICT_OF_ORIGIN", "text", "461"));
        values.add(new TemplatePlaceholder("PRECARRIAGE", "PRECARRIAGE", "text", "By road"));
        values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text", customerOrder.getIncoterms().getData().getOrDefault("portOfLoading", "")));
        values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text", customerOrder.getIncoterms().getData().getOrDefault("portOfDischarge", "")));
        values.add(new TemplatePlaceholder("PAYMENT_TERM", "PAYMENT_TERM", "text", customerOrder.getPaymentTermsString()));
        values.add(new TemplatePlaceholder("VESSEL_FLIGHT_NO", "VESSEL_FLIGHT_NO", "text", customerOrder.getVesselNumber()));
        values.add(new TemplatePlaceholder("VOYAGE_NAME", "VOYAGE_NAME", "text", customerOrder.getVoyageName()));
        values.add(new TemplatePlaceholder("TYPE_OF_SHIPMENT", "TYPE_OF_SHIPMENT", "text", customerOrder.getTypeOfShipment()));
        values.add(new TemplatePlaceholder("CONTAINER_NUMBER", "CONTAINER_NUMBER", "text", customerOrder.getMarksAndContainers()));
        values.add(new TemplatePlaceholder("VOYAGE_NO", "VOYAGE_NO", "text", customerOrder.getVoyageName()));
        String shipmentMethod = customerOrder.getIncoterms().getData().getOrDefault("shipmentMethod", "");
        shipmentMethod = !shipmentMethod.isEmpty() ? "By " + shipmentMethod : "";
        values.add(new TemplatePlaceholder("METHOD_OF_DISPATCH", "METHOD_OF_DISPATCH", "text", shipmentMethod));
        values.add(new TemplatePlaceholder("VESSEL_NO", "VESSEL_NO", "text", customerOrder.getVesselNumber()));
        values.add(new TemplatePlaceholder("DEPARTURE_DATE", "DEPARTURE_DATE", "text", commonUtils.formatDate(customerOrder.getShipmentDate())));
        values.add(new TemplatePlaceholder("FINAL_DESTINATION", "FINAL_DESTINATION", "text", customerOrder.getIncoterms().getData().getOrDefault("portOfDischarge", "")));
        IncoType shipmentTerm = customerOrder.getIncoterms().getType();
        String portOfDischarge = customerOrder.getIncoterms().getData().getOrDefault("portOfDischarge", "");
        String portOfLoading = customerOrder.getIncoterms().getData().getOrDefault("portOfLoading", "");

        if (shipmentTerm.equals(IncoType.FOB)) {
            values.add(new TemplatePlaceholder("TERM_OF_SHIPMENT", "TERM_OF_SHIPMENT", "text", shipmentTerm + " - " + portOfLoading));
        } else
            values.add(new TemplatePlaceholder("TERM_OF_SHIPMENT", "TERM_OF_SHIPMENT", "text", (shipmentTerm.equals(IncoType.DDP) ? "CIF" : shipmentTerm) + " - " + portOfDischarge));

        List<HashMap> list=new ArrayList<>();
        for (OrderedProduct orderedProduct:customerOrder.getProducts()) {
            HashMap map=new HashMap<>();
            if (orderedProduct.getProductNameAlias() != null && !orderedProduct.getProductNameAlias().isBlank())
                map.put("DESCRIPTION", orderedProduct.getProductNameAlias());
            else map.put("DESCRIPTION", orderedProduct.getProduct().getTradeName());
            map.put("HS_CODE",orderedProduct.getHsCode());
            map.put("QUANTITY",orderedProduct.getQuantity());
            map.put("PRICE", orderedProduct.getPrice());
            map.put("UNIT", getAbbreviation(orderedProduct.getUom()));
            double amt = orderedProduct.getPrice() * orderedProduct.getQuantity();
            amt = commonUtils.roundOff(amt);
            map.put("AMOUNT", amt);
            list.add(map);
        }
        values.add(new TemplatePlaceholder("TABLE_DATA", "TABLE_DATA", "table",list));
        double totalOrderValue = commonUtils.getTotalOrderValue(customerOrder);
        totalOrderValue = commonUtils.roundOff(totalOrderValue);
        values.add(new TemplatePlaceholder("TOTAL", "TOTAL", "text", totalOrderValue)); // Replace with the actual total
        values.add(new TemplatePlaceholder("TOTAL_IN_WORDS", "TOTAL_IN_WORDS", "text", "USD " + NumberToWordConverter.convertToWord(totalOrderValue)+" only"));
        if(customerOrder.getCustomExchangeRate()==null){
            throw new ServiceException("Exchange rate in dollars not present", 400);
        }
        if (customerOrder.getCustomExchangeRate() <= 0)
            throw new ServiceException("customer order is have custom exchange rate less the equal to zero . Please update ", 400);
        values.add(new TemplatePlaceholder("EXCHANGE_RATE", "EXCHANGE_RATE", "text", customerOrder.getCustomExchangeRate()));
        Double totalInInr = commonUtils.roundOff(totalOrderValue * customerOrder.getCustomExchangeRate());
        values.add(new TemplatePlaceholder("TOTAL_IN_INR", "TOTAL_IN_INR", "text", totalInInr.toString())); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("ARN_NUMBER", "ARN_NUMBER", "text", "ARN." + AppConstants.LUT_NUMBER));
        values.add(new TemplatePlaceholder("TOTAL_BEFORE_GST", "TOTAL_BEFORE_GST", "text", totalInInr.toString())); // Replace with the actual total before GST
        values.add(new TemplatePlaceholder("IGST", "IGST", "text", 0)); // Replace with the actual IGST
        values.add(new TemplatePlaceholder("SIGNATURE_1", "SIGNATURE_1", "image", assetConfig.getMstackStamp()));
        values.add(new TemplatePlaceholder("LUT_NUMBER", "LUT_NUMBER", "text", AppConstants.LUT_NUMBER));
        values.add(new TemplatePlaceholder("LUT_DATE", "LUT_DATE", "text", "28.02.2023"));
        values.add(new TemplatePlaceholder("LUT_VALID_DATE", "LUT_VALID_DATE", "text", "31.03.2024"));
        values.add(new TemplatePlaceholder("ACCOUNT_NAME", "ACCOUNT_NAME", "text", " CHEMSTACK PRIVATE LIMITED"));
        values.add(new TemplatePlaceholder("ACCOUNT_NUMBER", "ACCOUNT_NUMBER", "text", " **********"));
        values.add(new TemplatePlaceholder("BANK_NAME", "BANK_NAME", "text", " KOTAK MAHINDRA BANK LTD"));
        values.add(new TemplatePlaceholder("IFS_CODE", "IFS_CODE", "text", " KKBK0000431"));
        values.add(new TemplatePlaceholder("MICR_CODE", "MICR_CODE", "text", " *********"));
        values.add(new TemplatePlaceholder("BANK_ADDRESS", "BANK_ADDRESS", "text", " Indiranagar, 100 Ft Road, Bangalore, Karnataka - 560 038"));
        values.add(new TemplatePlaceholder("MARINE_COVER_POLICY_NO", "MARINE_COVER_POLICY_NO", "text", "2002/*********/00/000"));
        values.add(new TemplatePlaceholder("INVOICE_DUE_DATE", "INVOICE_DUE_DATE", "text", getDueDate(customerOrder)));
        values.add(new TemplatePlaceholder("DELIVERY_DATE", "DELIVERY_DATE", "text", commonUtils.formatDate(customerOrder.getDeliveryDate())));
        return values;
    }


    private double getNetWeightForProduct(OrderedProduct orderedProduct ){
        double output=0.0;
        if (orderedProduct == null || orderedProduct.getBatchData() == null || orderedProduct.getBatchData().isEmpty())
            return output;
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            int numberOfUnits=Integer.parseInt(map.getOrDefault("number_of_units","0").toString());
            output+=(numberOfUnits*(netWeight));
        }
        return output;
    }

    private String getDueDate(CustomerOrder customerOrder){
        PaymentTerms paymentTerms=customerOrder.getMstackPaymentTerms();
        if (paymentTerms == null) return "";
        String startDate=
            Objects.nonNull(paymentTerms.getPoPaymentTerms()) ? paymentTerms.getPoPaymentTerms() : paymentTerms.getStartDate();
        if(startDate==null){
            return "";
        }
        Date date=null;
        if(startDate.equals("BLDate")){
            date=commonUtils.addDaysToDate(customerOrder.getBlDate(),paymentTerms.getCreditorDays());
        }
        else if(startDate.equals("DeliveryDate")){
            date=commonUtils.addDaysToDate(customerOrder.getDeliveryDate(),paymentTerms.getCreditorDays());
        }
        else{
            date=commonUtils.addDaysToDate(customerOrder.getChemstackInvoiceDate(),paymentTerms.getCreditorDays());
        }
        if(date!=null){
            return commonUtils.formatDate(date);
        }
        return "";
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "lb";
            case GALLON -> "gal";
            case LITRE -> "L";
            case KILOLITRE -> "kL";
            case KILOGRAM -> "kg";
            default -> "";
        };
    }
}
