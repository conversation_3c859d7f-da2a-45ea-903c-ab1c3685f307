package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.CurrencyType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
public class MToChemPOAggregator implements IDocumentAggregator{
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    OrderBookService orderBookService;
    @Autowired
    CommonUtils commonUtils;
    @Override
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        CustomerOrder customerOrder=customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
        OrderBook orderBook=null;
        if(customerOrder.getInventoryId()==null){
             orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
        }
        else{
             orderBook = orderBookService.getByPONumber(customerOrder.getPurchaseOrderNumber());
        }
        List<TemplatePlaceholder> values=new ArrayList<>();
        //create PO Number format
        String poNumber = customerOrder.getPurchaseOrderNumber();
        values.add(new TemplatePlaceholder("PO_NUMBER","PO_NUMBER","text",poNumber));
        values.add(new TemplatePlaceholder("PO_DATE", "PO_DATE", "text", convertDateInFormat(customerOrder.getPurchaseOrderDate(), "dd/MMM/yy")));
        values.add(new TemplatePlaceholder("BUYER_CURRENCY", "BUYER_CURRENCY", "text", orderBook.getBuyerCurrency().getAbbreviation()));
//     TODO    Get chemstack supplier address and check if the  order is for gujarat or mumbai
        values.add(new TemplatePlaceholder("CHEMSTACK_ADDRESS", "CHEMSTACK_ADDRESS", "text", AddressType.CHEMSTACK_MUMBAI.getAddressValue()));
        // Also clear if Supplier order are for different suppliers
        String currencyAbbrevation = orderBook.getBuyerCurrency() != null ? orderBook.getBuyerCurrency().getAbbreviation() : "";
        String currencySymbol = orderBook.getBuyerCurrency() != null ? orderBook.getBuyerCurrency().getSign() : "";
        List<HashMap> products=new ArrayList<>();
        for (OrderedProduct product:customerOrder.getProducts()) {
            HashMap map=new HashMap<>();
            map.put("ITEM",product.getProduct().getTradeName());
            map.put("PACKAGING",product.getQuantityPerUnit() +" "+ getAbbreviation(product.getUom())+"/"+getPacking(product));
            map.put("QUANTITY", product.getQuantity() + " " + getAbbreviation(product.getUom()));
            map.put("UNIT_PRICE",currencySymbol+" "+ product.getPrice() );
            double amt = product.getPrice() * product.getQuantity();
            amt = commonUtils.roundOff(amt);
            map.put("TOTAL_PRICE", currencySymbol + " " + amt);
            products.add(map);
        }
        values.add(new TemplatePlaceholder("PRODUCTS_DATA","PRODUCTS_DATA","list",products));
        double totalOrderValue= commonUtils.getTotalOrderValue(customerOrder);
        totalOrderValue = commonUtils.roundOff(totalOrderValue);
        values.add(new TemplatePlaceholder("TOTAL_PRICE_WORDS","TOTAL_PRICE_WORDS",
                "text","USD "+ NumberToWordConverter.convertToWord(totalOrderValue)+" only."));
        //TODO clarify payment terms
        String paymentTerms="Advance";
        values.add(new TemplatePlaceholder("PAYMENT_TERMS","PAYMENT_TERMS","text",customerOrder.getMstackPaymentTerms().getPoPaymentTerms()));
        values.add(new TemplatePlaceholder("SHIPPMENT_TERMS","SHIPPMENT_TERMS","text",getAddressForCToMPo(customerOrder)));
        values.add(new TemplatePlaceholder("SHIPPMENT_METHOD","SHIPPMENT_METHOD","text",
                customerOrder.getIncoterms().getData().getOrDefault("shipmentMethod","-").equals("-")?"":"By "+customerOrder.getIncoterms().getData().getOrDefault("shipmentMethod","-").toString()));
        values.add(new TemplatePlaceholder("ETD", "ETD", "text", convertDateInFormat(customerOrder.getShipmentDate(), "dd/MMM/yy")));
        values.add(new TemplatePlaceholder("ETA", "ETA", "text", convertDateInFormat(customerOrder.getDeliveryDate(), "dd/MMM/yy")));
        values.add(new TemplatePlaceholder("TOTAL_PRICE","TOTAL_PRICE","text",totalOrderValue));
        return values;
    }


    private String getAddressForCToMPo(CustomerOrder customerOrder){
//        String value="CIF- ";
//        IncoTerms incoTerms=customerOrder.getIncoterms();
//        value+=incoTerms.getData().getOrDefault("portOfDischarge","").toString();
//        value+=" ,";
//        value+= incoTerms.getCountry();
        IncoTerms incoTerms=customerOrder.getIncoterms();
        String deliveryTerms = (incoTerms.getType().equals(IncoType.DDP) ? "CIF" : incoTerms.getType()) + " - " + incoTerms.getData().getOrDefault("portOfDischarge", "");
        if (incoTerms.getType().equals(IncoType.FOB)) {
            deliveryTerms = incoTerms.getType() + " - " + incoTerms.getData().getOrDefault("portOfLoading", "");
        }
        return deliveryTerms;
    }

    private String convertDateInFormat(Date date, String format) {
        if (date == null) return "";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // Format the date using the SimpleDateFormat object
        return sdf.format(date);
    }

    public String getPacking(OrderedProduct product) {
        String type = product.getPackaging().getType();
        return type == null ? "Unit" : StringUtils.capitalize(type.toLowerCase());
    }


    //    public String getPacking(OrderedProduct product){
//        String type=product.getPackaging().getType();
//        type=type.toLowerCase();
//        if(type.contains("drum")){
//            return "Drum";
//        }
//        if(type.contains("bag")){
//            return "Bag";
//        }
//        if(type.contains("iso")){
//            return "ISO Tank";
//        }
//        if(type.contains("can")){
//            return "Drum";
//        }
//        return "Unit";
//    }
    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "lb";
            case GALLON -> "gal";
            case LITRE -> "L";
            case KILOLITRE -> "kL";
            case KILOGRAM -> "kg";
            default -> "";
        };
    }
}
