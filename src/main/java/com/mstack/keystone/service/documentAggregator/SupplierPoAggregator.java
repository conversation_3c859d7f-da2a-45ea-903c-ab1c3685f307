package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.CurrencyType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.model.repository.order.DeliverySchedule;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.model.repository.order.Tax;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.SupplierOrderBookRepo;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Component
public class SupplierPoAggregator implements IDocumentAggregator{
    @Autowired
    SupplierOrderBookRepo supplierOrderBookRepository;
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    CommonUtils commonUtils;

    @Override
    @SneakyThrows
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        String orderBookId=meta.getOrDefault("supplierOrderbookId","").toString();
        if(orderBookId.isEmpty()){
            throw new ServiceException("Supplier orderbook id is null",404);
        }
        List<TemplatePlaceholder> values=new ArrayList<>();
        SupplierOrderBook supplierOrder=mongoQueries.getEntity(supplierOrderBookRepository,orderBookId);
        String poNumber=supplierOrder.getPurchaseOrderNumber();
        String poDate= commonUtils.formatDate(supplierOrder.getPurchaseOrderDate());
        String billingAddress = supplierOrder.getBillingAddress();
        String supplierRefNo = supplierOrder.getSupplierRefNumber();
        String quotationNumber = supplierOrder.getQuotationNumber();
        String paymentTerms = getPaymentTermsString(supplierOrder.getPaymentTerms());
        String modeOfDelivery = supplierOrder.getModeOfDelivery() != null && !supplierOrder.getModeOfDelivery().isEmpty() ? StringUtils.capitalize(supplierOrder.getModeOfDelivery()) : "";
        String deliveryTerms = supplierOrder.getDeliveryTerm();
        String deliveryLocation = supplierOrder.getDeliveryLocation();
        String pickupLocation = supplierOrder.getPickupLocation();
        String supplierAddress = Optional.of(supplierOrder)
                .map((orderBook -> supplierOrder.getSupplier()))
                .map(Supplier::getAddress)
                .map(Address::getAddressAsString)
                .map(address -> supplierOrder.getSupplier().getName() + " , " + address)
                .orElse(null);
        String gstin = Optional.of(supplierOrder)
                .map((orderBook -> supplierOrder.getSupplier()))
                .map(Supplier::getGstin)
                .orElse(null);
        String pan = Optional.of(supplierOrder)
                .map((orderBook -> supplierOrder.getSupplier()))
                .map(Supplier::getPan)
                .orElse(null);

        String additionalConditions = supplierOrder.getAdditionalCondition();
        String termsAndConditions = getTermsAndConditions(supplierOrder);
        String companyAddress = supplierOrder.getBillingAddress();
        String deliveryAddress = supplierOrder.getShippingAddress();
        String factoryAddress = "";
//        String factoryAddress = getFactoryAddress(supplierOrder.getSupplier());//product address supplier
        List<HashMap<String, Object>> productsData = getProductsData(supplierOrder);
        double subTotal = commonUtils.roundOff(getSubTotal(productsData));
        List<HashMap<String, Object>> taxList = getTaxList(supplierOrder.getTaxList(), subTotal);
        double orderValue = commonUtils.roundOff(getOrderValue(taxList, subTotal));
        String totalPriceWords = NumberToWordConverter.convertToWord(orderValue);
        if (supplierOrder.getCurrencyType().equals(CurrencyType.DOLLAR)) {
            totalPriceWords = "USD " + totalPriceWords + " only";
        } else {
            totalPriceWords = totalPriceWords + " rupees only";
        }
        List<HashMap<String, Object>> scheduleData = getScheduleData(supplierOrder.getDeliverySchedule());

        if (supplierOrder.getSupplier() != null && supplierOrder.getSupplier().getAddress() != null && supplierOrder.getSupplier().getAddress().getCountry() != null) {
            values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN", "COUNTRY_OF_ORIGIN", "text", supplierOrder.getSupplier().getAddress().getCountry()));
        }

        if (supplierOrder.getProducts() != null && !supplierOrder.getProducts().isEmpty() && supplierOrder.getProducts().get(0).getIncoterms() != null) {
            IncoTerms incoTerms = supplierOrder.getProducts().get(0).getIncoterms();
            if(incoTerms.getType().equals(IncoType.EXWORKS)){
                factoryAddress = incoTerms.getData().getOrDefault("placeOfPickup", "");
            }
            if (incoTerms.getType().equals(IncoType.CIF) || incoTerms.getType().equals(IncoType.CFR) || incoTerms.getType().equals(IncoType.DDP)) {
                values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text", incoTerms.getData().getOrDefault("portOfLoading", "")));
                values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text", incoTerms.getData().getOrDefault("portOfDischarge", "")));
            }
        }

        values.add(new TemplatePlaceholder("PO_NUMBER","PO_NUMBER","text",poNumber));
        values.add(new TemplatePlaceholder("PO_DATE","PO_DATE","text",poDate));
        values.add(new TemplatePlaceholder("BILLING_ADDRESS", "BILLING_ADDRESS", "text", billingAddress));
        values.add(new TemplatePlaceholder("SUPPLIER_REF_NO", "SUPPLIER_REF_NO", "text", supplierRefNo));
        values.add(new TemplatePlaceholder("CURRENCY_TYPE", "CURRENCY_TYPE", "text", supplierOrder.getCurrencyType().getSign()));
        values.add(new TemplatePlaceholder("QUOTATION_NUMBER", "QUOTATION_NUMBER", "text", quotationNumber));
        values.add(new TemplatePlaceholder("PAYMENT_TERMS", "PAYMENT_TERMS", "text", paymentTerms));
        values.add(new TemplatePlaceholder("MODE_OF_DELIVERY", "MODE_OF_DELIVERY", "text", modeOfDelivery));
        values.add(new TemplatePlaceholder("DELIVERY_TERMS", "DELIVERY_TERMS", "text", deliveryTerms));
        values.add(new TemplatePlaceholder("PICKUP_LOCATION", "PICKUP_LOCATION", "text", pickupLocation));
        values.add(new TemplatePlaceholder("DELIVERY_LOCATION", "DELIVERY_LOCATION", "text", deliveryLocation));
        values.add(new TemplatePlaceholder("SUPPLIER_ADDRESS", "SUPPLIER_ADDRESS", "text", supplierAddress));
        values.add(new TemplatePlaceholder("GSTIN", "GSTIN", "text", gstin));
        values.add(new TemplatePlaceholder("PAN", "PAN", "text", pan));
        values.add(new TemplatePlaceholder("ADDITIONAL_CONDITIONS", "ADDITIONAL_CONDITIONS", "text", additionalConditions));
        values.add(new TemplatePlaceholder("TERMS_AND_CONDITIONS", "TERMS_AND_CONDITIONS", "text", termsAndConditions));
        values.add(new TemplatePlaceholder("COMPANY_ADDRESS", "COMPANY_ADDRESS", "text", companyAddress));
        values.add(new TemplatePlaceholder("DELIVERY_ADDRESS", "DELIVERY_ADDRESS", "text", deliveryAddress));
        values.add(new TemplatePlaceholder("FACTORY_ADDRESS", "FACTORY_ADDRESS", "text", factoryAddress));
        values.add(new TemplatePlaceholder("PRODUCTS_DATA", "PRODUCTS_DATA", "list", productsData));
        values.add(new TemplatePlaceholder("SUB_TOTAL", "SUB_TOTAL", "text", commonUtils.printDouble(subTotal)));
        values.add(new TemplatePlaceholder("TAX_LIST", "TAX_LIST", "list", taxList));
        values.add(new TemplatePlaceholder("ORDER_VALUE", "ORDER_VALUE", "text", commonUtils.printDouble(orderValue)));
        values.add(new TemplatePlaceholder("TOTAL_PRICE_WORDS", "TOTAL_PRICE_WORDS", "text", totalPriceWords));
        values.add(new TemplatePlaceholder("SCHEDULE_DATA", "SCHEDULE_DATA", "list", scheduleData));
        values.add(new TemplatePlaceholder("TRANS_SHIPMENT", "TRANS_SHIPMENT", "text", supplierOrder.isTransShipment()));
        values.add(new TemplatePlaceholder("PARTIAL_SHIPMENT", "PARTIAL_SHIPMENT", "text", supplierOrder.isPartialShipment()));
        values.add(new TemplatePlaceholder("PRODUCT_UOM", "PRODUCT_UOM", "text", commonUtils.getAbbreviation(supplierOrder.getProducts().get(0).getUom())));

        return values;
    }

    private String getTermsAndConditions(SupplierOrderBook supplierOrder) {
        String tc = "";
        if (!supplierOrder.isExcludeDefaultTc()) {
            if (supplierOrder.getBillingAddress() != null && supplierOrder.getBillingAddress().equals(AppConstants.MSTACK_BILLING_ADDRESS))
                tc += AppConstants.SUPPLIER_PO_M_T_C;
            else tc += AppConstants.SUPPLIER_PO_C_T_C;
        }

        tc += " \n " + ((supplierOrder.getTermAndCondition() != null && !supplierOrder.getTermAndCondition().isEmpty()) ? supplierOrder.getTermAndCondition() : "");
        System.out.println("TC value " + tc);
        return tc;
    }

    public String getPaymentTermsString(PaymentTerms paymentTerms) {
        if (paymentTerms == null) return null;
//        if (paymentTerms.getCreditorDays() == 0) return " Advance ";
        String output = "";
        if ((100 - paymentTerms.getCreditAmount()) > 0)
            output += "advance amt : " + commonUtils.roundOff(100 - paymentTerms.getCreditAmount()) + " % ,";
        output = "credit amt : " + commonUtils.roundOff(paymentTerms.getCreditAmount()) + " % ,";
        output += "T/T ";
        output += (paymentTerms.getCreditorDays() + " days at ");
        output += (paymentTerms.getPoPaymentTerms() != null ? paymentTerms.getPoPaymentTerms() : paymentTerms.getStartDate());
        return output;
    }

    private List<HashMap<String, Object>> getScheduleData(List<DeliverySchedule> deliverySchedule) {
        List<HashMap<String, Object>> scheduleData = new ArrayList<>();
        if(deliverySchedule==null){
            return  null;
        }
        for (DeliverySchedule schedule : deliverySchedule) {
            HashMap<String, Object> data = new HashMap<>();
            data.put("ITEM", schedule.getProductName());
            data.put("QUANTITY", schedule.getQuantity());
            if (schedule.getDeliveryDate() != null) {
                data.put("DELIVERY_DATE", commonUtils.formatDate(schedule.getDeliveryDate()));
            } else data.put("DELIVERY_DATE", "");
            data.put("REMARK", schedule.getRemarks());
            scheduleData.add(data);
        }
        return scheduleData;
    }

    private double getOrderValue(List<HashMap<String, Object>> taxList, double subTotal) {
        double orderValue = subTotal;
        if(taxList!=null){
            for (HashMap<String, Object> tax : taxList)
                orderValue += (double) tax.getOrDefault("TAX_VALUE", 0.00);
        }
        return orderValue;
    }

    private List<HashMap<String, Object>> getTaxList(List<Tax> taxList, double subTotal) {
        List<HashMap<String, Object>> taxDatas = new ArrayList<>();
        if(taxList==null){
            return null;
        }
        for (Tax tax : taxList) {
            HashMap<String, Object> taxData = new HashMap<>();
            taxData.put("TAX_TYPE", tax.getTaxType());
            taxData.put("TAX_PERCENT", tax.getTaxPercent());
            double taxValue = ((tax.getTaxPercent() * subTotal) / 100);
            taxData.put("TAX_VALUE", taxValue);
            taxData.put("TAX_VALUE_AS_STR", commonUtils.printDouble(taxValue));
            taxDatas.add(taxData);
        }
        return taxDatas;
    }

    private double getSubTotal(List<HashMap<String, Object>> productsData) {
        double subTotal = 0;
        if(productsData==null){
            return subTotal;
        }
        for (HashMap<String, Object> product : productsData) {
            subTotal += (Double) product.getOrDefault("TOTAL_PRICE", 0.00);
        }
        ;
        return subTotal;
    }

    private List<HashMap<String, Object>> getProductsData(SupplierOrderBook supplierOrder) {
        List<HashMap<String, Object>> productDataList = new ArrayList<>();
        supplierOrder.getProducts().forEach(orderBookEntry -> {
            HashMap<String, Object> productData = new HashMap<>();
            productData.put("ITEM", orderBookEntry.getProduct().getTradeName());
            boolean isOthers = orderBookEntry.getPackaging() != null &&
                "Others".equalsIgnoreCase(orderBookEntry.getPackaging().getType()) &&
                orderBookEntry.getPackaging().getOtherPackagingDetails() != null &&
                !orderBookEntry.getPackaging().getOtherPackagingDetails().isEmpty();
            String packagingValue;
            if (isOthers) {
                packagingValue = orderBookEntry.getPackaging().getOtherPackagingDetails();
            } else {
                packagingValue = orderBookEntry.getUnits() + " x " + orderBookEntry.getPackaging().getType();
                productData.put("PACK_SIZE", orderBookEntry.getPackaging().getPackSize());
                productData.put("TARE_WEIGHT", orderBookEntry.getPackaging().getTareWeight());
            }
            productData.put("PACKAGING", packagingValue);
            productData.put("HS_CODE", orderBookEntry.getHsCode());
            productData.put("QUANTITY", orderBookEntry.getQuantity());
            productData.put("UNIT_PRICE", orderBookEntry.getPrice());
            double totalPrice = commonUtils.roundOff(orderBookEntry.getQuantity() * orderBookEntry.getPrice());
            productData.put("TOTAL_PRICE", totalPrice);
            productData.put("TOTAL_PRICE_AS_STR", commonUtils.printDouble(totalPrice));
            productDataList.add(productData);
        });
        return productDataList;
    }

    private String getFactoryAddress(Supplier supplier) {
        return Optional.ofNullable(supplier)
                .map(Supplier::getAddress)
                .map(Address::getAddressAsString)
                .map(s -> supplier.getName() + " , " + s)
                .orElse(null);
    }
}
