package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.config.AssetConfig;
import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.docEngine.DocMetaRepository;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.SupplierOrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.mstack.keystone.constants.AppConstants.INVOICE_DATE_ERROR;

@Component
public class ChemTaxInvoiceAggregator implements IDocumentAggregator{
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    OrderBookService orderBookService;
    @Autowired
    SupplierOrderService supplierOrderService;
    @Autowired
    DocMetaRepository docMetaRepository;
    @Autowired
    SupplierOrderBookService supplierOrderBookService;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AssetConfig assetConfig;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;

    @Autowired
    PackagingRepository packagingRepository;

    @Autowired
    DocGenRuleService docGenRuleService;
    @Override
    @SneakyThrows
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        CustomerOrder customerOrder=customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
//        Optional<SupplierOrder> optionalSupplierOrdersupplierOrder = supplierOrderService.getSupplierOrderById(meta.get("supplierOrderId").toString());
        SupplierOrder supplierOrder = null;
        SupplierOrderBook supplierOrderBook = null;
//        if (optionalSupplierOrdersupplierOrder.isEmpty())
//            throw new ServiceException("given supplierOrderId not present ", 404);
//        else {
//            supplierOrder = optionalSupplierOrdersupplierOrder.get();
//            supplierOrderBook = supplierOrderBookService.getByPONumber(supplierOrder.getPurchaseOrderNumber());
//        }

        List<TemplatePlaceholder> values = new ArrayList<>();
        values.add(new TemplatePlaceholder("DOCUMENT_NAME", "DOCUMENT_NAME", "text", "Chemstack Tax Invoice"));
        values.add(new TemplatePlaceholder("CHEMSTACK_LOGO", "CHEMSTACK_LOGO", "image", assetConfig.getChemstackLogo()));
//        if (supplierOrderBook.getBillingAddress() == null)
//            throw new ServiceException("Billing address not present for given order ", 400);
//        AddressType billingAddress = getAddressType(supplierOrderBook.getBillingAddress());
        values.add(new TemplatePlaceholder("CHEMSTACK_ADDRESS", "CHEMSTACK_ADDRESS", "text", AddressType.CHEMSTACK_MUMBAI.getAddressValue()));
        values.add(new TemplatePlaceholder("MOBILE_NUMBER", "MOBILE_NUMBER", "text", "+91-9925138848"));
        // TODO GB GET INVOICE LOGIC
//        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text",generateInvoiceNumber(customerOrder)));
        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text", customerOrder.getChemstackInvoiceNumber()));
        if (customerOrder.getChemstackInvoiceDate() == null) {
            throw new ServiceException(INVOICE_DATE_ERROR, 400);
        }
        docGenRuleService.validateInvoiceDate(customerOrder.getChemstackInvoiceDate());

        // TODO GB CHECK INVOICE DATE SHOULD GENERATION TIME DATE
        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", commonUtils.formatDate(customerOrder.getChemstackInvoiceDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_DATE", "BUYER_ORDER_DATE", "text", commonUtils.formatDate(customerOrder.getPurchaseOrderDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_NO", "BUYER_ORDER_NO", "text", customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("CONSIGNEE_ADDRESS", "CONSIGNEE_ADDRESS", "text", customerOrder.getConsignee()));
        values.add(new TemplatePlaceholder("NOTIFY_PARTY", "NOTIFY_PARTY", "text", customerOrder.getNotifyParty()));
        values.add(new TemplatePlaceholder("IEC_NO", "IEC_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("PAN_NO", "PAN_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("GSTIN_NO", "GSTIN_NO", "text", "27**********1ZS"));
        values.add(new TemplatePlaceholder("CIN_NO", "CIN_NO", "text", "U24290MH2022FTC385523"));
        //TODO - GB
        values.add(new TemplatePlaceholder("TRADE_CODE", "TRADE_CODE", "text", customerOrder.getTradeAgreementCode()));
        values.add(new TemplatePlaceholder("MSTACK_ADDR", "MSTACK_ADDR", "text", AddressType.MSTACK.getAddressValue()));
        values.add(new TemplatePlaceholder("RECIEPT_CITY", "RECIEPT_CITY", "text", "Mumbai"));
        values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN_OF_GOODS", "COUNTRY_OF_ORIGIN_OF_GOODS", "text", customerOrder.getCountryOfOrigin()));
        values.add(new TemplatePlaceholder("COUNTRY_OF_FINAL_DESTINATION", "COUNTRY_OF_FINAL_DESTINATION", "text", customerOrder.getIncoterms().getCountry()));
        values.add(new TemplatePlaceholder("PLACE_OF_DELIVERY", "PLACE_OF_DELIVERY", "text", getPLaceOfDelivery(customerOrder)));

//        values.add(new TemplatePlaceholder("PLACE_OF_RECIEPT", "PLACE_OF_RECIEPT", "object", placeOfReciept));
        values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN", "COUNTRY_OF_ORIGIN", "text", customerOrder.getCountryOfOrigin()));
        values.add(new TemplatePlaceholder("COUNTRY_OF_DESTINATION", "COUNTRY_OF_DESTINATION", "text", customerOrder.getIncoterms().getCountry()));
        values.add(new TemplatePlaceholder("STATE_OF_ORIGIN", "STATE_OF_ORIGIN", "text", "24"));
        values.add(new TemplatePlaceholder("DISTRICT_OF_ORIGIN", "DISTRICT_OF_ORIGIN", "text", "461"));
        values.add(new TemplatePlaceholder("PRECARRIAGE", "PRECARRIAGE", "text", "By road"));
        values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text", customerOrder.getIncoterms().getData().get("portOfLoading")));
        values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text", customerOrder.getIncoterms().getData().get("portOfDischarge")));
        values.add(new TemplatePlaceholder("PAYMENT_TERM", "PAYMENT_TERM", "object", customerOrder.getPaymentTermsString()));
//        values.add(new TemplatePlaceholder("VESSEL_FLIGHT_NO", "VESSEL_FLIGHT_NO", "text", customerOrder.getIncoterms().getData().get("shipmentMethod")));
        // TODO GB COMPLETE THIS
        values.add(new TemplatePlaceholder("VESSEL_FLIGHT_NO", "VESSEL_FLIGHT_NO", "text", customerOrder.getVesselNumber()));
        values.add(new TemplatePlaceholder("CONTAINER_NUMBER", "CONTAINER_NUMBER", "text", customerOrder.getMarksAndContainers()));
        // TODO GB COMPLETE THIS
        List<HashMap> list=new ArrayList<>();
        double totalOrderValue = 0;

        for (OrderedProduct orderedProduct:customerOrder.getProducts()) {
            if (orderedProduct.getBatchData() == null) {
                List<ProductBatchDetail> productBatchDetails = productBatchDetailRepository.findAllocatedBatches(customerOrder.getId());
                List<ProductBatchDetail> orderedProductBatchdetails = productBatchDetails.stream().filter(productBatchDetail -> productBatchDetail.getProductId().equals(orderedProduct.getProduct().getId())
                        && productBatchDetail.getPackagingId().equals(orderedProduct.getPackaging().getId())
                        && productBatchDetail.getType().equals("ALLOCATED")).collect(Collectors.toList());
                List<HashMap<String, Object>> batchList = new ArrayList<>();
                Packaging packaging = packagingRepository.findById(orderedProduct.getPackaging().getId()).get();
                for (ProductBatchDetail batch : orderedProductBatchdetails) {
                    HashMap batchMap = new HashMap();
                    batchMap.put("tare_weight", packaging.getTWeight());
                    batchMap.put("manufacturing_date", batch.getMfgDate());
                    batchMap.put("batch_number", batch.getBatchNumber());
                    batchMap.put("net_weight", orderedProduct.getQuantityPerUnit());
                    batchMap.put("number_of_units", batch.getUnits());
                    batchMap.put("expiry_date", batch.getExpDate());
                    batchList.add(batchMap);
                }
                orderedProduct.setBatchData(batchList);
            }
            HashMap map=new HashMap<>();
            map.put("PACKAGES",getPackages(orderedProduct));
            if (orderedProduct.getProductNameAlias() != null && !orderedProduct.getProductNameAlias().isBlank())
                map.put("DESCRIPTION", orderedProduct.getProductNameAlias());
            else map.put("DESCRIPTION", orderedProduct.getProduct().getTradeName());
            map.put("HS_CODE",orderedProduct.getHsCode());
            double quantity = getNetWeightForProduct(orderedProduct) * orderedProduct.getPerUnitKgValue();
            quantity = commonUtils.roundOff(quantity);
            map.put("QUANTITY", quantity);
            map.put("RATE", orderedProduct.getChemstackPrice());
            double productTotal = commonUtils.roundOff(orderedProduct.getChemstackPrice() * quantity);
            map.put("TOTAL", productTotal);
            totalOrderValue += productTotal;
            list.add(map);
        }
        totalOrderValue = commonUtils.roundOff(totalOrderValue);
        values.add(new TemplatePlaceholder("TABLE_DATA", "TABLE_DATA", "table",list));
        values.add(new TemplatePlaceholder("TOTAL", "TOTAL", "text", totalOrderValue)); // Replace with the actual total
        values.add(new TemplatePlaceholder("TOTAL_IN_WORDS", "TOTAL_IN_WORDS", "text", "USD " + NumberToWordConverter.convertToWord(totalOrderValue)+" only"));
        // TODO GB CHANGE THIS IF YOU WANT TO GET EXCH RATE FROM CONFIG
        if(customerOrder.getCustomExchangeRate()==null){
            throw new ServiceException("Exchange rate in dollars not present",500);
        }
        if (customerOrder.getCustomExchangeRate() <= 0)
            throw new ServiceException("customer order is have custom exchange rate less the equal to zero . Please update ", 400);
        values.add(new TemplatePlaceholder("EXCHANGE_RATE", "EXCHANGE_RATE", "text", customerOrder.getCustomExchangeRate()));
        double totalInInr = totalOrderValue * customerOrder.getCustomExchangeRate();
        totalInInr = commonUtils.roundOff(totalInInr);
        values.add(new TemplatePlaceholder("TOTAL_IN_INR", "TOTAL_IN_INR", "text", commonUtils.printDouble(totalInInr))); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("ARN_NUMBER", "ARN_NUMBER", "text", "ARN." + AppConstants.LUT_NUMBER));
        values.add(new TemplatePlaceholder("TOTAL_BEFORE_GST", "TOTAL_BEFORE_GST", "text", commonUtils.printDouble(totalInInr))); // Replace with the actual total before GST
//        if (supplierOrderBook.getGstPercent() == 0) {
//            throw new ServiceException("supplier order book gst percent is zero . Please update ", 400);
//        }
//        if (supplierOrderBook.getGstPercent() == 18) {
//            // if it is 18% gst
        values.add(new TemplatePlaceholder("IGST", "IGST", "text", customerOrder.getIgstAmt() == null ? 0 : customerOrder.getIgstAmt())); // Replace with the actual IGST
        double amtAfterGst = (customerOrder.getIgstAmt() == null) ? commonUtils.roundOff(totalInInr) : commonUtils.roundOff(customerOrder.getIgstAmt() + totalInInr);

//            values.add(new TemplatePlaceholder("TOTAL_AFTER_GST", "TOTAL_AFTER_GST", "text", totalInInr + (totalInInr * 0.18))); // Replace with the actual total after GST
//        } else if (supplierOrderBook.getGstPercent() == 1) {
//            values.add(new TemplatePlaceholder("IGST", "IGST", "text", "0")); // Replace with the actual IGST
        values.add(new TemplatePlaceholder("TOTAL_AFTER_GST", "TOTAL_AFTER_GST", "text", commonUtils.printDouble(amtAfterGst))); // Replace with the actual total after GST
//        }
        values.add(new TemplatePlaceholder("SIGNATURE_1", "SIGNATURE_1", "image", assetConfig.getMstackStamp()));
        IncoTerms incoTerms=customerOrder.getIncoterms();
        String deliveryTerms = (incoTerms.getType().equals(IncoType.DDP) ? "CIF" : incoTerms.getType()) + " - " + incoTerms.getData().getOrDefault("portOfDischarge", "");
        if (incoTerms.getType().equals(IncoType.FOB)) {
            deliveryTerms = incoTerms.getType() + " - " + incoTerms.getData().getOrDefault("portOfLoading", "");
        }
        values.add(new TemplatePlaceholder("DELIVERY_TERMS", "DELIVERY_TERMS", "text", deliveryTerms));
        List<HashMap> codeList=new ArrayList<>();
        for (OrderedProduct orderedProduct:customerOrder.getProducts()) {
            HashMap map=new HashMap<>();
            map.put("PRODUCT_NAME_ALIAS", orderedProduct.getProductNameAlias());
            map.put("PRODUCT",orderedProduct.getProduct().getTradeName());
            map.put("DISTRICT_OF_ORIGIN",orderedProduct.getDistrictOfOrigin());
            map.put("STATE_OF_ORIGIN",orderedProduct.getStateOfOrigin());
            codeList.add(map);
        }
        values.add(new TemplatePlaceholder("PRODUCT_ORIGIN_DATA", "PRODUCT_ORIGIN_DATA", "list", codeList));
        values.add(new TemplatePlaceholder("DELIVERY_DATE", "DELIVERY_DATE", "text", commonUtils.formatDate(customerOrder.getDeliveryDate())));
        return values;
    }

    private String getPLaceOfDelivery(CustomerOrder customerOrder){
        String output="";
        IncoTerms incoTerms=customerOrder.getIncoterms();
        if(incoTerms.getType().equals(IncoType.CIF)){
            return incoTerms.getData().getOrDefault("portOfDischarge","")+", "+incoTerms.getCountry();
        }
        if(incoTerms.getType().equals(IncoType.DDP)){
            return incoTerms.getData().getOrDefault("portOfDischarge","")+", "+incoTerms.getCountry();
        }
        output+=customerOrder.getIncoterms().getCountry();
        return output;
    }

    private AddressType getAddressType(String billingAddress) {
        if (AddressType.MSTACK.getAddressValue().equals(billingAddress))
            return AddressType.MSTACK;
        else if (AddressType.CHEMSTACK_MUMBAI.getAddressValue().equals(billingAddress))
            return AddressType.CHEMSTACK_MUMBAI;
        else if (AddressType.CHEMSTACK_GUJARAT.getAddressValue().equals(billingAddress))
            return AddressType.CHEMSTACK_GUJARAT;
        else return null;
    }


    private String generateInvoiceNumber(CustomerOrder customerOrder){
//        TODO GB
//        long count= docMetaRepository.getCountOFDocType("CHEMSTACK_TAX_INVOICE").size();
//        return "INV-"+commonUtils.getCurrentFinancialYear()+"-"+count+1;
        return customerOrder.getOrderId();
    }

    private List<List<String>> getPackages(OrderedProduct product) throws ParseException {
        List<List<String>> list=new ArrayList<>();
        if(product.getBatchData()==null){
            return new ArrayList<>();
        }
        for (HashMap  map:product.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","").toString());
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","").toString());
            double numberOfUnits = Double.parseDouble(map.getOrDefault("number_of_units", "").toString());
            productList.add("Net wt. " + numberOfUnits + " x " + commonUtils.roundOff(netWeight * product.getPerUnitKgValue()) + "kg");
            productList.add("Gross Wt. " + numberOfUnits + " x " + commonUtils.roundOff(tareWeight + ((netWeight) * product.getPerUnitKgValue())) + "kg");
            productList.add("Batch no. "+map.getOrDefault("batch_number","").toString());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            SimpleDateFormat monthAndYear = new SimpleDateFormat("MMM yyyy");
            if (map.getOrDefault("manufacturing_date", null) != null) {
                if (map.get("manufacturing_date") instanceof Date) {
                    if (!product.isBatchDateVisible()) {
//                        System.out.println("formatting date");
                        productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null), monthAndYear));
                    } else
                        productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null)));
                } else {
                    Date mfgDate = sdf.parse((String) map.get("manufacturing_date"));
                    if (!product.isBatchDateVisible())
                        productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate, monthAndYear));
                    else productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate));
                }
            } else productList.add("Manufacturing Date. ");
            if (map.getOrDefault("expiry_date", null) != null) {
                if (map.get("expiry_date") instanceof Date) {
                    if (!product.isBatchDateVisible())
                        productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", ""), monthAndYear));
                    else
                        productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", "")));
                } else {
                    Date expDate = sdf.parse((String) map.get("expiry_date"));
                    if (!product.isBatchDateVisible())
                        productList.add("Expiry Date. " + commonUtils.formatDate(expDate, monthAndYear));
                    else productList.add("Expiry Date. " + commonUtils.formatDate(expDate));
                }
            } else productList.add("Expiry Date. ");
            list.add(productList);
        }
        return list;
    }

    private double getNetWeightForProduct(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            double numberOfUnits = Double.parseDouble(map.getOrDefault("number_of_units", "0").toString());
            output+=(numberOfUnits*(netWeight));
        }
        return output;
    }

    private List<Packaging> getPackageList(List<OrderedProduct> products) {
        List<Packaging> packageList = new ArrayList<>();
        products.forEach(orderedProduct -> {
            packageList.add(orderedProduct.getPackaging());
        });
        return packageList;
    }

}
