package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.config.AssetConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import java.util.Objects;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.mstack.keystone.constants.AppConstants.INVOICE_DATE_ERROR;

@Component
public class MstackInvoiceAggregator implements IDocumentAggregator{
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    OrderBookService orderBookService;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    SupplierOrderService supplierOrderService;

    @Autowired
    DocGenRuleService docGenRuleService;
    @Autowired
    AssetConfig assetConfig;
    @Override
    @SneakyThrows
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        decimalFormat.setRoundingMode(RoundingMode.HALF_EVEN);
        List<TemplatePlaceholder> values = new ArrayList<>();
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
        PaymentTerms paymentTerms=customerOrder.getPaymentTerms();
        String poPaymentTerms =Objects.nonNull(paymentTerms.getPoPaymentTerms()) ? paymentTerms.getPoPaymentTerms() : paymentTerms.getStartDate();
        if(customerOrder.getInventoryId()!=null){
            throw new ServiceException("Invalid document for an inventory order",500);
        }

        OrderBook orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
        List<SupplierOrder> supplierOrders = supplierOrderService.findAllLinkedOrders(customerOrder.getId());
        values.add(new TemplatePlaceholder("MSTACK_ADDRESS", "MSTACK_ADDRESS", "text", AddressType.MSTACK.getAddressValue()));
        // TODO GB RECHECK BILLING ADDRESS
        values.add(new TemplatePlaceholder("BILLING_ADDRESS", "BILLING_ADDRESS", "text", orderBook.getBillTo()));
//        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text",generateInvoiceNumber(customerOrder)));
        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text", customerOrder.getMstackInvoiceNumber()));
        if (customerOrder.getInvoiceDate() == null) {
            throw new ServiceException(INVOICE_DATE_ERROR, 400);
        }
        if (!(meta.containsKey("requestedBy") && meta.get("requestedBy").equals("system"))) {
            if (!customerOrder.isInvoiceDateValidationDisabled())
                docGenRuleService.validateInvoiceDate(customerOrder.getInvoiceDate());
        }
        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", commonUtils.formatDate(customerOrder.getInvoiceDate())));
        values.add(new TemplatePlaceholder("PO_NUMBER", "PO_NUMBER", "text", customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("CURRENCY", "CURRENCY", "text", orderBook.getBuyerCurrency().getAbbreviation()));
        // TODO GB
        values.add(new TemplatePlaceholder("CREDITOR_DAYS", "CREDITOR_DAYS", "text", customerOrder.getPaymentTerms().getCreditorDays()));
        values.add(new TemplatePlaceholder("CREDITOR_AMOUNT", "CREDITOR_AMOUNT", "text", customerOrder.getPaymentTerms().getCreditAmount()));
        values.add(new TemplatePlaceholder("PAYMENT_TERMS_BASED_ON", "PAYMENT_TERMS_BASED_ON", "text", poPaymentTerms));

        values.add(new TemplatePlaceholder("TERMS", "TERMS", "text", customerOrder.getPaymentTerms().getPoPaymentTerms()));
        values.add(new TemplatePlaceholder("DUE_DATE", "DUE_DATE", "text", getDueDate(customerOrder)));
        double totalOrderValue = 0.00;
        List<HashMap> products = new ArrayList<>();
        for (OrderedProduct product : customerOrder.getProducts()) {
            HashMap map = new HashMap<>();
            map.put("ITEM_NAME_ALIAS", product.getProductNameAlias());
            map.put("ITEM", product.getProduct().getTradeName());
            map.put("PACKAGING", product.getQuantityPerUnit() + " " + getAbbreviation(product.getUom()) + "/" + getPacking(product));
            map.put("QUANTITY", product.getQuantity() + " " + getAbbreviation(product.getUom()));
            map.put("QUANTITY_VALUE", product.getQuantity());
            map.put("QUANTITY_UOM", product.getUom());
            map.put("QUANTITY_UOM_ABB", getAbbreviation(product.getUom()));
            map.put("UNIT_PRICE",   product.getPrice());
            String currVal = decimalFormat.format(product.getPrice() * product.getQuantity());
            map.put("TOTAL_PRICE", currVal);
            totalOrderValue += Double.parseDouble(currVal);
            products.add(map);
        }
        values.add(new TemplatePlaceholder("PRODUCTS_DATA", "PRODUCTS_DATA", "list", products));
        totalOrderValue = Double.parseDouble(decimalFormat.format(totalOrderValue));
        values.add(new TemplatePlaceholder("INCOTERM", "INCOTERM", "text", getPLaceOfDelivery(customerOrder)));
        values.add(new TemplatePlaceholder("INCOTERM_TYPE", "INCOTERM_TYPE", "text", customerOrder.getIncoterms().getType()));
        // INCLUDE COUNTRY OF ORIGIN only if incoterm is not ddp
        if (!customerOrder.getIncoterms().getType().equals(IncoType.DDP)) {
            values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN", "COUNTRY_OF_ORIGIN", "text", customerOrder.getCountryOfOrigin()));
        }
        if (customerOrder.getIncoterms().getType().equals(IncoType.CIF)) {
            values.add(new TemplatePlaceholder("FREIGHT_TERM", "FREIGHT_TERM", "image", "Freight terms: Prepaid"));
        }
        if (customerOrder.getIncoterms().getType().equals(IncoType.FOB)) {
            values.add(new TemplatePlaceholder("FREIGHT_TERM", "FREIGHT_TERM", "image", "Freight terms: Collect"));
        }
        IncoTerms incoTerms=customerOrder.getIncoterms();
        values.add(new TemplatePlaceholder("SHIPING_ADDRESS", "SHIPING_ADDRESS", "text", incoTerms.getData().getOrDefault("placeOfDelivery", "")));
        if(!incoTerms.getType().equals(IncoType.DDP)){
            values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text", customerOrder.getIncoterms().getData().get("portOfLoading")));
            values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text", customerOrder.getIncoterms().getData().get("portOfDischarge")));
        }

//        values.add(new TemplatePlaceholder("FREIGHT_COST", "FREIGHT_COST", "text", customerOrder.getFreightCost()!=null?customerOrder.getFreightCost().toString():null));
//        values.add(new TemplatePlaceholder("INSURANCE_COST", "INSURANCE_COST", "text", customerOrder.getInsuranceCost()!=null?customerOrder.getInsuranceCost().toString():null));
        if (customerOrder.getFreightCost() != null && customerOrder.getFreightCost() > 0) {
            values.add(new TemplatePlaceholder("FREIGHT_COST", "FREIGHT_COST", "text", "$" + customerOrder.getFreightCost()));
        }
        if (customerOrder.getInsuranceCost() != null && customerOrder.getInsuranceCost() > 0) {
            values.add(new TemplatePlaceholder("INSURANCE_COST", "INSURANCE_COST", "text", "$" + customerOrder.getInsuranceCost()));
        }
        if(customerOrder.getFreightCost()!=null&&customerOrder.getInsuranceCost()!=null){
            double fobCost = (totalOrderValue - customerOrder.getFreightCost() - customerOrder.getInsuranceCost());
            values.add(new TemplatePlaceholder("FOB_COST", "FOB_COST", "text", decimalFormat.format(fobCost)));
        }
        values.add(new TemplatePlaceholder("TOTAL_IN_WORDS", "TOTAL_IN_WORDS",
                "text", "USD " + NumberToWordConverter.convertToWord(totalOrderValue)+" only"));
        values.add(new TemplatePlaceholder("TOTAL", "TOTAL", "text", totalOrderValue));
        // FOR INVOICE SCRIPT PURPOSE
        values.add(new TemplatePlaceholder("TOTAL_INVOICE_AMOUNT", "TOTAL_INVOICE_AMOUNT", "text", totalOrderValue));
        values.add(new TemplatePlaceholder("ACCOUNT_NAME", "ACCOUNT_NAME", "text", " Mstack Inc."));
        values.add(new TemplatePlaceholder("ACCOUNT_NUMBER", "ACCOUNT_NUMBER", "text", " *********"));
        values.add(new TemplatePlaceholder("BANK", "BANK", "text", " HSBC bank"));
        values.add(new TemplatePlaceholder("BANK_ADDRESS", "BANK_ADDRESS", "text", " 2911 Walden Ave, Depew, NY 14043, USA"));
        values.add(new TemplatePlaceholder("SWIFT_CODE", "SWIFT_CODE", "text", "MRMDUS33"));
        values.add(new TemplatePlaceholder("ACH_ROUTING_NUMBER", "ACH_ROUTING_NUMBER", "text", " *********"));
        values.add(new TemplatePlaceholder("BILL_OF_LADING_DATE", "BILL_OF_LADING_DATE", "text", commonUtils.formatDate(customerOrder.getBlDate())));
        values.add(new TemplatePlaceholder("BILL_OF_LADING_NUMBER", "BILL_OF_LADING_NUMBER", "text", customerOrder.getBLNumber()));
        values.add(new TemplatePlaceholder("DELIVERY_DATE", "DELIVERY_DATE", "text", commonUtils.formatDate(customerOrder.getDeliveryDate())));
        return values;
    }

    private String getDueDate(CustomerOrder customerOrder){
        PaymentTerms paymentTerms=customerOrder.getPaymentTerms();
        String startDate= Objects.nonNull(paymentTerms.getPoPaymentTerms()) ? paymentTerms.getPoPaymentTerms() : paymentTerms.getStartDate();
        Date date=null;
        if(startDate.equals("BLDate")){
            date=commonUtils.addDaysToDate(customerOrder.getBlDate(),paymentTerms.getCreditorDays());
        }
        else if(startDate.equals("DeliveryDate")){
            date=commonUtils.addDaysToDate(customerOrder.getDeliveryDate(),paymentTerms.getCreditorDays());
        }
        else{
            date=commonUtils.addDaysToDate(customerOrder.getInvoiceDate(),paymentTerms.getCreditorDays());
        }
        if(date!=null){
            return commonUtils.formatDate(date);
        }
        return "";
    }


    private String generateInvoiceNumber(CustomerOrder customerOrder) {
//        TODO GB
//        long count= docMetaRepository.getCountOFDocType("CHEMSTACK_TAX_INVOICE").size();
//        return "INV-"+commonUtils.getCurrentFinancialYear()+"-"+count+1;
        return customerOrder.getOrderId();
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "lb";
            case GALLON -> "gal";
            case LITRE -> "L";
            case KILOLITRE -> "kL";
            case KILOGRAM -> "kg";
            default -> "";
        };
    }

    private String getPLaceOfDelivery(CustomerOrder customerOrder){
        String output="";
        IncoTerms incoTerms=customerOrder.getIncoterms();
        output+=incoTerms.getType()+" - ";
        if(incoTerms.getType().equals(IncoType.CIF)){
            output+= incoTerms.getData().getOrDefault("portOfDischarge","")+", "+incoTerms.getCountry();
        }
        else if(incoTerms.getType().equals(IncoType.DDP)){
            output += incoTerms.getData().getOrDefault("city", "") + ", " + incoTerms.getCountry();
        }
        else if(incoTerms.getType().equals(IncoType.FOB)){
            output += incoTerms.getData().getOrDefault("portOfLoading", "");
        }
        else{
            output+=customerOrder.getIncoterms().getCountry();
        }
        return output;
    }


    //    public String getPacking(OrderedProduct product){
//        String type=product.getPackaging().getType();
//        type=type.toLowerCase();
//        if(type.contains("drum")){
//            return "Drum";
//        }
//        if(type.contains("bag")){
//            return "Bag";
//        }
//        if(type.contains("iso")){
//            return "ISO Tank";
//        }
//        if(type.contains("can")){
//            return "Drum";
//        }
//        return "Unit";
//    }

    public String getPacking(OrderedProduct product) {
        String type = product.getPackaging() == null ? null : product.getPackaging().getType();
        return type == null ? "Unit" : StringUtils.capitalize(type.toLowerCase());
    }

}
