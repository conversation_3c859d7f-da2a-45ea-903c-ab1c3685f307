package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.config.AssetConfig;
import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.mstack.keystone.constants.AppConstants.INVOICE_DATE_ERROR;

@Component
public class ChemPLAggregator implements IDocumentAggregator {

    @Autowired
    CustomerOrderService customerOrderService;

    @Autowired
    OrderBookService orderBookService;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    SupplierOrderService supplierOrderService;
    @Autowired
    AssetConfig assetConfig;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;

    @Autowired
    PackagingRepository packagingRepository;

    @Autowired
    DocGenRuleService docGenRuleService;

    @Override
    @SneakyThrows
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        List<TemplatePlaceholder> values = new ArrayList<>();
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
//        OrderBook orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
        List<SupplierOrder> supplierOrders = supplierOrderService.findAllLinkedOrders(customerOrder.getId());
        values.add(new TemplatePlaceholder("DOCUMENT_NAME", "DOCUMENT_NAME", "text", "Chemstack Tax Invoice"));
        values.add(new TemplatePlaceholder("CHEMSTACK_LOGO", "CHEMSTACK_LOGO", "image", assetConfig.getChemstackLogo()));
        values.add(new TemplatePlaceholder("MOBILE_NUMBER", "MOBILE_NUMBER", "text", "+91-9925138848"));
        // TODO VERFIY : ASSUMING DISPTACH ORDER ID AS INVOICE NUMBER
//        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text",generateInvoiceNumber(customerOrder)));
        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text", customerOrder.getChemstackInvoiceNumber()));
        values.add(new TemplatePlaceholder("CHEMSTACK_ADDRESS", "CHEMSTACK_ADDRESS", "text",AddressType.CHEMSTACK_MUMBAI.getAddressValue()));
        if (customerOrder.getChemstackInvoiceDate() == null) {
            throw new ServiceException(INVOICE_DATE_ERROR, 400);
        }
        if (!customerOrder.isInvoiceDateValidationDisabled())
            docGenRuleService.validateInvoiceDate(customerOrder.getChemstackInvoiceDate());

        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", commonUtils.formatDate(customerOrder.getChemstackInvoiceDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_DATE", "BUYER_ORDER_DATE", "text", commonUtils.formatDate(customerOrder.getPurchaseOrderDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_NO", "BUYER_ORDER_NO", "text", customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("CONSIGNEE_ADDRESS", "CONSIGNEE_ADDRESS", "text", customerOrder.getConsignee()));
        values.add(new TemplatePlaceholder("NOTIFY_PARTY", "NOTIFY_PARTY", "text", customerOrder.getNotifyParty()));
        values.add(new TemplatePlaceholder("IEC_NO", "IEC_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("PAN_NO", "PAN_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("GSTIN_NO", "GSTIN_NO", "text", "27**********1ZS"));
        values.add(new TemplatePlaceholder("CIN_NO", "CIN_NO", "text", "U24290MH2022FTC385523"));
        values.add(new TemplatePlaceholder("TRADE_CODE", "TRADE_CODE", "text", customerOrder.getTradeAgreementCode()));
        values.add(new TemplatePlaceholder("MSTACK_ADDR", "MSTACK_ADDR", "text", AddressType.MSTACK.getAddressValue()));
        values.add(new TemplatePlaceholder("RECIEPT_CITY", "RECIEPT_CITY", "text", "Mumbai"));
        values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN_OF_GOODS", "COUNTRY_OF_ORIGIN_OF_GOODS", "text", customerOrder.getCountryOfOrigin()));
        values.add(new TemplatePlaceholder("COUNTRY_OF_FINAL_DESTINATION", "COUNTRY_OF_FINAL_DESTINATION", "text", customerOrder.getIncoterms().getCountry()));
        values.add(new TemplatePlaceholder("PLACE_OF_DELIVERY", "PLACE_OF_DELIVERY", "text", getPLaceOfDelivery(customerOrder)));
        values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN", "COUNTRY_OF_ORIGIN", "text", customerOrder.getCountryOfOrigin()));
        values.add(new TemplatePlaceholder("COUNTRY_OF_DESTINATION", "COUNTRY_OF_DESTINATION", "text",customerOrder.getIncoterms().getCountry() ));
        values.add(new TemplatePlaceholder("STATE_OF_ORIGIN", "STATE_OF_ORIGIN", "text", "24"));
        values.add(new TemplatePlaceholder("DISTRICT_OF_ORIGIN", "DISTRICT_OF_ORIGIN", "text", "461"));
        values.add(new TemplatePlaceholder("PRECARRIAGE", "PRECARRIAGE", "text", "By road"));
        values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text", customerOrder.getIncoterms().getData().get("portOfLoading")));
        values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text", customerOrder.getIncoterms().getData().getOrDefault("portOfDischarge","")));
        values.add(new TemplatePlaceholder("PAYMENT_TERM", "PAYMENT_TERM", "text", customerOrder.getPaymentTermsString()));
        values.add(new TemplatePlaceholder("VESSEL_FLIGHT_NO", "VESSEL_FLIGHT_NO", "text", customerOrder.getVesselNumber()));
        values.add(new TemplatePlaceholder("VOYAGE_NAME", "VOYAGE_NAME", "text", customerOrder.getVoyageName()));
        values.add(new TemplatePlaceholder("CONTAINER_NO", "CONTAINER_NO", "text", customerOrder.getMarksAndContainers()));
        List<HashMap> list=new ArrayList<>();
        double totalNetWeight=0.0;
        double totalGrossWeight=0.0;
        double totalTareWeight=0.0;
        double totalOrderValue = 0.0;
        for (OrderedProduct orderedProduct:customerOrder.getProducts()) {
            if(orderedProduct.getBatchData()==null){
                List<ProductBatchDetail> productBatchDetails=null;
                if(customerOrder.getInventoryId()==null){
                    productBatchDetails=productBatchDetailRepository.findAllocatedBatches(customerOrder.getId());
                }
                else{
                    productBatchDetails=productBatchDetailRepository.findByInventoryOrderId(customerOrder.getId());
                }
                List<ProductBatchDetail> orderedProductBatchdetails=productBatchDetails.stream().filter(productBatchDetail -> productBatchDetail.getProductId().equals(orderedProduct.getProduct().getId())
                        && productBatchDetail.getPackagingId().equals(orderedProduct.getPackaging().getId())
                        &&( productBatchDetail.getType().equals("UN_ALLOCATED") || productBatchDetail.getType().equals("ALLOCATED"))).collect(Collectors.toList());
                List<HashMap<String,Object>> batchList=new ArrayList<>();
                Packaging packaging=packagingRepository.findById(orderedProduct.getPackaging().getId()).get();
                for (ProductBatchDetail batch:orderedProductBatchdetails) {
                    HashMap batchMap=new HashMap();
                    batchMap.put("tare_weight", commonUtils.getConvertedWeight(packaging.getTWeight(), commonUtils.getUOM(packaging.getTWeightUom()), UnitOfMeasure.KILOGRAM));
                    batchMap.put("manufacturing_date",batch.getMfgDate());
                    batchMap.put("batch_number",batch.getBatchNumber());
                    batchMap.put("net_weight",orderedProduct.getQuantityPerUnit());
                    batchMap.put("number_of_units",batch.getUnits());
                    batchMap.put("expiry_date",batch.getExpDate());
                    batchList.add(batchMap);
                }
                orderedProduct.setBatchData(batchList);
            }
            HashMap map=new HashMap<>();
            map.put("PACKAGES",getPackages(orderedProduct));
            if (orderedProduct.getProductNameAlias() != null && !orderedProduct.getProductNameAlias().isBlank())
                map.put("DESCRIPTION", orderedProduct.getProductNameAlias());
            else map.put("DESCRIPTION", orderedProduct.getProduct().getTradeName());
            map.put("HS_CODE",orderedProduct.getHsCode());
            double netWeight = getNetWeightForProduct(orderedProduct);
            netWeight = commonUtils.roundOff(netWeight);
            totalNetWeight+=netWeight;
            double grossWeight = getGrossWeight(orderedProduct);
            double tareWeight = getTareWeight(orderedProduct);
            grossWeight = commonUtils.roundOff(grossWeight);
//            if (!orderedProduct.getUom().equals(UnitOfMeasure.KILOGRAM)) {
//                tareWeight = getConvertedTareWeight(tareWeight, orderedProduct.getUom());
//            }
            tareWeight = commonUtils.roundOff(tareWeight);
            totalTareWeight+=tareWeight;
            totalGrossWeight+=grossWeight;
            map.put("QUANTITY",netWeight);
            map.put("NET_WEIGHT",netWeight);
            map.put("GROSS_WEIGHT",grossWeight);
            totalOrderValue += commonUtils.roundOff(netWeight * orderedProduct.getChemstackPrice());
            list.add(map);
        }
        totalNetWeight = commonUtils.roundOff(totalNetWeight);
        values.add(new TemplatePlaceholder("TOTAL_NET_WEIGHT", "TOTAL_NET_WEIGHT", "text", totalNetWeight)); // Replace with the actual total in INR
        if(customerOrder.getNumberOfPallets()!=null&&customerOrder.getNumberOfPallets()!=0){
            totalGrossWeight += (customerOrder.getNumberOfPallets() * customerOrder.getPalletWt());
            totalTareWeight += (customerOrder.getNumberOfPallets() * customerOrder.getPalletWt());
            values.add(new TemplatePlaceholder("PALLET_DATA", "PALLET_DATA", "text", "Pallets - " + customerOrder.getNumberOfPallets() + " x " + customerOrder.getPalletWt())); // Replace with the actual total in INR
            values.add(new TemplatePlaceholder("PALLET_TOTAL", "PALLET_TOTAL", "text", customerOrder.getNumberOfPallets() * customerOrder.getPalletWt())); // Replace with the actual total in INR
        }
        totalTareWeight = commonUtils.roundOff(totalTareWeight);
        totalGrossWeight = commonUtils.roundOff(totalGrossWeight);
        values.add(new TemplatePlaceholder("TOTAL_TARE_WEIGHT", "TOTAL_TARE_WEIGHT", "text", totalTareWeight)); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("TOTAL_GROSS_WEIGHT", "TOTAL_GROSS_WEIGHT", "text", totalGrossWeight)); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("TABLE_DATA", "TABLE_DATA", "table",list));
//        double totalOrderValue = commonUtils.getTotalOrderValue(customerOrder);
        totalOrderValue = commonUtils.roundOff(totalOrderValue);
        values.add(new TemplatePlaceholder("TOTAL", "TOTAL", "text", totalOrderValue)); // Replace with the actual total
        values.add(new TemplatePlaceholder("TOTAL_IN_WORDS", "TOTAL_IN_WORDS", "text", "USD " + NumberToWordConverter.convertToWord(totalOrderValue)+" only"));
        if(customerOrder.getCustomExchangeRate()==null){
            throw new ServiceException("Exchange rate in dollars not present",500);
        }
        if (customerOrder.getCustomExchangeRate() <= 0)
            throw new ServiceException("customer order is have custom exchange rate less the equal to zero . Please update ", 400);
        values.add(new TemplatePlaceholder("EXCHANGE_RATE", "EXCHANGE_RATE", "text", customerOrder.getCustomExchangeRate()));
        Double totalInInr = commonUtils.roundOff(totalOrderValue * customerOrder.getCustomExchangeRate());
        values.add(new TemplatePlaceholder("TOTAL_IN_INR", "TOTAL_IN_INR", "text", totalInInr.toString())); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("ARN_NUMBER", "ARN_NUMBER", "text", "ARN." + AppConstants.LUT_NUMBER));
        values.add(new TemplatePlaceholder("TOTAL_BEFORE_GST", "TOTAL_BEFORE_GST", "text", totalInInr.toString())); // Replace with the actual total before GST
        values.add(new TemplatePlaceholder("IGST", "IGST", "text", customerOrder.getIgstAmt())); // Replace with the actual IGST
        values.add(new TemplatePlaceholder("SIGNATURE_1", "SIGNATURE_1", "image", assetConfig.getMstackStamp()));
        IncoTerms incoTerms=customerOrder.getIncoterms();
        String deliveryTerms = (incoTerms.getType().equals(IncoType.DDP) ? "CIF" : incoTerms.getType()) + " - " + incoTerms.getData().getOrDefault("portOfDischarge", "");
        if (incoTerms.getType().equals(IncoType.FOB)) {
            deliveryTerms = incoTerms.getType() + " - " + incoTerms.getData().getOrDefault("portOfLoading", "");
        }
        values.add(new TemplatePlaceholder("DELIVERY_TERMS", "DELIVERY_TERMS", "text", deliveryTerms));
//        values.add(new TemplatePlaceholder("DELIVERY_TERMS", "DELIVERY_TERMS", "text", (incoTerms.getType().equals(IncoType.DDP)?"CIF":incoTerms.getType()) +" - "+incoTerms.getData().getOrDefault("portOfDischarge","")));
        return values;
    }

    private double getConvertedTareWeight(double tareWeight, UnitOfMeasure uom) {
        switch (uom) {
            case POUND -> {
                return tareWeight * 2.2046;
            }
            case METRIC_TON -> {
                return tareWeight * 0.001;
            }
            default -> {
                return tareWeight;
            }
        }
    }

    private List<Packaging> getPackageList(List<OrderedProduct> products) {
        List<Packaging> packageList = new ArrayList<>();
        products.forEach(orderedProduct -> {
            packageList.add(orderedProduct.getPackaging());
        });
        return packageList;
    }


    private double getGrossWeight(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","0.0").toString());
//            if (!orderedProduct.getUom().equals(UnitOfMeasure.KILOGRAM)) {
//                tareWeight = getConvertedTareWeight(tareWeight, orderedProduct.getUom());
//            }
            netWeight = commonUtils.roundOff(netWeight * orderedProduct.getPerUnitKgValue());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units",0.0).toString());
            output+=(numberOfUnits*(netWeight+tareWeight));
        }
        return output;
    }

    private String getPLaceOfDelivery(CustomerOrder customerOrder){
        String output="";
        IncoTerms incoTerms=customerOrder.getIncoterms();
        if(incoTerms.getType().equals(IncoType.CIF)){
            return incoTerms.getData().getOrDefault("portOfDischarge","")+", "+incoTerms.getCountry();
        }
        if(incoTerms.getType().equals(IncoType.DDP)){
            return incoTerms.getData().getOrDefault("portOfDischarge","")+", "+incoTerms.getCountry();
        }
        output+=customerOrder.getIncoterms().getCountry();
        return output;
    }

    private double getTareWeight(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0.0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","0.0").toString());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units",0.0).toString());
            output+=(numberOfUnits*(tareWeight));
        }
        return output;
    }

    private double getNetWeightForProduct(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0.0;
        }
            for (HashMap  map:orderedProduct.getBatchData()) {
                double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
                netWeight = commonUtils.roundOff(netWeight * orderedProduct.getPerUnitKgValue());
                double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units",0.0).toString());
                output+=(numberOfUnits*(netWeight));
            }
        return output;
    }


    private List<List<String>> getPackages(OrderedProduct product) throws ParseException {
        // Check for 'Others' packaging and otherPackagingDetails at both packaging and product level
        String otherPackagingDetails = null;
        if (product.getPackaging() != null &&
            "Others".equalsIgnoreCase(product.getPackaging().getType())) {
            if (product.getPackaging().getOtherPackagingDetails() != null &&
                !product.getPackaging().getOtherPackagingDetails().isEmpty()) {
                otherPackagingDetails = product.getPackaging().getOtherPackagingDetails();
            } else if (product.getOtherPackagingDetails() != null &&
                       !product.getOtherPackagingDetails().isEmpty()) {
                otherPackagingDetails = product.getOtherPackagingDetails();
            }
        }
        if (otherPackagingDetails != null) {
            List<List<String>> result = new ArrayList<>();
            result.add(List.of(otherPackagingDetails));
            return result;
        }
        List<List<String>> list=new ArrayList<>();
        if(product.getBatchData()==null){
            return new ArrayList<>();
        }
        for (HashMap  map:product.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","").toString());
//            netWeight = commonUtils.roundOff(netWeight * product.getPerUnitKgValue());
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","").toString());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units",0.0).toString());
            productList.add("Net wt. " + numberOfUnits + " x " + commonUtils.roundOff(netWeight * product.getPerUnitKgValue()) + "kg");
            productList.add("Gross Wt. " + numberOfUnits + " x " + commonUtils.roundOff(tareWeight + (netWeight) * product.getPerUnitKgValue()) + "kg");
            productList.add("Batch no."+map.getOrDefault("batch_number","").toString());

//            productList.add("Manufacturing Date. "+commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date",null)));
//            productList.add("Expiry Date. "+commonUtils.formatDate((Date) map.getOrDefault("expiry_date","")));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            SimpleDateFormat monthAndYear = new SimpleDateFormat("MMM yyyy");
            if (map.getOrDefault("manufacturing_date", null) != null) {
                if (map.get("manufacturing_date") instanceof Date) {
                    if (!product.isBatchDateVisible()) {
//                        System.out.println("formatting date");
                        productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null), monthAndYear));
                    } else
                        productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null)));
                } else {
                    Date mfgDate = sdf.parse((String) map.get("manufacturing_date"));
                    if (!product.isBatchDateVisible())
                        productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate, monthAndYear));
                    else productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate));
                }
            } else productList.add("Manufacturing Date. ");
            if (map.getOrDefault("expiry_date", null) != null) {
                if (map.get("expiry_date") instanceof Date) {
                    if (!product.isBatchDateVisible())
                        productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", ""), monthAndYear));
                    else
                        productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", "")));
                } else {
                    Date expDate = sdf.parse((String) map.get("expiry_date"));
                    if (!product.isBatchDateVisible())
                        productList.add("Expiry Date. " + commonUtils.formatDate(expDate, monthAndYear));
                    else productList.add("Expiry Date. " + commonUtils.formatDate(expDate));
                }
            } else productList.add("Expiry Date. ");

            list.add(productList);
        }
        return list;
    }

    private String generateInvoiceNumber(CustomerOrder customerOrder) {
//        TODO GB
//        long count= docMetaRepository.getCountOFDocType("CHEMSTACK_TAX_INVOICE").size();
//        return "INV-"+commonUtils.getCurrentFinancialYear()+"-"+count+1;
        return customerOrder.getOrderId();
    }

}
