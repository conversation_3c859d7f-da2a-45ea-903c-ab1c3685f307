package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.config.AssetConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.CurrencyType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.mstack.keystone.constants.AppConstants.INVOICE_DATE_ERROR;

@Component
public class MstackPLAggregator implements IDocumentAggregator{
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    OrderBookService orderBookService;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    SupplierOrderService supplierOrderService;
    @Autowired
    AssetConfig assetConfig;
    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;
    @Autowired
    PackagingRepository packagingRepository;

    @Autowired
    DocGenRuleService docGenRuleService;

    @Override
    @SneakyThrows
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        List<TemplatePlaceholder> values = new ArrayList<>();
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
        if(customerOrder.getInventoryId()!=null){
            throw new ServiceException("Invalid document for an inventory order",500);
        }
        OrderBook orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
        List<SupplierOrder> supplierOrders = supplierOrderService.findAllLinkedOrders(customerOrder.getId());
        values.add(new TemplatePlaceholder("DOCUMENT_NAME", "DOCUMENT_NAME", "text", "MSTACK PACKING LIST"));
        values.add(new TemplatePlaceholder("CHEMSTACK_LOGO", "CHEMSTACK_LOGO", "image", assetConfig.getChemstackLogo()));
        // TODO WHOSE MOBILE NUMBER SHOULD COME ?
        values.add(new TemplatePlaceholder("MOBILE_NUMBER", "MOBILE_NUMBER", "text", "+91-9925138848"));
        // TODO VERFIY : ASSUMING DISPTACH ORDER ID AS INVOICE NUMBER
//        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text",generateInvoiceNumber(customerOrder)));
        values.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text", customerOrder.getMstackInvoiceNumber()));
        if (customerOrder.getInvoiceDate() == null) {
            throw new ServiceException(INVOICE_DATE_ERROR, 400);
        }
        if (!customerOrder.isInvoiceDateValidationDisabled())
            docGenRuleService.validateInvoiceDate(customerOrder.getInvoiceDate());

        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", commonUtils.formatDate(customerOrder.getInvoiceDate())));
        values.add(new TemplatePlaceholder("PO_NUMBER", "PO_NUMBER", "text",customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("CURRENCY", "CURRENCY", "text", CurrencyType.DOLLAR.getAbbreviation()));
        values.add(new TemplatePlaceholder("PACKAGING_LIST_NUMBER", "PACKAGING_LIST_NUMBER", "text",customerOrder.getMstackInvoiceNumber()));
        values.add(new TemplatePlaceholder("BUYER_ORDER_DATE", "BUYER_ORDER_DATE", "text", commonUtils.formatDate(customerOrder.getPurchaseOrderDate())));
        values.add(new TemplatePlaceholder("BUYER_ORDER_NO", "BUYER_ORDER_NO", "text", customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("CONSIGNEE", "CONSIGNEE", "text", customerOrder.getConsignee()));
        values.add(new TemplatePlaceholder("NOTIFY_PARTY", "NOTIFY_PARTY", "text", customerOrder.getNotifyParty()));
        values.add(new TemplatePlaceholder("IEC_NO", "IEC_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("PAN_NO", "PAN_NO", "text", "**********"));
        values.add(new TemplatePlaceholder("GSTIN_NO", "GSTIN_NO", "text", "27**********1ZS"));
        values.add(new TemplatePlaceholder("CIN_NO", "CIN_NO", "text", "U24290MH2022FTC385523"));
        values.add(new TemplatePlaceholder("TRADE_CODE", "TRADE_CODE", "text", customerOrder.getTradeAgreementCode()));
        values.add(new TemplatePlaceholder("MSTACK_ADDRESS", "MSTACK_ADDRESS", "text", AddressType.MSTACK.getAddressValue()));
        values.add(new TemplatePlaceholder("RECIEPT_CITY", "RECIEPT_CITY", "text", "Mumbai"));
        values.add(new TemplatePlaceholder("COUNTRY_OF_FINAL_DESTINATION", "COUNTRY_OF_FINAL_DESTINATION", "text", customerOrder.getIncoterms().getCountry()));
        values.add(new TemplatePlaceholder("PLACE_OF_DELIVERY", "PLACE_OF_DELIVERY", "text", customerOrder.getIncoterms().getCountry()));
        // INCLUDE COUNTRY OF ORIGIN only if incoterm is not ddp
        if (!customerOrder.getIncoterms().getType().equals(IncoType.DDP)) {
            values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN_OF_GOODS", "COUNTRY_OF_ORIGIN_OF_GOODS", "text", customerOrder.getCountryOfOrigin()));
            values.add(new TemplatePlaceholder("COUNTRY_OF_ORIGIN", "COUNTRY_OF_ORIGIN", "text", customerOrder.getCountryOfOrigin()));
        }
        values.add(new TemplatePlaceholder("COUNTRY_OF_DESTINATION", "COUNTRY_OF_DESTINATION", "text", orderBook.getCustomer().getAddress().getCountry()));
        values.add(new TemplatePlaceholder("DELIVERY_TERMS", "DELIVERY_TERMS", "text", customerOrder.getIncoterms().getType()));
        values.add(new TemplatePlaceholder("STATE_OF_ORIGIN", "STATE_OF_ORIGIN", "text", "24"));
        values.add(new TemplatePlaceholder("DISTRICT_OF_ORIGIN", "DISTRICT_OF_ORIGIN", "text", "461"));
        values.add(new TemplatePlaceholder("PRECARRIAGE", "PRECARRIAGE", "text", "By road"));
        if (!customerOrder.getIncoterms().getType().equals(IncoType.DDP)) {
            values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text", customerOrder.getIncoterms().getData().getOrDefault("portOfLoading", "")));
            values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text", customerOrder.getIncoterms().getData().getOrDefault("portOfDischarge", "")));
        }
        values.add(new TemplatePlaceholder("PAYMENT_TERM", "PAYMENT_TERM", "text", customerOrder.getPaymentTermsString()));
        values.add(new TemplatePlaceholder("VESSEL_FLIGHT_NO", "VESSEL_FLIGHT_NO", "text", customerOrder.getVesselNumber()));
        values.add(new TemplatePlaceholder("VOYAGE_NAME", "VOYAGE_NAME", "text", customerOrder.getVoyageName()));
        values.add(new TemplatePlaceholder("CONTAINER_NUMBER", "CONTAINER_NUMBER", "text", customerOrder.getMarksAndContainers()));
        List<HashMap> list=new ArrayList<>();
        double totalNetWeight=0.0;
        double totalGrossWeight=0.0;
        double totalTareWeight=0.0;
        for (OrderedProduct orderedProduct:customerOrder.getProducts()) {
            if(orderedProduct.getBatchData()==null){
                List<ProductBatchDetail> productBatchDetails=productBatchDetailRepository.findAllocatedBatches(customerOrder.getId());
                List<ProductBatchDetail> orderedProductBatchdetails=productBatchDetails.stream().filter(productBatchDetail -> productBatchDetail.getProductId().equals(orderedProduct.getProduct().getId())
                        &&productBatchDetail.getPackagingId().equals(orderedProduct.getPackaging().getId())
                        && productBatchDetail.getType().equals("ALLOCATED")).collect(Collectors.toList());
                List<HashMap<String,Object>> batchList=new ArrayList<>();
                Packaging packaging=packagingRepository.findById(orderedProduct.getPackaging().getId()).get();
                for (ProductBatchDetail batch:orderedProductBatchdetails) {
                    HashMap batchMap=new HashMap();
                    batchMap.put("tare_weight",packaging.getTWeight());
                    batchMap.put("manufacturing_date", batch.getMfgDate());
                    batchMap.put("batch_number",batch.getBatchNumber());
                    batchMap.put("net_weight",orderedProduct.getQuantityPerUnit());
                    batchMap.put("number_of_units",batch.getUnits());
                    batchMap.put("expiry_date", batch.getExpDate());
                    batchList.add(batchMap);
                }
                orderedProduct.setBatchData(batchList);
            }
            HashMap map=new HashMap<>();
            map.put("PACKAGES",getPackages(orderedProduct));
            if (orderedProduct.getProductNameAlias() != null && !orderedProduct.getProductNameAlias().isBlank())
                map.put("DESCRIPTION", orderedProduct.getProductNameAlias());
            else map.put("DESCRIPTION", orderedProduct.getProduct().getTradeName());
            map.put("UOM", getAbbreviation(orderedProduct.getUom()));
            map.put("HS_CODE",orderedProduct.getHsCode());
            double netWeight=getNetWeightForProduct(orderedProduct);
            netWeight = commonUtils.roundOff(netWeight);
            totalNetWeight+=netWeight;
            double grossWeight = commonUtils.roundOff(getGrossWeight(orderedProduct));
            double tareWeight = commonUtils.roundOff(getTareWeight(orderedProduct));
            if (!orderedProduct.getUom().equals(UnitOfMeasure.KILOGRAM)) {
                tareWeight = getConvertedTareWeight(tareWeight, orderedProduct.getUom());
            }
            totalTareWeight+=tareWeight;
            totalGrossWeight+=grossWeight;
            map.put("QUANTITY",netWeight);
            map.put("NET_WEIGHT",netWeight);
            map.put("GROSS_WEIGHT",grossWeight);
            list.add(map);
        }
        totalNetWeight = commonUtils.roundOff(totalNetWeight);
        values.add(new TemplatePlaceholder("TOTAL_NET_WEIGHT", "TOTAL_NET_WEIGHT", "text", totalNetWeight)); // Replace with the actual total in INR
        if(customerOrder.getNumberOfPallets()!=null&&customerOrder.getNumberOfPallets()!=0){
            if (customerOrder.getMstackPalletWt() != null) {

                totalGrossWeight += (customerOrder.getNumberOfPallets() * customerOrder.getMstackPalletWt());
                totalTareWeight += (customerOrder.getNumberOfPallets() * customerOrder.getMstackPalletWt());
            } else {
                totalGrossWeight += (customerOrder.getNumberOfPallets() * customerOrder.getPalletWt());
                totalTareWeight += (customerOrder.getNumberOfPallets() * customerOrder.getPalletWt());
            }
            values.add(new TemplatePlaceholder("PALLET_DATA", "PALLET_DATA", "text", "Pallets - " + customerOrder.getNumberOfPallets() + " x " + ((customerOrder.getMstackPalletWt() != null) ? customerOrder.getMstackPalletWt() : customerOrder.getPalletWt()))); // Replace with the actual total in INR
            values.add(new TemplatePlaceholder("PALLET_TOTAL", "PALLET_TOTAL", "text", customerOrder.getNumberOfPallets() * ((customerOrder.getMstackPalletWt() != null) ? customerOrder.getMstackPalletWt() : customerOrder.getPalletWt()))); // Replace with the actual total in INR
        }
        totalGrossWeight = commonUtils.roundOff(totalGrossWeight);
        totalTareWeight = commonUtils.roundOff(totalTareWeight);
        values.add(new TemplatePlaceholder("TOTAL_TARE_WEIGHT", "TOTAL_TARE_WEIGHT", "text", totalTareWeight)); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("TOTAL_GROSS_WEIGHT", "TOTAL_GROSS_WEIGHT", "text", totalGrossWeight)); // Replace with the actual total in INR
        values.add(new TemplatePlaceholder("TABLE_DATA", "TABLE_DATA", "table",list));
        double totalOrderValue = commonUtils.getTotalOrderValue(customerOrder);
        totalOrderValue = commonUtils.roundOff(totalOrderValue);
        values.add(new TemplatePlaceholder("TOTAL", "TOTAL", "text", totalOrderValue)); // Replace with the actual total
        values.add(new TemplatePlaceholder("TOTAL_IN_WORDS", "TOTAL_IN_WORDS", "text", "USD " + NumberToWordConverter.convertToWord(totalOrderValue)+" only"));
//        if(customerOrder.getCustomExchangeRate()==null){
//            throw new ServiceException("Exchange rate in dollars not present",500);
//        }
//        if (customerOrder.getCustomExchangeRate() <= 0)
//            throw new ServiceException("customer order is have custom exchange rate less the equal to zero . Please update ", 400);

        values.add(new TemplatePlaceholder("EXCHANGE_RATE", "EXCHANGE_RATE", "text", customerOrder.getCustomExchangeRate()));
        values.add(new TemplatePlaceholder("INCOTERM", "INCOTERM", "text", getIncoterms(customerOrder)));
        if(customerOrder.getIncoterms().getType().equals(IncoType.CIF)){
            values.add(new TemplatePlaceholder("FREIGHT_TERM", "FREIGHT_TERM", "image", "Freight terms: Prepaid"));
        }
        if(customerOrder.getIncoterms().getType().equals(IncoType.FOB)){
            values.add(new TemplatePlaceholder("FREIGHT_TERM", "FREIGHT_TERM", "image", "Freight terms: Collect"));
        }
        return values;
    }

    private double getConvertedTareWeight(double tareWeight, UnitOfMeasure uom) {
        switch (uom) {
            case POUND -> {
                return tareWeight * 2.2046;
            }
            case METRIC_TON -> {
                return tareWeight * 0.001;
            }
            default -> {
                return tareWeight;
            }
        }
    }

    private List<Packaging> getPackageList(List<OrderedProduct> products) {
        List<Packaging> packageList = new ArrayList<>();
        products.forEach(orderedProduct -> {
            packageList.add(orderedProduct.getPackaging());
        });
        return packageList;
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "lb";
            case GALLON -> "gal";
            case LITRE -> "L";
            case KILOLITRE -> "kL";
            case KILOGRAM -> "kg";
            default -> "";
        };
    }



    private String getIncoterms(CustomerOrder customerOrder){
        String value="";
        if(customerOrder.getIncoterms().getType().equals(IncoType.CIF)||customerOrder.getIncoterms().getType().equals(IncoType.CFR)){
            value+=customerOrder.getIncoterms().getType().toString()+"-";
            IncoTerms incoTerms=customerOrder.getIncoterms();
            value+=incoTerms.getData().getOrDefault("portOfDischarge","").toString();
            value+=", ";
            value+= incoTerms.getCountry();
        }
        else if(customerOrder.getIncoterms().getType().equals(IncoType.DDP)){
            value+=customerOrder.getIncoterms().getType().toString()+"-";
            IncoTerms incoTerms=customerOrder.getIncoterms();
            value += incoTerms.getData().getOrDefault("city", "").toString();
            value+=", ";
            value+= incoTerms.getCountry();
        } else if (customerOrder.getIncoterms().getType().equals(IncoType.FOB)) {
            value += customerOrder.getIncoterms().getType().toString() + "-";
            IncoTerms incoTerms = customerOrder.getIncoterms();
            value += incoTerms.getData().getOrDefault("portOfLoading", "");
        }
        return value;
    }

    private double getGrossWeight(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","0.0").toString());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units","0").toString());
            if (!orderedProduct.getUom().equals(UnitOfMeasure.KILOGRAM)) {
                tareWeight = getConvertedTareWeight(tareWeight, orderedProduct.getUom());
            }
            output+=(numberOfUnits*(netWeight+tareWeight));
        }
        return output;
    }
    private double getTareWeight(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","0.0").toString());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units","0").toString());
            output+=(numberOfUnits*(tareWeight));
        }
        return output;
    }

    private double getNetWeightForProduct(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units","0").toString());
            output+=(numberOfUnits*(netWeight));
        }
        return output;
    }


    private List<List<String>> getPackages(OrderedProduct product) throws ParseException {
        // Check for 'Others' packaging and otherPackagingDetails at both packaging and product level
        String otherPackagingDetails = null;
        if (product.getPackaging() != null &&
            "Others".equalsIgnoreCase(product.getPackaging().getType())) {
            if (product.getPackaging().getOtherPackagingDetails() != null &&
                !product.getPackaging().getOtherPackagingDetails().isEmpty()) {
                otherPackagingDetails = product.getPackaging().getOtherPackagingDetails();
            } else if (product.getOtherPackagingDetails() != null &&
                       !product.getOtherPackagingDetails().isEmpty()) {
                otherPackagingDetails = product.getOtherPackagingDetails();
            }
        }
        if (otherPackagingDetails != null) {
            List<List<String>> result = new ArrayList<>();
            result.add(List.of(otherPackagingDetails));
            return result;
        }
        List<List<String>> list=new ArrayList<>();
        if(product.getBatchData()==null){
            return new ArrayList<>();
        }
        for (HashMap  map:product.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight = Double.parseDouble(map.getOrDefault("net_weight", 0.0).toString());
            double tareWeight = Double.parseDouble(map.getOrDefault("tare_weight", 0.0).toString());
            double numberOfUnits=Double.parseDouble(map.getOrDefault("number_of_units",0.0).toString());
            if (!product.getUom().equals(UnitOfMeasure.KILOGRAM)) {
                tareWeight = getConvertedTareWeight(tareWeight, product.getUom());
            }
            productList.add("Net wt. " + numberOfUnits + " x " + commonUtils.roundOff(netWeight));
            productList.add("Gross Wt. " + numberOfUnits + " x " + commonUtils.roundOff(netWeight + tareWeight));
            productList.add("Batch no."+map.getOrDefault("batch_number","").toString());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            SimpleDateFormat monthAndYear = new SimpleDateFormat("MMM yyyy");
            if (map.getOrDefault("manufacturing_date", null) != null) {
                if (map.get("manufacturing_date") instanceof Date) {
                    if (!product.isBatchDateVisible()) {
//                        System.out.println("formatting date");
                        productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null), monthAndYear));
                    } else
                        productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null)));
                } else {
                    Date mfgDate = sdf.parse((String) map.get("manufacturing_date"));
                    if (!product.isBatchDateVisible())
                        productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate, monthAndYear));
                    else productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate));
                }
            } else productList.add("Manufacturing Date. ");
            if (map.getOrDefault("expiry_date", null) != null) {
                if (map.get("expiry_date") instanceof Date) {
                    if (!product.isBatchDateVisible())
                        productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", ""), monthAndYear));
                    else
                        productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", "")));
                } else {
                    Date expDate = sdf.parse((String) map.get("expiry_date"));
                    if (!product.isBatchDateVisible())
                        productList.add("Expiry Date. " + commonUtils.formatDate(expDate, monthAndYear));
                    else productList.add("Expiry Date. " + commonUtils.formatDate(expDate));
                }
            } else productList.add("Expiry Date. ");
            list.add(productList);
        }
        return list;
    }

    private String generateInvoiceNumber(CustomerOrder customerOrder) {
//        long count= docMetaRepository.getCountOFDocType("CHEMSTACK_TAX_INVOICE").size();
//        return "INV-"+commonUtils.getCurrentFinancialYear()+"-"+count+1;
        return customerOrder.getOrderId();
    }

}
