package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.utils.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import static com.mstack.keystone.constants.AppConstants.INDIA_ZONE_ID;

@Service
public class DocGenRuleService {

    @Autowired
    CommonUtils commonUtils;

    public void validateInvoiceDate(Date invoiceDate) throws ServiceException {
        LocalDate invoiceLD = commonUtils.convertToLocalDateViaInstant(invoiceDate);
        LocalDate currDate = LocalDate.now(ZoneId.of(INDIA_ZONE_ID));
        // apply rule
        LocalDate minDate;
        if (currDate.getDayOfMonth() >= 3) {
            minDate = LocalDate.of(2024, currDate.getMonth(), 3);
        } else {
            // if curr date has same month but is less then 3
            minDate = LocalDate.of(2024, currDate.getMonth().minus(1), 3);
        }
        if (invoiceLD.isBefore(minDate))
            throw new ServiceException("Document cannot be generated for invoice date less then " + minDate, 400);
    }

}
