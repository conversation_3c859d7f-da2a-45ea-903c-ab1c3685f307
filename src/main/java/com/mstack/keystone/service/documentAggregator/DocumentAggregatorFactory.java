package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.exception.ServiceException;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DocumentAggregatorFactory {
    @Autowired
    MToChemPOAggregator mToChemPOAggregator;
    @Autowired
    BLAggregator blAggregator;
    @Autowired
    ChemTaxInvoiceAggregator chemTaxInvoiceAggregator;
    @Autowired
    ChemPLAggregator chemPLAggregator;
    @Autowired
    ChemCommInvoiceAggregator chemCommInvoiceAggregator;
    @Autowired
    MstackPLAggregator mstackPLAggregator;
    @Autowired
    MstackInvoiceAggregator mstackInvoiceAggregator;
    @Autowired
    SupplierPoAggregator supplierPoAggregator;


    @SneakyThrows
    public IDocumentAggregator getDocumentAggregator(String docType){
        switch (docType){
            case "BILL_OF_LADING":
                return blAggregator;
            case "MSTACK_TO_CHEMSTACK_PO":
                return  mToChemPOAggregator;
            case "CHEMSTACK_TAX_INVOICE":
                return  chemTaxInvoiceAggregator;
            case "CHEMSTACK_PACKAGING_LIST":
                return chemPLAggregator;
            case "CHEMSTACK_COMMERCIAL_INVOICE":
                return chemCommInvoiceAggregator;
            case "MSTACK_COMMERCIAL_INVOICE":
                return mstackInvoiceAggregator;
            // TODO GB REMOVE THIS WHEN FRONTEND DOC TYPE IS CHANGED
            case "MSTACK_INVOICE":
                return mstackInvoiceAggregator;
            case "MSTACK_PACKING_LIST":
                return mstackPLAggregator;
            case "SUPPLIER_PO":
                return supplierPoAggregator;
        }
        throw new ServiceException("Invalid Document Type",500);
    }
}
