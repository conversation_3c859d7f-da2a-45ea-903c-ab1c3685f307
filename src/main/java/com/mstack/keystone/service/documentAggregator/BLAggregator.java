package com.mstack.keystone.service.documentAggregator;

import com.mstack.keystone.config.AssetConfig;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.LinkedOrder;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.enums.AddressType;
import com.mstack.keystone.model.enums.IncoType;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.ProductBatchDetailRepository;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.NumberToWordConverter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class BLAggregator implements IDocumentAggregator{
    @Autowired
    CustomerOrderService customerOrderService;
    @Autowired
    OrderBookService orderBookService;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    SupplierOrderService supplierOrderService;
    @Autowired
    AssetConfig assetConfig;

    @Autowired
    ProductBatchDetailRepository productBatchDetailRepository;
    @Autowired
    PackagingRepository packagingRepository;

    @Override
    @SneakyThrows
    public List<TemplatePlaceholder> getDocumentData(HashMap meta) {
        CustomerOrder customerOrder = customerOrderService.getCustomerOrderById(meta.get("customerOrderId").toString());
        List<OrderedProduct> products=customerOrder.getProducts();
        List<TemplatePlaceholder> values = new ArrayList<>();
        String supplierId = meta.getOrDefault("supplierId","").toString();
        if(!supplierId.equals("")){
            List<SupplierOrder> supplierOrders = supplierOrderService.findAllLinkedOrders(customerOrder.getId());
            products = getOrderProductsForBL(customerOrder, supplierId, supplierOrders);
        }
        values.add(new TemplatePlaceholder("MARKS_NO", "MARKS_NO", "text", customerOrder.getMarksAndContainers()));
        values.add(new TemplatePlaceholder("CONSIGNEE", "CONSIGNEE", "text", customerOrder.getConsignee()));
        values.add(new TemplatePlaceholder("SHIPPER", "SHIPPER", "text", "Chemstack Pvt. Ltd."));
        values.add(new TemplatePlaceholder("SHIPPER_ADDRESS", "SHIPPER_ADDRESS", "text",AddressType.CHEMSTACK_MUMBAI.getAddressValue()));
        values.add(new TemplatePlaceholder("NOTIFY_ADDRESS", "NOTIFY_ADDRESS", "text", customerOrder.getNotifyParty()));
        String placeOfDelivery = customerOrder.getIncoterms().getData().getOrDefault("portOfDischarge", "");
        String countryOfDelivery = customerOrder.getIncoterms().getCountry();
        if (!placeOfDelivery.isEmpty() && !countryOfDelivery.isEmpty())
            values.add(new TemplatePlaceholder("PLACE_OF_DELIVERY", "PLACE_OF_DELIVERY", "text", placeOfDelivery + " , " + countryOfDelivery));
        else
            values.add(new TemplatePlaceholder("PLACE_OF_DELIVERY", "PLACE_OF_DELIVERY", "text", placeOfDelivery + countryOfDelivery));
        List marksAndContainersList=new ArrayList();
        double totalNetWeight=0;
        double totalGrossWeight=0;
        for (OrderedProduct product:products) {
            if (product.getBatchData() == null) {
                List<ProductBatchDetail> productBatchDetails = productBatchDetailRepository.findAllocatedBatches(customerOrder.getId());
                List<ProductBatchDetail> orderedProductBatchdetails = productBatchDetails.stream().filter(productBatchDetail -> productBatchDetail.getProductId().equals(product.getProduct().getId())
                        && productBatchDetail.getPackagingId().equals(product.getPackaging().getId())
                        && productBatchDetail.getType().equals("ALLOCATED")).collect(Collectors.toList());
                List<HashMap<String, Object>> batchList = new ArrayList<>();
                Packaging packaging = packagingRepository.findById(product.getPackaging().getId()).get();
                for (ProductBatchDetail batch : orderedProductBatchdetails) {
                    HashMap batchMap = new HashMap();
                    batchMap.put("tare_weight", packaging.getTWeight());
                    batchMap.put("manufacturing_date", batch.getMfgDate());
                    batchMap.put("batch_number", batch.getBatchNumber());
                    batchMap.put("net_weight", product.getQuantityPerUnit());
                    batchMap.put("number_of_units", batch.getUnits());
                    batchMap.put("expiry_date", batch.getExpDate());
                    batchList.add(batchMap);
                }
                product.setBatchData(batchList);
            }
            HashMap map=new HashMap();
            map.put("PRODUCT_NAME",product.getProduct().getTradeName());
            List<List<String>> list=getPackages(product);
            double netWeight=getNetWeightForProduct(product);
            totalNetWeight+=netWeight;
            map.put("PACKAGES",list);
            marksAndContainersList.add(map);
            double grossWeight=getGrossWeight(product);
            totalGrossWeight+=grossWeight;
        }
        List quantityList = getTotalQuantity(products);
        // add pallet weight also
        totalGrossWeight += (customerOrder.getNumberOfPallets() != null) ? customerOrder.getNumberOfPallets() * 16 : 0;
        values.add(new TemplatePlaceholder("MARKS_CONTAINER_DATA", "MARKS_CONTAINER_DATA", "list", marksAndContainersList));
        values.add(new TemplatePlaceholder("QUANTITY_PKGS", "QUANTITY_PKGS", "text", quantityList));
        values.add(new TemplatePlaceholder("SHIPMENT_TYPE", "SHIPMENT_TYPE", "text", customerOrder.getTypeOfShipment()));
        values.add(new TemplatePlaceholder("NET_WEIGHT", "NET_WEIGHT", "text",totalNetWeight+" Kg"));
        values.add(new TemplatePlaceholder("GROSS_WEIGHT", "GROSS_WEIGHT", "text",totalGrossWeight+" Kg"));
        values.add(new TemplatePlaceholder("PO_NUMBER", "PO_NUMBER", "text",customerOrder.getPurchaseOrderNumber()));
        values.add(new TemplatePlaceholder("PO_DATE", "PO_DATE", "text",commonUtils.formatDate(customerOrder.getPurchaseOrderDate())));
        values.add(new TemplatePlaceholder("SHIPPING_BILL_NO", "SHIPPING_BILL_NO", "text","-"));
        values.add(new TemplatePlaceholder("SHIPPING_BILL_DATE", "SHIPPING_BILL_DATE", "text","-"));
        values.add(new TemplatePlaceholder("SHIPPING_BILL_DATE", "SHIPPING_BILL_DATE", "text","-"));
        values.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", customerOrder.getInvoiceDate() == null ? "" : commonUtils.formatDate(customerOrder.getInvoiceDate())));
        values.add(new TemplatePlaceholder("INVOICE_NO", "INVOICE_NO", "text", customerOrder.getMstackInvoiceNumber()));
        values.add(new TemplatePlaceholder("BEHALF_OF", "BEHALF_OF", "text",AddressType.MSTACK.getAddressValue()));

        if (customerOrder.getFreightCost() != null && customerOrder.getFreightCost() > 0) {
            values.add(new TemplatePlaceholder("FREIGHT_COST", "FREIGHT_COST", "text", "$"+customerOrder.getFreightCost()));
        }
        if (customerOrder.getInsuranceCost() != null && customerOrder.getInsuranceCost() > 0) {
            values.add(new TemplatePlaceholder("INSURANCE_COST", "INSURANCE_COST", "text", "$"+customerOrder.getInsuranceCost()));
        }
        IncoTerms incoTerms=customerOrder.getIncoterms();
        if(incoTerms.getType().equals(IncoType.CIF)||incoTerms.getType().equals(IncoType.CFR)||incoTerms.getType().equals(IncoType.DDP))
        {
            values.add(new TemplatePlaceholder("PORT_OF_LOADING", "PORT_OF_LOADING", "text",incoTerms.getData().getOrDefault("portOfLoading","")));
            values.add(new TemplatePlaceholder("PORT_OF_DISCHARGE", "PORT_OF_DISCHARGE", "text",incoTerms.getData().getOrDefault("portOfDischarge","")));

        }
        List productData=new ArrayList();
        for (OrderedProduct product:products) {
            HashMap map=new HashMap();
            map.put("nameAlias", product.getProductNameAlias());
            map.put("name",product.getProduct().getTradeName());
            map.put("hsCode","Export HS Code : "+product.getHsCode());
            map.put("hazInfo",product.getHazDetails());
            productData.add(map);
        }
        values.add(new TemplatePlaceholder("PRODUCT_DATA", "PRODUCT_DATA", "list", productData));

        //        for (OrderedProduct product:products) {
//            HashMap<String,Object> map=new HashMap<>();
//            ArrayList<Object> list=new ArrayList<>();
//            list.add(new TemplatePlaceholder("INVOICE_DATE", "INVOICE_DATE", "text", commonUtils.formatDate(customerOrder.getInvoiceDate())));
//            list.add(new TemplatePlaceholder("INVOICE_NUMBER", "INVOICE_NUMBER", "text", customerOrder.getOrderId()));
//            list.add(new TemplatePlaceholder("SHIPPING_BILL_NO", "SHIPPING_BILL_NO", "text", "-"));
//            list.add(new TemplatePlaceholder("SHIPPING_BILL_DATE", "SHIPPING_BILL_DATE", "text", "-"));
//            list.add(new TemplatePlaceholder("PO_NUMBER", "PO_NUMBER", "text", customerOrder.getPurchaseOrderNumber()));
//            list.add(new TemplatePlaceholder("PO_DATE", "PO_DATE", "text", customerOrder.getPurchaseOrderDate()));
//            list.add(new TemplatePlaceholder("NET_WEIGHT", "NET_WEIGHT", "text", getNetWeightForProduct(product)));
//            map.put("PRODUCT_DATA",list);
//        }

        return values;
    }
    private List<String> getTotalQuantity(List<OrderedProduct> products){
        List<String> list=new ArrayList<>();
        HashMap<String, Double> map = new HashMap<>();
        for (OrderedProduct product:products) {
            String pack=getPacking(product);
            map.put(pack, map.getOrDefault(pack, 0.00) + getUnits(product));
        }
        for (String key: map.keySet()) {
            list.add(map.get(key)+" "+key+"s");
        }
        return list;
    }

    private List<OrderedProduct> getOrderProductsForBL(CustomerOrder customerOrder,String supplierId,
                                                       List<SupplierOrder> supplierOrders){
        Set<OrderedProduct> products=new HashSet<>();
        for (SupplierOrder supplierOrder:supplierOrders) {
            if(supplierId.equals(supplierOrder.getSupplier().getId())){
                for (OrderedProduct product:supplierOrder.getProducts()) {
                    List<LinkedOrder> linkedOrders=product.getLinkedOrders();
                    for (LinkedOrder linkedOrder:linkedOrders) {
                        if(linkedOrder.getOrderId().equals(customerOrder.getOrderId())){
                            for (OrderedProduct orderedProduct:customerOrder.getProducts()) {
                                if(orderedProduct.getProduct().getId().equals(product.getProduct().getId())){
                                    products.add(orderedProduct);
                                }
                            }
                        }
                    }
                }
            }
        }
        return  products.stream().toList();
    }


    private double getNetWeightForProduct(OrderedProduct orderedProduct ){
        double output=0.0;
        if (orderedProduct == null || orderedProduct.getBatchData() == null || orderedProduct.getBatchData().isEmpty())
            return output;
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            double numberOfUnits = Double.parseDouble(map.getOrDefault("number_of_units", "0").toString());
            output+=(numberOfUnits*(netWeight));
        }
        return output;
    }


    private double getUnits(OrderedProduct product) {
        List<HashMap<String,Object>> batches=product.getBatchData();
        if(batches==null){
            return 0;
        }
        int number=0;
        for (HashMap map:batches) {
            number += Double.valueOf(map.getOrDefault("number_of_units", 0).toString());
        }
        return number;
    }

    private List<List<String>> getPackages(OrderedProduct product) throws ParseException {
        List<List<String>> list=new ArrayList<>();
        if(product.getBatchData()==null){
            return new ArrayList<>();
        }
        for (HashMap  map:product.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","").toString());
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","").toString());
            double numberOfUnits = Double.parseDouble(map.getOrDefault("number_of_units", 0.0).toString());
            productList.add("Net wt. "+numberOfUnits+" x "+ (netWeight)+"kg");
            productList.add("Gross Wt. "+numberOfUnits+" x "+ (netWeight+tareWeight)+"kg");
            productList.add("Batch no."+map.getOrDefault("batch_number","").toString());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
            if (map.getOrDefault("manufacturing_date", null) != null) {
                if (map.get("manufacturing_date") instanceof Date) {
                    productList.add("Manufacturing Date. " + commonUtils.formatDate((Date) map.getOrDefault("manufacturing_date", null)));
                } else {
                    Date mfgDate = sdf.parse((String) map.get("manufacturing_date"));
                    productList.add("Manufacturing Date. " + commonUtils.formatDate(mfgDate));
                }
            } else productList.add("Manufacturing Date. ");
            if (map.getOrDefault("expiry_date", null) != null) {
                if (map.get("expiry_date") instanceof Date) {
                    productList.add("Expiry Date. " + commonUtils.formatDate((Date) map.getOrDefault("expiry_date", "")));
                } else {
                    Date expDate = sdf.parse((String) map.get("expiry_date"));
                    productList.add("Expiry Date. " + commonUtils.formatDate(expDate));
                }
            } else productList.add("Expiry Date. ");
            list.add(productList);
        }
        return list;
    }

    private double getGrossWeight(OrderedProduct orderedProduct ){
        double output=0.0;
        if(orderedProduct.getBatchData()==null){
            return 0;
        }
        for (HashMap  map:orderedProduct.getBatchData()) {
            List<String> productList=new ArrayList<>();
            double netWeight=Double.parseDouble(map.getOrDefault("net_weight","0.0").toString());
            double tareWeight=Double.parseDouble(map.getOrDefault("tare_weight","0.0").toString());
            double numberOfUnits = Double.parseDouble(map.getOrDefault("number_of_units", "0").toString());
            output+=(numberOfUnits*(netWeight+tareWeight));
        }
        return output;
    }

    private String getAddressForCToMPo(CustomerOrder customerOrder){
        String value="CIF- ";
        IncoTerms incoTerms=customerOrder.getIncoterms();
        value+=incoTerms.getData().getOrDefault("portOfDischarge","").toString();
        value+=" ,";
        value+= incoTerms.getCountry();
        return value;
    }

    private String convertDateInFormat(Date date, String format) {
        if (date == null) return "";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // Format the date using the SimpleDateFormat object
        return sdf.format(date);
    }

//    public String getPacking(OrderedProduct product){
//        String type=product.getPackaging().getType();
//        type=type.toLowerCase();
//        if(type.contains("drum")){
//            return "Drum";
//        }
//        if(type.contains("bag")){
//            return "Bag";
//        }
//        if(type.contains("iso")){
//            return "ISO Tank";
//        }
//        if(type.contains("can")){
//            return "Drum";
//        }
//        return "Unit";
//    }

    public String getPacking(OrderedProduct product) {
        String type = product.getPackaging().getType();
        return type == null ? "Unit" : StringUtils.capitalize(type.toLowerCase());
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "lb";
            case GALLON -> "gal";
            case LITRE -> "L";
            case KILOLITRE -> "kL";
            case KILOGRAM -> "kg";
            default -> "";
        };
    }
}
