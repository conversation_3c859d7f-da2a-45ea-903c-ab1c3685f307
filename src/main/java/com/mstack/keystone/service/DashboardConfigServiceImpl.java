package com.mstack.keystone.service;

import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.repository.DashboardConfigRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.service.interfaces.ConfigService;
import java.util.List;
import java.util.Optional;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DashboardConfigServiceImpl implements ConfigService {

    @Autowired
    private DashboardConfigRepository dashboardRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Override
    public List<DashBoardConfig> getAllDashboardConfigs() {
        return dashboardRepository.findAll();
    }

    @Override
    public DashBoardConfig getDashboardConfigByConfigName(String configName) {
        return dashboardRepository.findByName(configName).get(0);
    }

    @Override
    public DashBoardConfig createDashboardConfig(DashBoardConfig dashboardConfig) {
        return dashboardRepository.save(dashboardConfig);
    }

    @Override
    @SneakyThrows
    public DashBoardConfig updateDashboardConfig(String id, DashBoardConfig updatedDashboardConfig) {
        return mongoQueries.updateEntity(dashboardRepository, id, updatedDashboardConfig);
    }

    @Override
    public void deleteDashboardConfig(String configName) {
        dashboardRepository.deleteById(String.valueOf(configName));
    }
}
