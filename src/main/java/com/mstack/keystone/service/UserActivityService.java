package com.mstack.keystone.service;

import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.model.repository.UserActivity;
import com.mstack.keystone.repository.UserActivityRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.user.EmployeeRepository;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.utils.CommonUtils;
import lombok.Synchronized;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.mstack.keystone.constants.AppConstants.ANALYTICS_GROUP_MAIL_ID;

@Service
public class UserActivityService {
    @Autowired
    UserActivityRepository userActivityRepository;
    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    EmployeeRepository employeeRepository;

    @Autowired
    AwsSesService awsSesService;
    @Autowired
    CommonUtils commonUtils;

    @Value("${spring.profiles.active}")
    String envType;


    @Synchronized
    public void updateUserActivity(String entityId, String entityType, LocalDate date) {
        if (entityType == null || entityType.isBlank() || entityId == null || entityId.isBlank()) return;
        UserActivity userActivity = userActivityRepository.findByEntityId(entityId);
        if (userActivity == null) {
            userActivity = new UserActivity();
            userActivity.setEntityId(entityId);
            userActivity.setEntityType(entityType);
            userActivity.setDeleted(false);
            userActivity.setCreatedBy(entityId);
            userActivity.setCreatedAt(new Date());
            userActivityRepository.save(userActivity);
        }
        if (userActivity.getActiveTimeMap() == null) userActivity.setActiveTimeMap(new HashMap<>());
        HashMap<LocalDate, Long> map = userActivity.getActiveTimeMap();
        map.put(date, map.getOrDefault(date, 0L) + 1);
        if (userActivity.getLastUpdatedAt() != null) {
            if (!commonUtils.isMoreThanOneMinute(userActivity.getLastUpdatedAt())) return;
        }
        userActivity.setLastUpdatedAt(new Date());
        userActivity.setLastUpdatedBy(entityId);
        userActivityRepository.save(userActivity);
    }

    // @Scheduled(cron = "${useractivty.cron.expression}")
    public void generateActivityReport() {
        List<Employee> employees = employeeRepository.findAll();
        employees = employees.stream().filter(employee -> !employee.isDeleted()).collect(Collectors.toList());
        employees.sort(new Comparator<Employee>() {
            @Override
            public int compare(Employee o1, Employee o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });
        LocalDate date = LocalDate.now(ZoneId.of("Asia/Kolkata"));
        List<HashMap<String, String>> activityMapList = new ArrayList<>();
        for (Employee employee : employees) {
            HashMap<String, String> activityMap = new HashMap<>();
            activityMap.put("employeeId", employee.getEmployeeId());
            activityMap.put("employeeName", employee.getName());
            UserActivity userActivity = userActivityRepository.findByEntityId(employee.getId());
            if (userActivity == null) {
                activityMap.put("lastActiveAt", "NA");
                activityMap.put("noOfMinutesActive", "NA");
            } else {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                if (userActivity.getLastUpdatedAt() == null) activityMap.put("lastActiveAt", "NA");
                else activityMap.put("lastActiveAt", simpleDateFormat.format(userActivity.getLastUpdatedAt()));
                activityMap.put("noOfMinutesActive", String.valueOf((userActivity.getActiveTimeMap().getOrDefault(date, 0L))));
            }
            activityMapList.add(activityMap);
        }
        String mailBody = getMailBody(activityMapList);
//        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM/yyyy");
        String sendersAddress = envType.equalsIgnoreCase("dev") || envType.equalsIgnoreCase("local") ? "<EMAIL>" : ANALYTICS_GROUP_MAIL_ID;
        try {
            awsSesService.sendEmail(
                    "<EMAIL>",
                    sendersAddress,
                    "User activiy report for " + date.format(DateTimeFormatter.ofPattern("dd-MM-yyyy")),
                    mailBody,
                    null
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }

    }

    private String getMailBody(List<HashMap<String, String>> activityMapList) {
        String mailBody =
                "<html><head><title>test-mail</title></head>" +
                        "<style>\n" +
                        "  table,\n" +
                        "  th,\n" +
                        "  td {\n" +
                        "    border: 1px solid black;\n" +
                        "  }\n" +
                        "</style>" +
                        "<body>  <h2>Employee Activity Report</h2>\n" +
                        "  <table>\n" +
                        "    <thead>\n" +
                        "      <tr>\n" +
                        "        <th>Employee ID</th>\n" +
                        "        <th>Employee Name</th>\n" +
                        "        <th>Last Active At</th>\n" +
                        "        <th>No of Minutes active</th>\n" +
                        "      </tr>\n" +
                        "    </thead>\n" +
                        "    <tbody>";
        for (HashMap<String, String> activity : activityMapList) {
            mailBody += getHtmlForActivity(activity);
        }
        mailBody += "  </tbody>\n" +
                "  </table> </body></html>\n";
        return mailBody;
    }

    private String getHtmlForActivity(HashMap<String, String> activity) {
        return " <tr>\n" +
                "        <td>" + activity.get("employeeId") + "</td>\n" +
                "        <td>" + activity.get("employeeName") + "</td>\n" +
                "        <td>" + activity.get("lastActiveAt") + "</td>\n" +
                "        <td>" + activity.get("noOfMinutesActive") + "</td>\n" +
                "      </tr>";
    }


}
