package com.mstack.keystone.service;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.repository.UserConfig;
import com.mstack.keystone.repository.UserConfigRepository;
import com.mstack.keystone.service.interfaces.IUserConfigService;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class UserConfigService implements IUserConfigService {
    @Autowired
    UserConfigRepository userConfigRepository;
    @Autowired
    RequestConfig requestConfig;

    public UserConfig getUserConfig(String userId){
        return userId.isEmpty()?null: userConfigRepository.findByUserId(userId);
    }

    @Override
    @SneakyThrows
    public UserConfig createUserConfig(UserConfig userConfig) {
        if(userConfig==null){
            throw new ServiceException("Failed to create , user config object cannot be null ", 400);
        }
        if(userConfigRepository.findByUserId(userConfig.getUserId())!=null)
            throw new ServiceException("Failed to create , user config with given user id:  "+userConfig.getUserId()+" already exists . Please try with different user id ",400);
        // generate object id
        userConfig.setId(new ObjectId().toString());
        userConfig.setCreatedAt(new Date());
        userConfig.setCreatedBy(requestConfig.getEntityId());
        return userConfigRepository.save(userConfig);
    }


    @Override
    @SneakyThrows
    public UserConfig updateUserConfig(String userId, UserConfig userConfig) {
        if (userConfig == null)
            throw new ServiceException("Failed to update , user config object cannot be null ", 400);
        UserConfig existingUserConfig = getUserConfig(userId);
        if (existingUserConfig == null) {
            throw new ServiceException("Failed to update , no object exists with user id: " + userId, 400);
        }
        existingUserConfig.setTabConfigs(userConfig.getTabConfigs());
        existingUserConfig.setUserId(userConfig.getUserId());
        existingUserConfig.setLastUpdatedAt(new Date());
        existingUserConfig.setLastUpdatedBy(requestConfig.getEntityId());
        return userConfigRepository.save(existingUserConfig);
    }

    @Override
    @SneakyThrows
    public void deleteUserConfig(String userId) {
        UserConfig existingGroup = getUserConfig(userId);
        if (existingGroup != null) {
            userConfigRepository.delete(existingGroup);
            System.out.println("permssions group with userId: " + userId + " sucesfully deleted");
        } else throw new ServiceException("Failed to delete , given userId " + userId + " doesnot exists", 400);

    }
}
