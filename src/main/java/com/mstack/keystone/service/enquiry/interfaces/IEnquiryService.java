package com.mstack.keystone.service.enquiry.interfaces;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.repository.ClientEnquiry;
import com.mstack.keystone.model.repository.EnquiryQuotationHistory;
import com.mstack.keystone.model.repository.enquiry.Enquiry;
import org.springframework.data.domain.Page;

import java.util.List;

public interface IEnquiryService {
    List<Enquiry> getAllNonDeletedEnquirys();

    EnquiryDto getEnquiryById(String id);

    Enquiry createEnquiry(Enquiry enquiry);

    Page<EnquiryDto> filterEnquiries(FilterRequest filterRequest);

    Enquiry updateEnquiry(String id, Enquiry enquiry);
    void deleteEnquiry(String id);
    EnquiryDto assignEnquiry(String emmployeeId,String enquiryId);
    EnquiryDto approveEnquiry(String id, Boolean status);

    EnquiryDto negotiate(String id, String negotiationRemark);
    EnquiryDto approveQuotation(String id,float quotedPrice);

    EnquiryDto rejectQuotation(String enquiryId, String reason, EnquiryStatus enquiryStatus);

    EnquiryDto createQuotation(SupplierQuotation supplierQuotation, String enquiryId);

    EnquiryQuotationHistory getQuotationHistory(String enquiryId);

    EnquiryDto customerUpdateQuotation(String enquiryId, EnquiryStatus enquiryStatus, String rejectionRemark);

    Page<CustomerEnquiry> findEnquiriesByCustomerId(String customerId, FilterRequest filterRequest);

    CustomerEnquiry findEnquiryByCustomerIdAndEnquiryId(String customerId, String enquiryId);

    void submitCustomerEnquiry(ClientEnquiry clientEnquiry) throws JsonProcessingException;
}
