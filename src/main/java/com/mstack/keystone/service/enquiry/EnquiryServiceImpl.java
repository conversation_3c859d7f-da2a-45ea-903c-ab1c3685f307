package com.mstack.keystone.service.enquiry;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mstack.keystone.config.EmailConfig;
import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.mapper.EnquiryToCustomerEnquiryMapper;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.repository.*;
import com.mstack.keystone.model.repository.enquiry.Enquiry;
import com.mstack.keystone.repository.ClientEnquiryRepository;
import com.mstack.keystone.repository.EnquiryQuotationRepository;
import com.mstack.keystone.repository.catalogue.ProductRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.enquiry.EnquiryRepository;
import com.mstack.keystone.repository.user.CustomerRepository;
import com.mstack.keystone.repository.user.SupplierRepository;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.cache.IRedisService;
import com.mstack.keystone.service.enquiry.interfaces.IEnquiryService;
import com.mstack.keystone.utils.CommonUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.mstack.keystone.constants.AppConstants.ANALYTICS_GROUP_MAIL_ID;
import static com.mstack.keystone.constants.AppConstants.INDIA_ZONE_ID;

@Service
public class EnquiryServiceImpl implements IEnquiryService {

    @Autowired
    private EnquiryRepository enquiryRepository;

    @Autowired
    CustomerRepository customerRepository;
    @Autowired
    SupplierRepository supplierRepository;

    @Autowired
    ProductRepository productRepository;
    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    RequestConfig requestConfig;
    @Autowired
    CommonUtils commonUtils;

    @Autowired
    EnquiryQuotationRepository enquiryQuotationRepository;

    @Autowired
    IRedisService redisService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    EmailConfig emailConfig;

    @Autowired
    ClientEnquiryRepository clientEnquiryRepository;

    @Autowired
    AwsSesService awsSesService;

    @Value("${spring.profiles.active}")
    String envType;


    @Override
    public List<Enquiry> getAllNonDeletedEnquirys() {
        return enquiryRepository.findByDeletedFalse();
    }

    @Override
    @SneakyThrows
    public EnquiryDto getEnquiryById(String id) {
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, id);
        EnquiryDto enquiryDto = createEnquiryDto(enquiry);
        enquiryDto.setQuotation(populateQuotationDetails(enquiryDto.getQuotation()));
        EnquiryQuotationHistory enquiryQuotationHistory = enquiryQuotationRepository.findByEnquiryId(enquiry.getId());
        if (enquiryQuotationHistory != null) {
            List<SupplierQuotation> quotations = enquiryQuotationHistory.getQuotationList();
            List<SupplierQuotation> populatedQuotations = new ArrayList<>();
            for (SupplierQuotation quotation : quotations) {
                populatedQuotations.add(populateQuotationDetails(quotation));
            }
            enquiryDto.setQuotationHistory(populatedQuotations);
        }

        return enquiryDto;
    }

    SupplierQuotation populateQuotationDetails(SupplierQuotation supplierQuotation) {
        if (supplierQuotation == null)
            return null;
        Supplier supplier = mongoQueries.getEntity(supplierRepository, supplierQuotation.getSupplierId());
        supplierQuotation.setSupplier(new SupplierProductDto(supplier, supplierQuotation.getProductId()));
        return supplierQuotation;
    }


    @Override
    @SneakyThrows
    public Enquiry createEnquiry(Enquiry enquiry) {
        if (requestConfig.getEntityType().equals("CUSTOMER")) {
            enquiry.setEnquiryStatus(EnquiryStatus.CREATED);
        } else {
            enquiry.setEnquiryStatus(EnquiryStatus.UNASSIGNED);
        }
        // set default priority to p2
        if (enquiry.getEnquiryPriority() == null)
            enquiry.setEnquiryPriority(EnquiryPriority.P2);
        enquiry.setEnquiryId(commonUtils.CreateID("ENQ", mongoQueries.getCount(enquiryRepository)));
        return mongoQueries.saveEntity(enquiryRepository, enquiry);
    }

    private List<String> getAllAccessibleCustomerIds() {
        Pageable pageable = PageRequest.of(0, 1000);
        return mongoQueries.findByFiltersAndSearch(null, null, pageable, Customer.class)
                .getContent()
                .stream()
                .map(Customer::getId)
                .toList();
    }

    @Override
    public Page<EnquiryDto> filterEnquiries(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        // TODO FIX GB REVIEW
        if (request.getFilters().get("customerId") != null) {
            request.getFilters().get("customerId").addAll(getAllAccessibleCustomerIds());
        } else request.getFilters().put("customerId", getAllAccessibleCustomerIds());
        Page<Enquiry> enquiries = mongoQueries.findByFiltersAndSearch(
                request.getFilters(),
                request.getSearch(),
                pageable,
                Enquiry.class
        );
        List<Enquiry> enquiryList = enquiries.getContent();
        List<EnquiryDto> enquiryDtoList = getEnquiryDtoList(enquiryList);
        return new PageImpl<>(enquiryDtoList, pageable, enquiries.getTotalElements());
    }

    @Override
    @SneakyThrows
    public Enquiry updateEnquiry(String id, Enquiry enquiry) {
        return mongoQueries.updateEntity(enquiryRepository, id, enquiry);
    }

    @Override
    public void deleteEnquiry(String id) {
        mongoQueries.softDeleteById(enquiryRepository, id, Enquiry.class);
    }

    @Override
    public EnquiryDto assignEnquiry(String employeeId, String enquiryId) {
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, enquiryId);
        enquiry.setAssignedTo(employeeId);
        enquiry.setEnquiryStatus(EnquiryStatus.ASSIGNED);
        enquiry.setAssignedTo(employeeId);
        mongoQueries.updateEntity(enquiryRepository, enquiryId, enquiry);
        return createEnquiryDto(enquiry);
    }

    @Override
    public EnquiryDto approveEnquiry(String enquiryId, Boolean status) {
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, enquiryId);
        if (status && enquiry.getEnquiryStatus().equals(EnquiryStatus.CREATED)) {
            enquiry.setEnquiryStatus(EnquiryStatus.UNASSIGNED);
        } else if (enquiry.getEnquiryStatus().equals(EnquiryStatus.CREATED)) {
            enquiry.setEnquiryStatus(EnquiryStatus.REJECTED);
        }
        mongoQueries.updateEntity(enquiryRepository, enquiryId, enquiry);
        return createEnquiryDto(enquiry);

    }

    @Override
    @SneakyThrows
    public EnquiryDto negotiate(String enquiryId, String negotiationRemark) {
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, enquiryId);
        if (enquiry.getEnquiryStatus().equals(EnquiryStatus.QUOTED)
                || enquiry.getEnquiryStatus().equals(EnquiryStatus.QUOTATION_APPROVED)) {
            // reject current quotation and set enquriy status to negotiate
            return rejectQuotation(enquiryId, negotiationRemark, EnquiryStatus.NEGOTIATE);
        } else {
            throw new ServiceException("Action Not possible", 500);
        }
//        return createEnquiryDto(enquiry);
    }


    @SneakyThrows
    @Override
    public EnquiryDto rejectQuotation(String enquiryId, String reason, EnquiryStatus enquiryStatus) {
        if (reason.isBlank()) {
            throw new ServiceException("Please provide a reason for qutoation rejection ", 400);
        }
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, enquiryId);
        // set current quotation status as  rejected
        enquiry.getQuotation().setSupplierQuotationStatus(SupplierQuotationStatus.REJECTED);
        // set rejection or negotiation resason
        if (enquiryStatus == EnquiryStatus.NEGOTIATE) {
            enquiry.setNegotiationRemark(reason);
        }
        enquiry.getQuotation().setRejectionReason(reason);
        // fetch the enquiry history
        EnquiryQuotationHistory enquiryQuotationHistory = enquiryQuotationRepository.findByEnquiryId(enquiryId);
        if (enquiryQuotationHistory == null) {
            // create new history if no history is present
            enquiryQuotationHistory = new EnquiryQuotationHistory(enquiryId);
            enquiryQuotationHistory.addQuotation(enquiry.getQuotation());
            mongoQueries.saveEntity(enquiryQuotationRepository, enquiryQuotationHistory);
        } else {
            // update the enquiry history
            enquiryQuotationHistory.addQuotation(enquiry.getQuotation());
            mongoQueries.updateEntity(enquiryQuotationRepository, enquiryQuotationHistory.getId(), enquiryQuotationHistory);
        }
        // set currenty enquiry quotation to null
        enquiry.setQuotation(null);
        enquiry.setLastUpdatedBy(requestConfig.getEntityId());
        enquiry.setLastUpdatedAt(new Date());
        enquiry.setEnquiryStatus(enquiryStatus);
        enquiryRepository.save(enquiry);
        // mongoQueries will not work because quotation is set to null
//        mongoQueries.updateEntity(enquiryRepository, enquiry.getId(), enquiry);
        return createEnquiryDto(enquiry);
    }


    @Override
    @SneakyThrows
    public EnquiryDto approveQuotation(String enquiryId, float quotedPrice) {
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, enquiryId);
        if (enquiry.getEnquiryStatus().equals(EnquiryStatus.QUOTED) ||
                enquiry.getEnquiryStatus().equals(EnquiryStatus.QUOTATION_APPROVED)) {
            // check if quoted price is valid or not
            if (!enquiry.getQuotation().isQuotedPriceValid(quotedPrice)) {
                throw new ServiceException("Quotation price is not valid ", 400);
            }
            enquiry.setEnquiryStatus(EnquiryStatus.QUOTATION_APPROVED);
            enquiry.getQuotation().setQuotedPrice(quotedPrice);
            enquiry.getQuotation().setSupplierQuotationStatus(SupplierQuotationStatus.QUOTED_TO_CUSTOMER);
        }
        mongoQueries.updateEntity(enquiryRepository, enquiryId, enquiry);
        return createEnquiryDto(enquiry);
    }

    @SneakyThrows
    private String getProductNameFromProductId(String productId) {
        Product product = productRepository.findById(productId).orElse(null);
        if (product == null) {
            throw new ServiceException("Product doesn't exists with given product Id", 400);
        }
        return product.getTradeName();
    }

    @SneakyThrows
    public EnquiryDto createQuotation(SupplierQuotation supplierQuotation, Enquiry enquiry) {
        if (enquiry.getQuotation() != null) {
            throw new ServiceException("Enquiry already has a qutotaion please reject it to add new quotation", 400);
        }
        supplierQuotation.setSupplierQuotationStatus(SupplierQuotationStatus.WAITING_FOR_APPROVAL);
        supplierQuotation.setProductName(getProductNameFromProductId(supplierQuotation.getProductId()));
        enquiry.setQuotation(supplierQuotation);
        enquiry.setEnquiryStatus(EnquiryStatus.QUOTED);
        mongoQueries.updateEntity(enquiryRepository, enquiry.getId(), enquiry);
        return createEnquiryDto(enquiry);
    }

    @Override
    @SneakyThrows
    public EnquiryDto createQuotation(SupplierQuotation supplierQuotation, String enquiryId) {
        Optional<Enquiry> enquiryDto = enquiryRepository.findById(enquiryId);
        if (enquiryDto.isEmpty()) {
            throw new ServiceException("Not exist", 500);
        }
        Enquiry enquiry = enquiryDto.get();
        return createQuotation(supplierQuotation, enquiry);
    }


    @Override
    public EnquiryQuotationHistory getQuotationHistory(String enquiryId) {
        return enquiryQuotationRepository.findByEnquiryId(enquiryId);
    }

    @Override
    @SneakyThrows
    public EnquiryDto customerUpdateQuotation(String enquiryId, EnquiryStatus enquiryStatus, String rejectionRemark) {
        Enquiry enquiry = mongoQueries.getEntity(enquiryRepository, enquiryId);

        if (!enquiry.getEnquiryStatus().equals(EnquiryStatus.QUOTATION_APPROVED)&&!enquiry.getEnquiryStatus().equals(EnquiryStatus.QUOTED)) {
            throw new ServiceException("Not possible", 500);
        }
        if (enquiryStatus.equals(EnquiryStatus.REJECTED)) {
            enquiry.setRejectionRemark(rejectionRemark);
        }
        if(enquiry.getRejectionRemark()!=null){
            enquiry.setRejectionRemark(rejectionRemark);
        }
        enquiry.setEnquiryStatus(enquiryStatus);
        mongoQueries.updateEntity(enquiryRepository, enquiryId, enquiry);
        return createEnquiryDto(enquiry);
    }

    public List<EnquiryStatus> mapCustomerEnquiryStatusToEnquiryStatus(CustomerEnquiryStatus customerEnquiryStatus) {
        return switch (customerEnquiryStatus) {
            case INTIATED -> List.of(EnquiryStatus.CREATED, EnquiryStatus.UNASSIGNED);
            case IN_PROGRESS -> List.of(EnquiryStatus.ASSIGNED, EnquiryStatus.QUOTED, EnquiryStatus.NEGOTIATE);
            case QUOTED -> List.of(EnquiryStatus.QUOTATION_APPROVED);
            case CLOSED -> List.of(EnquiryStatus.REJECTED, EnquiryStatus.ACCEPTED);
        };
    }

    @Override
    @SneakyThrows
    public Page<CustomerEnquiry> findEnquiriesByCustomerId(String customerId, FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize(), Sort.by(Sort.Direction.DESC, "createdAt"));
        if (request.getFilters() == null) {
            // add new filter
            request.setFilters(new HashMap<>());
        }
        request.getFilters().put("customerId", Collections.singletonList(customerId));
        // check if  request contains enquiryStatus filter
        if (request.getFilters().containsKey("enquiryStatus")) {
            List<CustomerEnquiryStatus> customerEnquiryStatus = request.getFilters().get("enquiryStatus").stream()
                    .map(CustomerEnquiryStatus::valueOf)
                    .toList();
            Set<EnquiryStatus> enquiryStatuses = new HashSet<>();
            customerEnquiryStatus.forEach(status -> enquiryStatuses.addAll(mapCustomerEnquiryStatusToEnquiryStatus(status)));
            // update enqury status filter with new mapped values
            request.getFilters().put("enquiryStatus", enquiryStatuses.stream().map(Enum::toString).collect(Collectors.toList()));
        }
        Page<Enquiry> enquiries = mongoQueries.findByFiltersAndSearch(request.getFilters(), request.getSearch(), pageable, Enquiry.class);
        return enquiries.map(EnquiryToCustomerEnquiryMapper::mapEnquiryToCustomerEnquiry);
    }

    @Override
    public CustomerEnquiry findEnquiryByCustomerIdAndEnquiryId(String customerId, String enquiryId) {
        Enquiry enquiry = enquiryRepository.findByEnquiryIdAndCustomerId(enquiryId, customerId);
        return enquiry == null ? null : EnquiryToCustomerEnquiryMapper.mapEnquiryToCustomerEnquiry(enquiry);
    }

    List<EnquiryDto> getEnquiryDtoList(List<Enquiry> enquiryList) {
        List<String> customerIds = enquiryList.stream()
                .map(Enquiry::getCustomerId)  // Assuming Enquiry class has a getCustomerId method
                .collect(Collectors.toList());
        List<Customer> customers = customerRepository.findAllById(customerIds);
        HashMap<String, Customer> customerHashMap = new HashMap<>();
        for (Customer customer : customers) {
            if (customer != null)
                customerHashMap.put(customer.getId(), customer);
        }
        List<EnquiryDto> list = new ArrayList<>();
        for (Enquiry enquiry : enquiryList) {
            if (customerHashMap.get(enquiry.getCustomerId()) != null)
                list.add(new EnquiryDto(enquiry, customerHashMap.get(enquiry.getCustomerId())));
        }
        return list;
    }

    EnquiryDto createEnquiryDto(Enquiry enquiry) {
        Customer customer = mongoQueries.getEntity(customerRepository, enquiry.getCustomerId());
        EnquiryQuotationHistory quotationHistory = enquiryQuotationRepository.findByEnquiryId(enquiry.getId());
        // populate quotation history
        if (quotationHistory != null)
            enquiry.setQuotationHistory(quotationHistory.getQuotationList());
        EnquiryDto enquiryDto = new EnquiryDto(enquiry, customer);
        return enquiryDto;
    }

    @Override
    public void submitCustomerEnquiry(ClientEnquiry clientEnquiry) throws JsonProcessingException {
        mongoQueries.saveEntity(clientEnquiryRepository, clientEnquiry);
        SendMailRequest sendMailRequest = new SendMailRequest();
        sendMailRequest.setFrom(emailConfig.getCustomerEnquirySenderMailId());
        sendMailRequest.setTo(emailConfig.getCustomerEnquiryRecieverMailList());
        sendMailRequest.setEmailType(emailConfig.getCustomerEnquiryMailTemplate());
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("name", clientEnquiry.getName());
        hashMap.put("companyName", clientEnquiry.getCompanyName());
        hashMap.put("email", clientEnquiry.getEmail());
        hashMap.put("mobile", clientEnquiry.getMobile());
        hashMap.put("requirement", clientEnquiry.getRequirement());
        sendMailRequest.setTemplateInfo(hashMap);
        redisService.enqueueMessage(objectMapper.writeValueAsString(sendMailRequest));
    }

    //    @Scheduled(cron = "${enquiry.cron.expression}")
    public void generateDailyClientEnquriyReport() {
        List<ClientEnquiry> enquiries = clientEnquiryRepository.findAll();
        enquiries = enquiries.stream()
                .filter(clientEnquiry -> !clientEnquiry.isDeleted())
                .filter(clientEnquiry -> {
                    LocalDate enquiryCreationDate = commonUtils.convertToLocalDateViaInstant(clientEnquiry.getCreatedAt());
                    LocalDate localDate = LocalDate.now(ZoneId.of(INDIA_ZONE_ID));
                    return enquiryCreationDate.isBefore(localDate);
                })
                .collect(Collectors.toList());
        String mailBody = generateMailBody(enquiries);
        String sendersAddress = envType.equalsIgnoreCase("dev") || envType.equalsIgnoreCase("local") ? "<EMAIL>" : ANALYTICS_GROUP_MAIL_ID;
        try {
            awsSesService.sendEmail(
                    "<EMAIL>",
                    sendersAddress,
                    "Daily Enquiry Report " + LocalDate.now(ZoneId.of(INDIA_ZONE_ID)),
                    mailBody,
                    null
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }

    }

    private String generateMailBody(List<ClientEnquiry> enquiries) {
        StringBuilder mailBodyBuilder = new StringBuilder();
        mailBodyBuilder.append("<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            font-family: Arial, sans-serif;\n" +
                "            background-color: #e6f2ff;\n" +
                "            margin: 0;\n" +
                "            padding: 20px;\n" +
                "            display: flex;\n" +
                "            justify-content: center;\n" +
                "            align-items: center;\n" +
                "            flex-direction: column;\n" +
                "        }\n" +
                "        .container {\n" +
                "            background-color: white;\n" +
                "            padding: 20px;\n" +
                "            border-radius: 10px;\n" +
                "            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n" +
                "            width: 80%;\n" +
                "            max-width: 1000px;\n" +
                "        }\n" +
                "        .logo {\n" +
                "            text-align: center;\n" +
                "            margin-bottom: 20px;\n" +
                "        }\n" +
                "        table {\n" +
                "            width: 100%;\n" +
                "            border-collapse: collapse;\n" +
                "            margin: 20px 0;\n" +
                "            font-size: 18px;\n" +
                "            text-align: left;\n" +
                "        }\n" +
                "        th, td {\n" +
                "            padding: 12px;\n" +
                "            border-bottom: 1px solid #ddd;\n" +
                "        }\n" +
                "        th {\n" +
                "            background-color: #283A8D;\n" +
                "            color: white;\n" +
                "        }\n" +
                "        tr:nth-child(even) {\n" +
                "            background-color: #f2f2f2;\n" +
                "        }\n" +
                "        tr:hover {\n" +
                "            background-color: #ddd;\n" +
                "        }\n" +
                "        caption {\n" +
                "            caption-side: top;\n" +
                "            margin-bottom: 10px;\n" +
                "            font-size: 24px;\n" +
                "            font-weight: bold;\n" +
                "        }\n" +
                "        h2 {\n" +
                "            text-align: center;\n" +
                "            color: rgb(42, 89, 141);\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "\n" +
                "<div class=\"container\">\n" +
                "\n" +
                "    <h2>Client Enquiries</h2>\n" +
                "\n" +
                "    <table> <tr>\n" +
                "            <th>Name</th>\n" +
                "            <th>Company Name</th>\n" +
                "            <th>Email</th>\n" +
                "            <th>Requirement</th>\n" +
                "        </tr>");
        enquiries.forEach(enquiry -> {
            mailBodyBuilder.append("<tr>")
                    .append("<td>").append(enquiry.getName()).append("</td>")
                    .append("<td>").append(enquiry.getCompanyName()).append("</td>")
                    .append("<td>").append(enquiry.getEmail()).append("</td>")
                    .append("<td>").append(enquiry.getRequirement()).append("</td>")
                    .append("</tr>");
        });
        mailBodyBuilder.append("</table>\n" +
                "</div>\n" +
                "\n" +
                "</body>\n" +
                "</html>\n");
        return mailBodyBuilder.toString();
    }
}
