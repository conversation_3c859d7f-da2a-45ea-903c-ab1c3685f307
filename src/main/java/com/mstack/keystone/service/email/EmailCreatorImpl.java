package com.mstack.keystone.service.email;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.enums.DeliveryStatus;
import com.mstack.keystone.model.repository.email.Email;
import com.mstack.keystone.model.repository.email.EmailTemplate;
import com.mstack.keystone.repository.email.EmailRepository;
import com.mstack.keystone.repository.email.EmailTemplateRepository;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.document.DocumentService;
import com.mstack.keystone.service.email.interfaces.IEmailCreator;
import com.mstack.keystone.utils.VelocityTemplateEvaluator;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

@Service
public class EmailCreatorImpl implements IEmailCreator {

    @Autowired
    AwsSesService awsSesService;

    @Autowired
    EmailTemplateRepository emailTemplateRepository;

    @Autowired
    EmailRepository emailRepository;
    @Autowired
    DocumentService documentService;

    @SneakyThrows
    @Override
    public void createEmail(Email email) {
        email.setDeliveryStatus(DeliveryStatus.CREATED);
        email = emailRepository.save(email);
        EmailTemplate template = emailTemplateRepository.getByEmailType(email.getEmailType());
        if (template == null) {
            throw new ServiceException("No template with given email type " + email.getEmailType(), 404);
        }
//        String templatePath = template.getBody();
        if (email.getFrom() == null || email.getFrom().isEmpty()) {
            email.setFrom(template.getFrom());
        }
        try {
//            String templateBody = getHtml(templatePath);
            String templateBody = getHtmlFromFile(documentService.downloadFileViaSignedUrl(template.getHtmlTemplate().getFileId(), template.getId()));
            String body = VelocityTemplateEvaluator.getInstance().evaluate(templateBody, email.getTemplateInfo());
            String subject = VelocityTemplateEvaluator
                .getInstance()
                .evaluate(template.getSubject(), email.getTemplateInfo());
            awsSesService.sendEmail(email.getFrom(), email.getTo(), subject, body, email.getAttachments());
            email.setDeliveryStatus(DeliveryStatus.SENT);
            emailRepository.save(email);
        } catch (Exception e) {
            System.err.println(e);
            email.setDeliveryStatus(DeliveryStatus.FAILED);
            emailRepository.save(email);
            throw new ServiceException("Something went wrong", 500);
        }
    }

    @SneakyThrows
    private String getHtml(String templatePath) {
        Resource resource = new ClassPathResource(templatePath);
        byte[] htmlBytes = Files.readAllBytes(resource.getFile().toPath());
        return new String(htmlBytes);
    }

    @SneakyThrows
    private String getHtmlFromFile(File file) {
        Path filePath = file.toPath();
        String fileContent = Files.readString(filePath);
        Files.delete(filePath);
        return fileContent;
    }
}
