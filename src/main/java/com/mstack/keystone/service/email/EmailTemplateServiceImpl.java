package com.mstack.keystone.service.email;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.repository.email.EmailTemplate;
import com.mstack.keystone.repository.email.EmailTemplateRepository;
import com.mstack.keystone.service.document.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;

@Service
public class EmailTemplateServiceImpl {

    @Autowired
    EmailTemplateRepository emailTemplateRepository;

    @Autowired
    DocumentService documentService;

    public EmailTemplate create(EmailTemplate emailTemplate, MultipartFile htmlFile) {
        HashMap<String, Object> meta = new HashMap<>();
        HashMap<String, String> extraDetails = new HashMap<>();
        extraDetails.put("fileName", htmlFile.getName());
        meta.put("meta", extraDetails);
        meta.put("category", "EmailHtmlTemplates");
        meta.put("name", htmlFile.getName());
        File htmlFileResponse = documentService.uploadFile(meta, htmlFile);
        emailTemplate.setHtmlTemplate(htmlFileResponse);
        emailTemplate = emailTemplateRepository.save(emailTemplate);
        return emailTemplate;
    }
}
