package com.mstack.keystone.service.criticalPath.interfaces;

import com.mstack.keystone.model.dto.ActivityDto;
import com.mstack.keystone.model.dto.DispatchOrderSummary;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

public interface ActivityService {
    List<Activity> getAllActivities();
    Activity getActivityById(String id);
    Activity createActivity(Activity activity);
    Activity updateActivity(String id, Activity activity);
    Page<ActivityDto> getActivitiesForUser();

    List<DispatchOrderSummary> getOrderSummaryForCustomer(String customerId);

    //    Activity updateActivityStatus(String id, ActivityStatus activityStatus);
    void deleteActivity(String id);
    void transferCriticalPath(String oldId,String newId,boolean fromInventory);
    Page<Activity> filterActivities(FilterRequest filterRequest);
    List<Activity> findPendingInvoiceUploadActivitiesByCustomer(String customerName);
}
