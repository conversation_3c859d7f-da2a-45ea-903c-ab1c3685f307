package com.mstack.keystone.service.criticalPath;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBookEntry;
import com.mstack.keystone.repository.ActivityRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.service.DashboardConfigServiceImpl;
import com.mstack.keystone.service.catalogue.IProductService;
import com.mstack.keystone.utils.CommonUtils;
import com.mstack.keystone.utils.EventManager;
import com.mstack.keystone.utils.InfixExpressionCalculator;
import com.mstack.keystone.utils.VelocityTemplateEvaluator;
import com.mstack.keystone.model.repository.order.OrderBook;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Service;

@Service
public class CriticalPathService {

    private static final Logger log = LoggerFactory.getLogger(CriticalPathService.class);
    @Autowired
    RequestConfig requestConfig;
    @Autowired
    ActivityRepository activityRepository;
    @Autowired
    DashboardConfigServiceImpl configService;
    @Autowired
    IProductService productService;
    @Autowired
    MongoQueries mongoQueries;
    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    InfixExpressionCalculator calculator;
    @Autowired
    EventManager eventManager;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    DashboardConfigServiceImpl dashboardConfigService;
    private static final String PAYMENT_TO_SUPPLIER = "Payment to supplier";
    public void generateCriticalPath(CriticalPath criticalPath) {
        List<Activity> activities = criticalPath.getActivities();
        for (Activity activity : activities) {
            activity.setEntityId(criticalPath.getEntityId());
            activity.setEntityType(criticalPath.getEntityType());
            activityRepository.save(activity);
        }
    }

    public List<Activity> getCriticalPath(List<String> ids) {
        List<Activity> activities = activityRepository.findByEntityIds(ids);
        return activities;
    }

    @SneakyThrows
    public CriticalPathTemplate getCriticalPathForm(String name) {
        CriticalPathTemplate criticalPathTemplate = getCriticalPathTemplate(name);
        criticalPathTemplate.setOrderLevelTasks(null);
        criticalPathTemplate.setProductLevelTasks(null);
        return criticalPathTemplate;
    }

    public List<Activity> getCriticalPath(String id) {
        int pageNumber = 0;
        int pageSize = 1000;
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        Sort sort = Sort.by(Sort.Order.asc("dueDate"));
        Pageable pageableWithSorting = PageRequest.of(pageNumber, pageSize, sort);
        List<Activity> activities = activityRepository.findByEntityId(id,pageableWithSorting);
        if(activities==null||activities.size()==0){
            return new ArrayList<>();
        }
        return getSortedActivities(activities);
    }

    private List<Activity> getSortedActivities(List<Activity> activities){
        if(activities==null||activities.size()==0){
            return new ArrayList<>();
        }
        List<Activity> sortedActivites=new ArrayList<>();
        HashMap<String,List<Activity>> map=commonUtils.getCategoryWiseActivities(activities);
        DashBoardConfig dashBoardConfig=dashboardConfigService.getDashboardConfigByConfigName("taskCategories");
        List<Map<String,Object>> categories=dashBoardConfig.getValue();
        for (Map<String,Object> category:categories) {
            String key=category.get("key").toString();
            List<Activity> categoryActivityList=map.get(key);
            if(categoryActivityList!=null&&categoryActivityList.size()>0){
                Collections.sort(categoryActivityList, Comparator.comparing(Activity::getDueDate));
                sortedActivites.addAll(categoryActivityList);
            }
        }
        return sortedActivites;
    }
    @SneakyThrows
    public CriticalPathTemplate getCriticalPathTemplate(String name) {
        //        String fileName = "";
        //        if (name.equals("DefaultCriticalPath")) {
        //            fileName = "templates/DefaultCustomerCriticalPath.json";
        //        }
        //        Resource resource = new ClassPathResource(fileName);
        //        byte[] htmlBytes = Files.readAllBytes(resource.getFile().toPath());
        //        String criticalPathTemplateString = new String(htmlBytes);
        DashBoardConfig config = configService.getDashboardConfigByConfigName(name);
        CriticalPathTemplate criticalPathTemplate = objectMapper.readValue(
            objectMapper.writeValueAsString(config.getValue().get(0)),
            CriticalPathTemplate.class
        );
        return criticalPathTemplate;
    }

    @SneakyThrows
    public void updateCriticalPath(){
        DashBoardConfig config = configService.getDashboardConfigByConfigName("DefaultCriticalPath");
        CriticalPathTemplate criticalPathTemplate = objectMapper.readValue(
                objectMapper.writeValueAsString(config.getValue().get(0)),
                CriticalPathTemplate.class
        );
        HashMap<String,Object> taskConfig=new HashMap<>();

        HashMap<String,Object> eventConfig=new HashMap<>();
        for (Activity activity:criticalPathTemplate.getOrderLevelTasks()) {
            taskConfig.put(activity.getName(),activity.getTaskConfig());
            eventConfig.put(activity.getName(),activity.getEvents());
        }
        for (Activity activity:criticalPathTemplate.getProductLevelTasks()) {
            taskConfig.put(activity.getName(),activity.getTaskConfig());
            eventConfig.put(activity.getName(),activity.getEvents());
        }
        List<Activity> activities = activityRepository.findIncompleteActivities("");
        for (Activity activity:activities) {
            if(taskConfig.getOrDefault(activity.getName(),null)!=null){
                activity.setTaskConfig((List<HashMap<String, Object>>) taskConfig.get(activity.getName()));
                activityRepository.save(activity);
            }
            if(eventConfig.getOrDefault(activity.getName(),null)!=null){
                activity.setEvents((List<Event>) eventConfig.get(activity.getName()));
                activityRepository.save(activity);
            }
        }
    }

    @SneakyThrows
    public List<Activity> generateCriticalPath(
        String entityId,
        EntityType entityType,
        String name,
        HashMap<String, Object> context
    ) {
        CriticalPathTemplate criticalPathTemplate = getCriticalPathTemplate(name);
        List<Activity> activities = new ArrayList<>();
        if(!entityType.equals(EntityType.CUSTOMER_ORDER)){
            throw new ServiceException("Api not supported",500);
        }
        CustomerOrder order=mongoQueries.getEntity(customerOrderRepository,entityId);
        updateCriticalPathType(name, order);
        if (name.equals("ddpCriticalPath")) {
            updateDeliveryDate(getDateFromString((String) context.get("deliveryDate")), order);
        }
        for (Activity activity : criticalPathTemplate.getOrderLevelTasks()) {
            activity = new Activity(activity);
            activity.setEntityType(entityType);
            activity.setEntityId(entityId);
            activity.setSecondaryId(order.getOrderId());
            activity = createActivity(activity, context);
            activities.add(activity);
        }
        if (context.get("products") != null) {
            List<HashMap<String, Object>> productList = (List<HashMap<String, Object>>) context.get("products");
            for (HashMap<String, Object> map : productList) {
                for (String key : map.keySet()) {
                    context.put(key, map.get(key));
                }
                HashMap<String, Object> additionalData = new HashMap<>();
                additionalData.put("productId", context.get("productId").toString());
                additionalData.put("id", context.get("id").toString());
                Product currentProduct = productService.getProductById(context.get("productId").toString());
                context.put("name", currentProduct.getTradeName());
                for (Activity activity : criticalPathTemplate.getProductLevelTasks()) {
                    activity = new Activity(activity);
                    activity.setAdditionalData(additionalData);
                    activity.setEntityType(entityType);
                    activity.setEntityId(entityId);
                    activity.setSecondaryId(order.getOrderId());
                    activity = createActivity(activity, context);
                    activities.add(activity);
                }
            }
        }
        Thread myThread = new Thread(() -> {
            executeActivities(activities);
        });

        myThread.start();
        return getSortedActivities(activities);
    }

    private void updateCriticalPathType(String name, CustomerOrder order) {
        if (order.getMeta() == null) {
            order.setMeta(new HashMap<>());
        }
        order.getMeta().put("criticalPathType", name);
        mongoQueries.updateEntity(customerOrderRepository, order.getId(), order);
    }

    private void updateDeliveryDate(Date date, CustomerOrder order) {
        order.setDeliveryDate(date);
        mongoQueries.updateEntity(customerOrderRepository, order.getId(), order);
    }
    private void executeActivities(List<Activity> activities){
        for (Activity activity :activities) {
            List<Event> events=activity.getEvents();
            for (Event event:events) {
                eventManager.executeEvent(event,activity);
            }
        }
    }

    //    private void addsupplierDispatchId(CustomerOrder order, String id) {
    //        List<String> supplierDispatchIds = order.getSupplierDispatchIds();
    //        if (supplierDispatchIds == null) {
    //            order.setSupplierDispatchIds(new ArrayList<>());
    //            supplierDispatchIds = order.getSupplierDispatchIds();
    //        }
    //        supplierDispatchIds.add(id);
    //    }

    private Activity createActivity(Activity activity, HashMap<String, Object> context) {
        String name = VelocityTemplateEvaluator.getInstance().evaluate(activity.getName(), context);
        activity.setName(name);
        String description = VelocityTemplateEvaluator.getInstance().evaluate(activity.getName(), context);
        activity.setName(description);
        activity.setStatus(ActivityStatus.TODO);
        String dateString = activity.getDueDateLogic();
        dateString = VelocityTemplateEvaluator.getInstance().evaluate(dateString, context);
        Date deliveryDate = getDateFromString(context.get("deliveryDate").toString());
        if (dateString.isEmpty() || dateString.isBlank()) {
            activity.setDueDate(new Date());
        } else {
            int days = calculator.calculateInfixExpression(dateString);
            Date dueDate = getDueDate(deliveryDate, days);
            activity.setDueDate(dueDate);
        }
        List<HashMap<String,Object>> taskConfig=activity.getTaskConfig();
        if(taskConfig!=null){
            for (HashMap<String,Object> map:taskConfig) {
                if(map.get("inputType").toString().equals("file")){
                    map.put("category","DispatchOrders");
                    HashMap<String, String> meta=new HashMap<>();
                    meta.put("entityId",activity.getEntityId());
                    meta.put("documentType", (String) map.getOrDefault("key","Others"));
                    map.put("meta",meta);
                }
            }
        }
        activity.setCreatedBy(requestConfig.getEntityId());
        activity.setAssignedTo(requestConfig.getEntityId());
        activity.setAssignedBy(requestConfig.getEntityId());
        activity.setCreatedAt(new Date());
        activity = activityRepository.save(activity);
        return activity;
    }

    private Date getDateFromString(String dateString) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        Date date = null;
        try {
            date = sdf.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    private Date getDueDate(Date deliveryDate, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(deliveryDate);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        Date newDate = calendar.getTime();
        return newDate;
    }

    @SneakyThrows
    public List<Activity> updateCriticalPath(String entityId, EntityType entityType, String name, HashMap<String, Object> context) {
        CriticalPathTemplate criticalPathTemplate = getCriticalPathTemplate(name);
        if (name.equals("ddpCriticalPath")) {
            CustomerOrder order = mongoQueries.getEntity(customerOrderRepository, entityId);

            updateDeliveryDate(getDateFromString((String) context.get("deliveryDate")), order);
        }
        // get order level activites names
        HashSet<String> orderLevelActivitiesNames = new HashSet<>();
        criticalPathTemplate.getOrderLevelTasks().forEach(activity -> {
            orderLevelActivitiesNames.add(activity.getName());
        });
        List<Activity> activities = activityRepository.findIncompleteActivities(entityId);
        List<Activity> orderLevelActivites = new ArrayList<>();
        List<Activity> productLevelActivites = new ArrayList<>();
        activities.forEach(activity -> {
            if (orderLevelActivitiesNames.contains(activity.getName())) {
                orderLevelActivites.add(activity);
            } else productLevelActivites.add(activity);
        });
        // Update Order Level activities
        updateActivities(orderLevelActivites, context);
        if (context.get("products") != null) {
            // Update product level activites
            List<HashMap<String, Object>> productList = (List<HashMap<String, Object>>) context.get("products");
            for (HashMap<String, Object> map : productList) {
                for (String key : map.keySet()) {
                    context.put(key, map.get(key));
                }
                Product currentProduct = productService.getProductById(context.get("productId").toString());
                context.put("name", currentProduct.getTradeName());
                HashSet<String> productLevelActivitiesNames = new HashSet<>();
                criticalPathTemplate.getProductLevelTasks().forEach(activity -> {
                    productLevelActivitiesNames.add(VelocityTemplateEvaluator.getInstance().evaluate(activity.getName(), context));
                });
                // filter out current product activities
                List<Activity> currentProductActivites = productLevelActivites.stream()
                        .filter(activity -> productLevelActivitiesNames.contains(activity.getName()))
                        .toList();
                // update current product activites
                updateActivities(currentProductActivites, context);
            }
        }
        //        Thread myThread = new Thread(() -> {
        executeActivities(activities);
//        });

//        myThread.start();

        return getSortedActivities(activityRepository.findByEntityId(entityId));
    }

    private void updateActivities(List<Activity> activities, HashMap<String, Object> context) {
        for (Activity activity : activities) {
            String dateString = activity.getDueDateLogic();
            dateString = VelocityTemplateEvaluator.getInstance().evaluate(dateString, context);
            Date deliveryDate = getDateFromString(context.get("deliveryDate").toString());
            if (dateString.isEmpty() || dateString.isBlank()) {
                activity.setDueDate(new Date());
            } else {
                int days = calculator.calculateInfixExpression(dateString);
                Date dueDate = getDueDate(deliveryDate, days);
                activity.setDueDate(dueDate);
            }
            activity.setLastUpdatedAt(new Date());
            activity.setLastUpdatedBy(requestConfig.getEntityId());
            activityRepository.save(activity);
        }
    }

    private Activity createOrderActivities(Activity activity, ActivitiesRequest activitiesRequest, OrderBook orderBook ) {
        if (activity.getTaskId().equals("8") && OrderType.SAMPLE.toString().equals(activitiesRequest.getOrderType()) && !orderBook.getProducts().get(0).isTestingRequired()) {
            return null;
        }
        if (activity.getCategory().equals(activitiesRequest.getCategoryName())) {
            activity.setEntityId(activitiesRequest.getEntityId());
        }
        Map<String, Date> dueDateFields = activitiesRequest.getDueDateFields();
        String dueDateLogic = activity.getDueDateLogic();
        if (dueDateLogic != null) {
            Date calculatedDate = calculateDueDate(dueDateLogic, dueDateFields);
            if (calculatedDate != null) {
                activity.setDueDate(calculatedDate);
            }
        }
        activity.setStatus(ActivityStatus.TODO);
        activity.setEntityType(EntityType.valueOf(activity.getCategory()));
        activity.setOrderId(activitiesRequest.getOrderId());
        activity.setCreatedBy(requestConfig.getEntityId());
        activity.setCreatedAt(new Date());
        activity.setSecondaryId(activitiesRequest.getSecondaryId());
        activity.setOrderType(activitiesRequest.getOrderType());
        activity.setCustomerName(activitiesRequest.getCustomerName());
        activity.setProductName(activitiesRequest.getProductName());
        return activityRepository.save(activity);

    }

    /**
     * Updates sourcing tasks to include category-based names.
     */
    @Autowired
    private OrderBookRepository orderBookRepository;
    private void updateSourcingTasks(List<Activity> savedTasks,String category) {
        // 1. Identify sourcing tasks
        List<Activity> sourcingTasks = savedTasks.stream()
                .filter(task -> "sourcing".equals(task.getGroup()))
                .toList();

        System.out.println(category);

        if (sourcingTasks.isEmpty()) return;
        String sourcingCategory = Objects.nonNull(category) ? category : "default";

        // 3. Update sourcing tasks with the new category-based group
        for (Activity task : sourcingTasks) {
            task.setGroup(sourcingCategory + "Sourcing");
            task.setAssignedTo(requestConfig.getEntityId());
        }

        // 4. Save the modified tasks
        activityRepository.saveAll(sourcingTasks);
    }

    public List<Activity> generateAndSaveTasks(ActivitiesRequest activitiesRequest) {
        // 1. Generate tasks from the selected template
        CriticalPathTemplate template = getCriticalPathTemplate(
            activitiesRequest.getTemplateName());
        boolean isSampleFlow = activitiesRequest.getTemplateName().equals("sampleCdoFlow");
        OrderBook orderBook = orderBookRepository.findByOrderBookId(activitiesRequest.getOrderId())
            .stream().findFirst().orElse(null);
        String customerName = Objects.nonNull(orderBook) ? orderBook.getCustomer().getName()
            : activitiesRequest.getCustomerName();
        String productName =
            Objects.nonNull(orderBook) ? Optional.ofNullable(orderBook.getProducts().get(0)).map(
                OrderBookEntry::getProduct).map(Product::getTradeName).orElse(null)
                : activitiesRequest.getProductName();
        activitiesRequest.setCustomerName(customerName);
        activitiesRequest.setProductName(productName);
        List<Activity> generatedTasks = template.getOrderLevelTasks().stream()
            .map(activity -> createOrderActivities(activity, activitiesRequest, orderBook))
            .filter(Objects::nonNull)
            .toList();

        System.out.print(generatedTasks);

        // 2. Save all tasks to MongoDB to get their generated Mongo IDs
        List<Activity> savedTasks = activityRepository.saveAll(generatedTasks);

        // 3. Update sourcing tasks based on category
        updateSourcingTasks(savedTasks, activitiesRequest.getCategory());

        // 3. Build maps: taskId -> Mongo ID and taskId -> Task Name for reference
        Map<String, String> taskIdToMongoIdMap = savedTasks.stream()
                .filter(task -> task.getTaskId() != null)
                .collect(Collectors.toMap(Activity::getTaskId, Activity::getId));

        Map<String, String> taskIdToNameMap = savedTasks.stream()
                .filter(task -> task.getTaskId() != null)
                .collect(Collectors.toMap(Activity::getTaskId, Activity::getName));

        // 4. Define task dependencies using taskIds (logical sequence mapping)
        Map<String, List<String>> prerequisiteMapping;
        if (isSampleFlow) {
            OrderBookEntry orderBookEntry = orderBook.getProducts().stream().findFirst()
                .orElse(null);
            boolean isTestingRequired =
                orderBookEntry != null && orderBookEntry.isTestingRequired();

            // Task 11 dependencies
            List<String> task11Dependencies = isTestingRequired ?
                List.of("10", "9", "8") : List.of("10", "9");

            // Task 15 dependencies
            List<String> task15Dependencies =
                (isTestingRequired && !orderBookEntry.isDispatchWithResults()) ?
                    List.of("14", "8") : List.of("14");

            prerequisiteMapping = Map.ofEntries(
                Map.entry("11", task11Dependencies),
                Map.entry("15", task15Dependencies)
            );
        } else {
            prerequisiteMapping = Map.ofEntries(
                Map.entry("4", List.of("3")),
                Map.entry("12", List.of("11", "10", "9"))
            );
        }

        System.out.print(prerequisiteMapping);
        // 5. Assign prerequisite tasks dynamically based on mapping
        List<Activity> tasksToUpdate = new ArrayList<>();
        for (Activity task : savedTasks) {
            String currentTaskId = task.getTaskId(); // Logical task ID (like "1", "2", "3")
            if (prerequisiteMapping.containsKey(currentTaskId)) {
                // Build list of PrerequisiteTask objects with Mongo ID and name
                List<PrerequisiteTask> prerequisiteTasks = prerequisiteMapping.get(currentTaskId).stream()
                        .map(prereqTaskId -> {
                            String mongoId = taskIdToMongoIdMap.get(prereqTaskId);
                            String name = taskIdToNameMap.get(prereqTaskId);
                            if (mongoId != null && name != null) {
                                return new PrerequisiteTask(mongoId, name); // Mapping both ID and name
                            } else {
                                // Optional: log warning if mapping is missing
                                // log.warn("Missing prerequisite mapping for taskId: {}", prereqTaskId);
                                return null; // Skip if missing data
                            }
                        })
                        .filter(Objects::nonNull) // Filter out any missing/mismatched prerequisites
                        .toList();

                // If valid prerequisites found, assign to task
                if (!prerequisiteTasks.isEmpty()) {
                    task.setPrerequisiteTasks(prerequisiteTasks);
                    tasksToUpdate.add(task);
                }
            }
        }

        // 6. Update and save tasks with assigned prerequisites
        if (!tasksToUpdate.isEmpty()) {
            activityRepository.saveAll(tasksToUpdate);
        }

        // 7. Return the saved tasks (with and without prerequisites)
        return savedTasks;
    }


    public List<Activity> createOrUpdateTask(ActivitiesRequest activitiesRequest) {
        String orderId = activitiesRequest.getOrderId();
        String categoryName = activitiesRequest.getCategoryName();
        Map<String, Date> dueDateFields = activitiesRequest.getDueDateFields();

        // Fetch existing tasks for the given order
        List<Activity> existingActivities = activityRepository.findByOrderId(orderId);

        //handling for PO not required tasks
        if(Objects.nonNull(activitiesRequest.getPoGenrationRequired()) && !activitiesRequest.getPoGenrationRequired()){
            existingActivities.stream()
                    .filter(activity -> PAYMENT_TO_SUPPLIER.equals(activity.getName()))
                    .findFirst()
                    .ifPresent(activity -> activity.setDeleted(true));
        }

        // Filter existing activities for the given category
        List<Activity> existingCategoryActivities = existingActivities.stream()
                .filter(activity -> categoryName.equals(activity.getCategory()))
                .toList();

        // If tasks already exist for this category -> Update due dates and entityId
        if (!existingCategoryActivities.isEmpty()) {
            existingActivities.forEach(activity -> {
                Optional.ofNullable(activity.getDueDateLogic())
                        .map(dueDateLogic -> calculateDueDate(dueDateLogic, dueDateFields))
                        .ifPresent(activity::setDueDate);

                if (categoryName.equals(activity.getCategory())) {
                    activity.setEntityId(activitiesRequest.getEntityId());
                }
            });

            // Save and return updated activities
            return activityRepository.saveAll(existingActivities);
        } else {
            // If no existing tasks, generate and save tasks (handling prerequisites too)
            return generateAndSaveTasks(activitiesRequest);
        }
    }

    private Date calculateDueDate(String dueDateLogic, Map<String, Date> dueDateFields) {

        String regex = "([a-zA-Z_]+)([+-])(\\d+)?";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(dueDateLogic);

        if (matcher.matches()) {
            String dateKey = matcher.group(1);   // First part: due date field
            String operator = matcher.group(2);  // Second part: + or -
            String daysStr = matcher.group(3);   // Third part: number of days
            Date baseDate = dueDateFields.get(dateKey);
            if (baseDate == null) {
                return null;  // Return null if the base date is missing
            }

            int days = (daysStr != null) ? Integer.parseInt(daysStr) : 0;
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(baseDate);

            if (operator.equals("+")) {
                calendar.add(Calendar.DAY_OF_MONTH, days);
            } else if (operator.equals("-")) {
                calendar.add(Calendar.DAY_OF_MONTH, -days);
            }

            return calendar.getTime();
        }

        // If the logic is just a key with no +/- modification, use the base date directly
        return dueDateFields.get(dueDateLogic);
    }
}
