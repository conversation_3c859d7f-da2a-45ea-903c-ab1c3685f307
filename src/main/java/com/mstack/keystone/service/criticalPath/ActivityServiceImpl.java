package com.mstack.keystone.service.criticalPath;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.Audit;
import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import com.mstack.keystone.repository.ActivityRepository;
import com.mstack.keystone.repository.AuditRepository;
import com.mstack.keystone.repository.DashboardConfigRepository;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.service.criticalPath.interfaces.ActivityService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.utils.EventManager;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.mockito.internal.matchers.Or;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

@Service
public class ActivityServiceImpl implements ActivityService {

    private final ActivityRepository activityRepository;

    @Autowired
    MongoQueries mongoQueries;

    @Autowired
    EventManager eventManager;

    @Autowired
    RequestConfig requestConfig;

    @Autowired
    CustomerOrderService customerOrderService;

    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    OrderBookRepository orderRepository;

    @Autowired
    DashboardConfigRepository dashboardConfigRepository;

    @Autowired
    AuditRepository auditRepository;

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    public ActivityServiceImpl(ActivityRepository activityRepository) {
        this.activityRepository = activityRepository;
    }

    @Override
    public List<Activity> getAllActivities() {
        return activityRepository.findAll();
    }

    @Override
    @SneakyThrows
    public Activity getActivityById(String id) {
        return mongoQueries.getEntity(activityRepository, id);
    }

    @Override
    public Activity createActivity(Activity activity) {
        activity.setAssignedBy(requestConfig.getEntityId());
        activity.setCreatedAt(new Date());
        activity.setCreatedBy(requestConfig.getEntityId());
        return activityRepository.save(activity);
    }

    @Override
    public Activity updateActivity(String id, Activity updatedActivity) {
        Activity activity = getActivityById(id);
        if (activity != null) {
            // IF STATUS IS NOT SAME UPDATE STATUS
            activity.setDisplayDate(updatedActivity.getDisplayDate());
            activity.setTaskConfig(updatedActivity.getTaskConfig());
//            if (!activity.getStatus().equals(updatedActivity.getStatus())) {
//                updateActivityStatus(activity, updatedActivity.getStatus());
//            }
            // UPDATE REMAINING FIELDS
            activity.setLastUpdatedAt(new Date());
            activity.setLastUpdatedBy(requestConfig.getEntityId());
            activity.setAssignedTo(updatedActivity.getAssignedTo());
            activity.setAssignedBy(updatedActivity.getAssignedBy());
            activity.setDueDate(updatedActivity.getDueDate());
            activity.setRemarks(updatedActivity.getRemarks());
            activity.setDependencyResolved(updatedActivity.isDependencyResolved());
            activity.setTaskId(updatedActivity.getTaskId());
            activity.setDependentOn(updatedActivity.getDependentOn());
            activity.setCustomerName(updatedActivity.getCustomerName());
            activity.setProductName(updatedActivity.getProductName());

            // ✅ Add these two lines
            activity.setRemarksGiven(updatedActivity.getRemarksGiven());
            activity.setRemarksReceived(updatedActivity.getRemarksReceived());

            updateActivityStatus(activity, updatedActivity.getStatus());
            if (updatedActivity.getStatus().equals(ActivityStatus.COMPLETED)) {
                activity.setTaskCompletedOn(new Date());
            }
            Audit audit = new Audit().builder()
                .entity(activity)
                .entityType("Activity")
                .build();
            mongoQueries.saveEntity(auditRepository, audit);
            Activity savedActivity = activityRepository.save(activity);
            if(updatedActivity.getStatus().equals(ActivityStatus.COMPLETED) && Objects.nonNull(updatedActivity.getTaskId())){
                List<Activity> existingActivities = activityRepository.findByOrderIdAndDependentOn(
                    updatedActivity.getOrderId(), updatedActivity.getTaskId());
                for( Activity existingActivity: existingActivities){

                        existingActivity.setDependencyResolved(true);

                }
                activityRepository.saveAll(existingActivities);
            }
            return savedActivity;
        }
        return null;
    }

    @Override
    @SneakyThrows
    public Page<ActivityDto> getActivitiesForUser() {
        if(requestConfig.getEntityType()==null){
            throw new ServiceException("Wrong user",403);
        }
        String employeeId=requestConfig.getEntityId();
        int pageNumber = 0;
        int pageSize = 100;
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        Sort sort = Sort.by(Sort.Order.asc("dueDate"));
        Pageable pageableWithSorting = PageRequest.of(pageNumber, pageSize, sort);
        Page<Activity> activities=activityRepository.findActivitiesForUser(employeeId,pageableWithSorting);
        List<String> distinctEntityIds = activities.getContent()
                .stream()
                .map(Activity::getEntityId)
                .distinct()
                .collect(Collectors.toList());
        List<CustomerOrder> orders=customerOrderRepository.findAllById(distinctEntityIds);
        orders = orders.stream().filter(customerOrder -> !(customerOrder.isDeleted())).collect(Collectors.toList());
        HashMap<String, CustomerOrder> map = orders.stream().collect(Collectors.toMap(CustomerOrder::getId, order -> order, (existing, replacement) -> replacement, HashMap::new));
        List<Activity> activityList=activities.getContent();
        List<ActivityDto> activityDtoList=new ArrayList<>();
        HashMap<String, OrderBook> orderBookMap=new HashMap();
        for(Activity activity:activityList){
            String entityId=activity.getEntityId();
            CustomerOrder order=map.get(entityId);
            if (order == null) continue;
            if(orderBookMap.get(entityId)==null){
                OrderBook orderBook=orderRepository.findByPONumberAndCustomerId(order.getPurchaseOrderNumber(),new ObjectId(order.getCustomer().getId()));
                orderBookMap.put(entityId,orderBook);
            }
            ActivityDto activityDto=new ActivityDto(activity,order,orderBookMap.get(entityId));
            activityDtoList.add(activityDto);
        }
        return new PageImpl<>(activityDtoList, pageable, activityList.size());
    }

    @Override
    public List<DispatchOrderSummary> getOrderSummaryForCustomer(String customerId) {
        List<DispatchOrderSummary> summary=new ArrayList<>();
        List<CustomerOrder> orderList=customerOrderService.getIncompletedOrdersForCustomer(customerId);
        for (CustomerOrder customerOrder:orderList) {
            List<Activity> activityList=activityRepository.findByEntityId(customerOrder.getId());
            if(activityList==null||activityList.size()==0){
                continue;
            }
            HashMap<String,List<Activity>> categoryActivityMap=getCategoryWiseActivities(activityList);
            HashMap<String,String> statuses=getCategoryWiseStatus(categoryActivityMap);
            DispatchOrderSummary dispatchOrderSummary=new DispatchOrderSummary();
            dispatchOrderSummary.setOrderId(customerOrder.getOrderId());
            dispatchOrderSummary.setEntityId(customerOrder.getId());
            dispatchOrderSummary.setStatusMap(statuses);
            OrderBook orderBook=orderRepository.findByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(),new ObjectId(customerOrder.getCustomer().getId()));
            dispatchOrderSummary.setOrderBookId(orderBook.getId());
            dispatchOrderSummary.setIncoterms(customerOrder.getIncoterms().getType()+"-"+customerOrder.getIncoterms().getCountry());
            dispatchOrderSummary.setProducts(getProductNameList(customerOrder));
            summary.add(dispatchOrderSummary);
        }
        return summary;
    }
    HashMap<String,String> getCategoryKeyMap(DashBoardConfig dashBoardConfig)
    {
        HashMap<String,String> output=new HashMap<>();
        List<Map<String, Object>> valueList=dashBoardConfig.getValue();
        for (Map map:valueList) {
            output.put(map.get("displayName").toString(),map.get("key").toString());
        }
        return output;
    }

    String getProductNameList(CustomerOrder customerOrder){
        String productNames="";
        for (OrderedProduct product:customerOrder.getProducts()) {
            productNames+=product.getProduct().getTradeName()+",";
        }
        return productNames;
    }
    HashMap<String,String> getCategoryWiseStatus(HashMap<String,List<Activity>> categoryActivityMap){
        Set<String> keySet= categoryActivityMap.keySet();
        HashMap<String,String> map=new HashMap<>();
        for (String category:keySet) {
            List<Activity> activities=categoryActivityMap.get(category);
            if(areAllActivitiesWithStatus(activities,ActivityStatus.TODO)){
                map.put(category.trim(),ActivityStatus.TODO.toString());
            }
            else if(areAllActivitiesWithStatus(activities,ActivityStatus.COMPLETED)){
                map.put(category.trim(),ActivityStatus.COMPLETED.toString());
            }
            else {
                map.put(category.trim(),ActivityStatus.IN_PROGRESS.toString());
            }

        }
        return map;
    }
    public boolean areAllActivitiesWithStatus(List<Activity> activities, ActivityStatus statusToCheck) {
        for (Activity activity : activities) {
            if (!activity.getStatus().equals(statusToCheck)) {
                return false; // Found an activity with a different status
            }
        }
        return true; // All activities have the same status
    }


    HashMap<String,List<Activity>> getCategoryWiseActivities(List<Activity> activities){
        HashMap<String,List<Activity>> map=new HashMap<String,List<Activity>>();
        for (Activity activity:activities) {
            if(!map.containsKey(activity.getCategory())){
                map.put(activity.getCategory(),new ArrayList<>());
            }
            List<Activity> value=map.get(activity.getCategory());
            value.add(activity);
        }
        return  map;
    }

    @SneakyThrows
    public void updateActivityStatus(Activity activity, ActivityStatus activityStatus) {
        if (
            (activityStatus.equals(ActivityStatus.IN_PROGRESS) && activity.getStatus().equals(ActivityStatus.TODO)) ||
            (activityStatus.equals(ActivityStatus.COMPLETED) && activity.getStatus().equals(ActivityStatus.IN_PROGRESS)) ||
            // ADDING TRANSITION RULE : TODO --> COMPLETED
            (activityStatus.equals(ActivityStatus.COMPLETED) && activity.getStatus().equals(ActivityStatus.TODO)) ||
                    (activityStatus.equals(ActivityStatus.IN_PROGRESS) && activity.getStatus().equals(ActivityStatus.COMPLETED))
        ) {
            activity.setStatus(activityStatus);
        }
//        else {
//            throw new ServiceException("Invalid State transition", 500);
//        }
        List<Event> events = activity.getEvents();
        if(events!=null){
            for (Event event : events) {
                eventManager.executeEvent(event, activity);
            }
        }
        List<HashMap> history = activity.getHistory();
        if (history == null) {
            history = new ArrayList<>();
            activity.setHistory(history);
        }
        HashMap status = new HashMap<String, Object>();
        status.put(activityStatus, new Date());
        history.add(status);
    }

    @Override
    public void deleteActivity(String id) {
        Optional<Activity> activityOptional = activityRepository.findById(id);
        activityOptional.ifPresent(activity -> {
            activity.softDelete();
            activityRepository.save(activity);
        });
    }

    public void transferCriticalPath(String oldCustomerOrderId,String newCustomerOrderId,boolean fromInventory){
        //get all activitie with old ids and replace with new ones
        List<Activity> activities=activityRepository.findByEntityId(oldCustomerOrderId);
        for ( Activity activity:activities) {
            if(fromInventory){
                activity.setEntityId(newCustomerOrderId);
            }
            else{
                activity.setDeleted(true);
            }
        }
        activityRepository.saveAll(activities);
    }

    @Override
    public Page<Activity> filterActivities(FilterRequest request) {
        Pageable pageable = PageRequest.of(request.getNumber(), request.getSize());
        
        // Build match criteria
        Criteria matchCriteria = buildMatchCriteria(request);
        
        // Create aggregation pipeline
        TypedAggregation<Activity> aggregation = Aggregation.newAggregation(
            Activity.class,
            // Match stage
            Aggregation.match(
                Criteria.where("orderId").ne(null)
                    .and("deleted").ne(true)
                    .andOperator(matchCriteria)
            ),
            // Sort activities by createdAt ascending
            Aggregation.sort(Sort.Direction.ASC, "createdAt"),
            // Group by orderId
            Aggregation.group("orderId")
                .push("$$ROOT").as("activities")
                .max("createdAt").as("latestCreatedAt"),
            // Sort groups by latest activity's createdAt descending
            Aggregation.sort(Sort.Direction.DESC, "latestCreatedAt")
        );
        
        // Execute aggregation
        AggregationResults<Document> result = mongoTemplate.aggregate(
            aggregation,
            Document.class
        );
        
        // Flatten and convert results
        List<Activity> flattenedActivities = flattenResults(result.getMappedResults());
        
        // Calculate proper pagination bounds
        int totalElements = flattenedActivities.size();
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), totalElements);
        
        // Handle case where start is beyond the list size
        if (start >= totalElements) {
            return new PageImpl<>(
                Collections.emptyList(),
                pageable,
                totalElements
            );
        }
        
        // Return paginated results
        return new PageImpl<>(
            flattenedActivities.subList(start, end),
            pageable,
            totalElements
        );
    }

    private Criteria buildMatchCriteria(FilterRequest request) {
        List<Criteria> criteriaList = new ArrayList<>();
        
        // Handle filters (exact match with $in operator)
        if (request.getFilters() != null && !request.getFilters().isEmpty()) {
            request.getFilters().forEach((key, values) -> {
                if (values != null && !values.isEmpty()) {
                    criteriaList.add(Criteria.where(key).in(values));
                }
            });
        }
        
        // Handle search (regex search for strings)
        if (request.getSearch() != null && !request.getSearch().isEmpty()) {
            request.getSearch().forEach((key, value) -> {
                if (value != null && !value.isEmpty()) {
                    criteriaList.add(Criteria.where(key).regex(value, "i"));
                }
            });
        }
        
        return criteriaList.isEmpty() 
            ? new Criteria() 
            : new Criteria().andOperator(criteriaList.toArray(new Criteria[0]));
    }

    private List<Activity> flattenResults(List<Document> groupedResults) {
        List<Activity> flattenedActivities = new ArrayList<>();
        
        for (Document group : groupedResults) {
            @SuppressWarnings("unchecked")
            List<Document> activities = (List<Document>) group.get("activities");
            if (activities != null) {
                for (Document activityDoc : activities) {
                    Activity activity = mongoTemplate.getConverter()
                        .read(Activity.class, activityDoc);
                    flattenedActivities.add(activity);
                }
            }
        }
        
        return flattenedActivities;
    }

    private List<Object> convertFilterValues(String key, List<String> values) {
        // Add type conversion logic based on field type
        switch (key) {
            case "status":
                return values.stream()
                    .map(ActivityStatus::valueOf)
                    .collect(Collectors.toList());
            case "createdAt":
            case "updatedAt":
                return values.stream()
                    .map(this::parseDate)
                    .collect(Collectors.toList());
            // Add more cases for other field types as needed
            default:
                return new ArrayList<>(values);
        }
    }

    private Date parseDate(String dateStr) {
        try {
            return Date.from(Instant.parse(dateStr));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format: " + dateStr);
        }
    }

    // Add helper method to determine field type
    private Class<?> getFieldType(String fieldName) {
        try {
            return Activity.class.getDeclaredField(fieldName).getType();
        } catch (NoSuchFieldException e) {
            return String.class; // Default to String if field not found
        }
    }

    @Override
    public List<Activity> findPendingInvoiceUploadActivitiesByCustomer(String customerName) {
        return activityRepository.findPendingInvoiceUploadActivitiesByCustomer(customerName);
    }

}
