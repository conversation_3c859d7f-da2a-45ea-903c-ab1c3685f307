package com.mstack.keystone.service.interfaces;

import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.FilterRequest;
import com.mstack.keystone.model.dto.SupplierDocument;
import com.mstack.keystone.model.dto.SupplierProductDto;
import com.mstack.keystone.model.repository.Supplier;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

public interface ISupplierService {
    Supplier saveSupplier(Supplier supplier);
    Page<Supplier> filterSuppliers(FilterRequest request);
    Supplier getSupplierById(String id);
    Supplier updateSupplier(String id, Supplier newSupplierValues) throws IllegalAccessException;
    boolean supplierExists(String id);

    void deleteSupplier(String id) throws ServiceException;
    void deleteMstackDocument(String documentType,String supplierId,String productId,String fileId);
    List<SupplierProductDto> getSuppliersForProductId(String productId);

    List<SupplierDocument> uploadMstackDocument(String supplierId,String productId, List<SupplierDocument> documents);
}
