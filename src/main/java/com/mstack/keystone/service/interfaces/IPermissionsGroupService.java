package com.mstack.keystone.service.interfaces;

import com.mstack.keystone.model.repository.DashBoardConfig;
import com.mstack.keystone.model.repository.PermissionsGroup;

import java.util.HashMap;
import java.util.List;

public interface IPermissionsGroupService {
    PermissionsGroup createPermissionsGroup(PermissionsGroup permissionsGroup);
    PermissionsGroup getPermissionsGroup(String groupId);
    PermissionsGroup updatePermissionsGroup(String groupId, PermissionsGroup permissionsGroup);
    void deletePermissionsGroup(String groupId);

    List<PermissionsGroup> getAllGroups();

    DashBoardConfig getAllPermissionConfig();
}
