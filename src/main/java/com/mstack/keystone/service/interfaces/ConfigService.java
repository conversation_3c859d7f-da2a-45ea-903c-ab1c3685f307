package com.mstack.keystone.service.interfaces;

import com.mstack.keystone.model.repository.DashBoardConfig;
import java.util.List;
import java.util.Optional;

public interface ConfigService {
    List<DashBoardConfig> getAllDashboardConfigs();
    DashBoardConfig getDashboardConfigByConfigName(String configName);
    DashBoardConfig createDashboardConfig(DashBoardConfig dashboardConfig);

    DashBoardConfig updateDashboardConfig(String configName, DashBoardConfig updatedDashboardConfig);
    void deleteDashboardConfig(String id);
}
