package com.mstack.keystone.constants;

public final class AppConstants {
    // Define your constants here
    public static final String LUT_NUMBER = "AD270324182804X";
    // SUPPLIER PO CHEMSTACK TERMS AND CONDITION
    public static final String SUPPLIER_PO_C_T_C = "1.INSPECTION: Samples from each production batch will be tested at a third party lab appointed by Chemstack. Once the sample is approved the material is dispatched from the factory.\n" +
            "\n" +
            "2.QUALITY: Claims including any quality issue on arrival in the end destination country can be notified within 30 days from arrival in the destination country. The manufacturer will refund the full purchase price.\n" +
            "\n" +
            "3.MEASUREMENT - For ISO tank, weight slip proof of Gross weight and net weight of the container should be recorded and shared . VGM slips are mandatory and need to be verified with the weight recorded at the factory. There shouldn’t be a discrepancy of more than 0.5% between them.\n" +
            "\n" +
            "4.DELIVERY : Late delivery charges of 0.5% of PO basic value per every Week of delay up-to a maximum of 5%\n" +
            "\n" +
            "5.RISK PURCHASE : If the Supplier fails to supply the finished goods no later than 30 days from the Delivery Schedule, Chemstack shall be entitled to cancel the PO and in the event of such cancellation the Supplier shall be liable to pay (a) liquidated damages of 5% of the PO; (b) differential cost incurred in procuring Product from third parties; (c) Advances with interest,\n" +
            "\n" +
            "6.FORCE-MAJEURE: Force-majeure will be applicable only in the event of rare natural calamity, such as an earthquake or flood, tsunami, which caused enough havoc to disturb the production schedules. The normal conditions of Strikes, Lock-outs, Changes in Govt. Regulations,problems arising out of statutory requirements including but not limited to Code requirements, shortage of electrical power etc. are not covered under force-majeure conditions.\n" +
            "\n" +
            "7.LIABILITY of SUPPLIER: Supplier shall be liable for all losses resulting to Chemstack from non-compliance with the Delivery Schedule or breach of any terms of the P,O, including any loss of profits, to the maximum extent permissible under law. Notwithstanding anything, no limitation or exclusion of liability shall apply with respect to any claims based on this P.O arising out of the Supplier’s willful misconduct or gross negligence,or with respect to any claims for personal injury or property damage, or to Supplier’s indemnification obligations stated herein.\n" +
            "\n" +
            "8.CONSEQUENTIAL DAMAGES: Chemstack shall in no event be liable for loss of profit, loss of revenues, loss of use, loss of production, costs of capital or costs connected with interruption of operation, loss of anticipated savings or for any special, indirect or consequential damage or loss Chemstack’s entire liability for any claim, whether in contract, tort, or any other theory of liability shall be limited to the P.O value. of any nature whatsoever, accruing to Supplier arising out of execution of this PO or cancellation of the PO due to non-performance.\n" +
            "\n" +
            "9.CONFIDENTIALITY: Supplier undertakes that it shall, at all times, maintain confidentiality of all the information including other technical specifications received by it in respect of the P.O and/or disclosed to it by Chemstack or its client and shall not disclose or divulge the same or any part thereof to any third party without the prior written consent of Chemstack. The obligations of this clause shall survive\n" +
            "\n" +
            "10.ARBITRATION: All disputes in connection with this contract or the execution thereof shall be amicably settled through negotiation in case no settlement can be reached between the two parties, the case under dispute shall be submitted to US Arbitration. The arbitration shall take place in the US and shall be executed in accordance with the arbitration rules of the US Commercial Arbitration board. The award rendered by the arbitration center shall be final and shall bind both parties conclusively. Judgment may be entered in any court having jurisdiction thereof. The parties obligation under this paragraph to arbitrate shall not preclude either party from making a request to a court for interim protections.\n" +
            "\n" +
            "11.DOCUMENT REQUIRED -\n" +
            "\tInvoice\n" +
            "\tPacking List\n" +
            "\tEway Bill\n" +
            "\tCertificate of Analysis";

    public static final String SUPPLIER_PO_M_T_C = "1. INSPECTION: Samples from each production batch will be tested at a third party lab appointed by Mstack. Once the sample is approved the material is dispatched from the factory. \n" +
            "\n" +
            "\n" +
            "2. QUALITY: Claims including any quality issue on arrival in the end destination country can be notified within 30 days from arrival in the destination country. The manufacturer will refund the full purchase price.\n" +
            "\n" +
            "\n" +
            "3. MEASUREMENT - For ISO tank, weight slip proof of Gross weight and net weight of the container should be recorded and shared . VGM slips are mandatory and need to be verified with the weight recorded at the factory. There shouldn’t be a discrepancy of more than 0.5% between them.\n" +
            "\n" +
            "\n" +
            "4. DELIVERY : Late delivery charges of 0.5% of PO basic value per every Week of delay up-to a maximum of 5%\n" +
            "\n" +
            "\n" +
            "5. RISK PURCHASE : If the Supplier fails to supply the finished goods no later than 30 days from the Delivery Schedule, Mstack shall be entitled to cancel the PO and in the event of such cancellation the Supplier shall be liable to pay (a) liquidated damages of 5% of the PO; (b) differential cost incurred in procuring Product from third parties; (c) Advances with interest,\n" +
            "\n" +
            "\n" +
            "6. FORCE-MAJEURE: Force-majeure will be applicable only in the event of rare natural calamity, such as an earthquake or flood, tsunami, which caused enough havoc to disturb the production schedules. The normal conditions of Strikes, Lock-outs, Changes in Govt. Regulations, problems arising out of statutory requirements including but not limited to Code requirements, shortage of electrical power etc. are not covered under force-majeure conditions. \n" +
            "\n" +
            "\n" +
            "7. LIABILITY of SUPPLIER: Supplier shall be liable for all losses resulting to Mstack from non-compliance with the Delivery Schedule or breach of any terms of the P,O, including any loss of profits, to the maximum extent permissible under law. Notwithstanding anything, no limitation or exclusion of liability shall apply with respect to any claims based on this P.O arising out of the Supplier’s willful misconduct or gross negligence,or with respect to any claims for personal injury or property damage, or to Supplier’s indemnification obligations stated herein.\n" +
            "\n" +
            "\n" +
            "8. CONSEQUENTIAL DAMAGES: Mstack shall in no event be liable for loss of profit, loss of revenues, loss of use, loss of production, costs of capital or costs connected with interruption of operation, loss of anticipated savings or for any special, indirect or consequential damage or loss Mstack’s entire liability for any claim, whether in contract, tort, or any other theory of liability shall be limited to the P.O value. of any nature whatsoever, accruing to Supplier arising out of execution of this PO or cancellation of the PO due to non-performance. \n" +
            "\n" +
            "\n" +
            "9. CONFIDENTIALITY: Supplier undertakes that it shall, at all times, maintain confidentiality of all the information including other technical specifications received by it in respect of the P.O and/or disclosed to it by Mstack or its client and shall not disclose or divulge the same or any part thereof to any third party without the prior written consent of Mstack. The obligations of this clause shall survive.\n" +
            "\n" +
            "\n" +
            "10. ARBITRATION: All disputes in connection with this contract or the execution thereof shall be amicably settled through negotiation in case no settlement can be reached between the two parties, the case under dispute shall be submitted to US Arbitration. The arbitration shall take place in the US and shall be executed in accordance with the arbitration rules of the US Commercial Arbitration board. The award rendered by the arbitration center shall be final and shall bind both parties conclusively. Judgment may be entered in any court having jurisdiction thereof. The parties obligation under this paragraph to arbitrate shall not preclude either party from making a request to a court for interim protections. \n" +
            "\n" +
            "\n" +
            "11. DOCUMENT REQUIRED -\n" +
            "   1. Bill of Lading  \n" +
            "   2. ISO Certificate of manufacturer \n" +
            "   3. Certificate of Origin \n" +
            "   4. GMP of manufacturer \n" +
            "   5. Certificate of Analysis";
    public static final String MSTACK_BILLING_ADDRESS = "Mstack Inc. , 21st Floor, Wework 2700 Post Oak Blvd Galleria Office Tower I, Houston, TX 77056 | Tel. No.: +91-9726939356 ";
    public static final String CHEMSTACK_GUJARAT_BILLING_ADDRESS = "Second Floor, Office No. SF-11,12, Omkar Complex,Valia Chokdi,GIDC Ankleshwar, Ankleshwar, Bharuch, Gujarat, 393002";
    public static final String CHEMSTACK_MUMBAI_BILLING_ADDRESS = "3rd Floor Room A, E-309, Crystal Plaza Premises Co Op Soc Ltd, New Link Road, Opp Infinity Mall, Andheri West, Mumbai , Mumbai Suburban , Maharashtra , 400053";

    public static final double METRIC_TON_TO_KILOGRAM = 1000.0;
    public static final double POUND_TO_KILOGRAM = 0.453592;
    public static final double GALLON_TO_LITRE = 3.78541;
    public static final double LITRE_TO_KILOGRAM = 1.0; // Assuming density of water (1 litre = 1 kilogram)
    public static final double KILOLITRE_TO_LITRE = 1000.0;

    public static final String ANALYTICS_GROUP_MAIL_ID = "<EMAIL>";

    public static final String INDIA_ZONE_ID = "Asia/Kolkata";
    public static final String INVOICE_DATE_ERROR = "Cannot generate document without invoice date ";

    //mongo entity names
    public static final String CUSTOMER_ORDER = "customer-order";
    public static final String SUPPLIER_ORDER = "supplier-order";
    public static final String ORDER_BOOK = "order-book";
    public static final String SUPPLIER_ORDER_BOOK = "supplier-order-book";
    public static final String CUSTOMER = "customer";
    public static final String PRODUCT = "product";
    public static final String ID = "_id";

}
