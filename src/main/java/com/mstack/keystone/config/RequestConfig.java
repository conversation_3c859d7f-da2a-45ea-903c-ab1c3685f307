package com.mstack.keystone.config;

import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.repository.user.EmployeeRepository;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.RequestScope;

@Component
@RequestScope
@Data
public class RequestConfig {

    String entityType;
    String entityId;
}
