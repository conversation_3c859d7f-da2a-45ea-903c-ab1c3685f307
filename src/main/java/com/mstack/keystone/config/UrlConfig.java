package com.mstack.keystone.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
@ConfigurationProperties(prefix = "url")
@Data
public class UrlConfig {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    String gatewayUrl;
    String documentUrl;
}
