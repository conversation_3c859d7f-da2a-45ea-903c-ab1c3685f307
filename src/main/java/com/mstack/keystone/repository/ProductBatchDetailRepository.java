package com.mstack.keystone.repository;

import com.mstack.keystone.model.repository.inventory.ProductBatchDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ProductBatchDetailRepository extends MongoRepository<ProductBatchDetail, String> {
    @Query("{ 'inventoryInOrderId' : ?0 , 'deleted':false }")
    List<ProductBatchDetail> findByInventoryOrderId(String inventoryOrderId);

    @Query("{'packagingId':?0 ,'productId': ?1 ,inventoryOutOrderId: null }")
    List<ProductBatchDetail> findUnAllocatedBatches(String packagingId, String productId);

    @Query("{ 'productId' : ?0,'inventoryId':?1,'packagingId':?2 'type': ?3, $or: [ { 'customerOrderDispatchId': { $exists: false } }, { 'customerOrderDispatchId': null } ], 'deleted': false }")
    List<ProductBatchDetail> findUnAllocatedBatchesByProductIdAndInventoryIdAndType(String productId, String inventoryId, String packagingId, String type);

    @Query("{  $or: [ { 'customerOrderDispatchId': { $exists: false } }, { 'customerOrderDispatchId': null } ], 'productId':?0, 'inventoryId':?1,'packagingId':?2, 'deleted':false ,'type':?3 , 'supplierOrderDispatchId':?4}")
    List<ProductBatchDetail> findVirtualBatches(String productId, String inventoryId, String packagingId, String type, String supplierOrderDispatchId);

    @Query("{'packagingId': ?0 , 'productId': ?1, 'inventoryId':?2, 'type': ?3 , 'deleted': false }")
    Page<ProductBatchDetail> findUnAllocatedBatchForPackagingAndProductAndInventory(String packagingId, String productId, String inventoryId, String type, Pageable pageable);

    @Query("{'supplierOrderDispatchId': ?0 , 'deleted': false }")
    List<ProductBatchDetail> findBatchesForSupplierDispatchOrder(String supplierDispatchOrderId);

    @Query("{'customerOrderDispatchId':?0, 'deleted' : false ,  'type' : 'ALLOCATED' }")
    List<ProductBatchDetail> findAllocatedBatches(String customerDispatchId);

    @Query("{'batchUID': ?0 , 'type' : 'UN_ALLOCATED', 'deleted': false } ")
    ProductBatchDetail findUnAllocatedProduct(String batchUID);
}
