package com.mstack.keystone.repository;

import com.mstack.keystone.model.repository.EnquiryQuotationHistory;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface EnquiryQuotationRepository extends MongoRepository<EnquiryQuotationHistory, String> {
    @Query("{'enquiryId':?0}")
    EnquiryQuotationHistory findByEnquiryId(String enquiryId);
}
