package com.mstack.keystone.repository;

import com.mstack.keystone.model.repository.Customer;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CustomerCustomRepository {
    Page<Customer> findByFilters(
        List<String> countries,
        String accountOwnerName,
        String customerName,
        Pageable pageable
    );
}
