package com.mstack.keystone.repository.user;

import com.mstack.keystone.model.repository.EnquiryQuotationHistory;
import com.mstack.keystone.model.repository.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRepository extends MongoRepository<User, String> {

    @Query("{'entityId':?0}")
    User findByEntityId(String entityId);

    @Query("{'username':?0 , 'deleted':false}")
    List<User> findByUserName(String username);
}