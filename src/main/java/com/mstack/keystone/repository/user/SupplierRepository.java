package com.mstack.keystone.repository.user;

import com.mstack.keystone.model.dto.SupplierProduct;
import com.mstack.keystone.model.repository.Supplier;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface SupplierRepository extends MongoRepository<Supplier, String> {
    @Query("{'products.product._id': ?0 }")
    List<Supplier> getSuppliersFromProductId(ObjectId productId);

    @Query("{'deleted':false}")
    List<Supplier> getAllNonDeletedSupplier();

    @Query("{ 'supplierId':?0, 'deleted':false}")
    List<Supplier> findBySupplierId(String supplierId);
}
