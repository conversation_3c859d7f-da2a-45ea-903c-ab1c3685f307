package com.mstack.keystone.repository.packaging;

import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.service.packaging.PackagingService;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface PackagingRepository extends MongoRepository<Packaging, String> {
    @Query("{ $or: [ { 'deleted': { $exists: false } }, { 'deleted': false } ] }")
    List<Packaging> getAllPackagingList();

    @Query("{ 'packagingName': ?0 , 'deleted':false }")
    Packaging findByPackagingName(String updatedPackagingName);
}
