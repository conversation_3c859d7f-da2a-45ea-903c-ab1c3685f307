package com.mstack.keystone.repository.inventory;

import com.mstack.keystone.model.repository.inventory.InventoryProduct;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface InventoryProductRepository extends MongoRepository<InventoryProduct, String> {
    @Query("{'inventoryId': ?0}")
    List<InventoryProduct> findByInventoryId(String inventoryId);

    @Query("{'packagingId': ?0, 'productId': ?1 , 'inventoryId': ?2 }")
    InventoryProduct findByPackagingIdAndProductIdAndInventoryId(String packagingId, String productId, String inventoryId);

    @Query("{'packagingId': ?0, 'productId': ?1 , 'deleted': false}")
    List<InventoryProduct> findByPackagingIdAndProductId(String packagingId,String productId);
}
