package com.mstack.keystone.repository.inventory;

import com.mstack.keystone.model.repository.inventory.InventoryProductTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface InventoryTransactionRepo extends MongoRepository<InventoryProductTransaction, String> {

}
