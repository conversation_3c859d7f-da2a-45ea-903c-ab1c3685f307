package com.mstack.keystone.repository;

import com.mstack.keystone.model.dto.ActivityGroup;
import com.mstack.keystone.model.repository.Activity;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Aggregation;
import org.bson.Document;

public interface ActivityRepository extends MongoRepository<Activity, String> {
    @Query("{ 'id' : { $in : ?0 } }")
    List<Activity> findByEntityIds(List<String> entityId);

    @Query("{ 'entityId' :  ?0 , 'deleted' : false }")
    List<Activity> findByEntityId(String entityId);
    @Query("{ 'entityId' :  ?0 , 'deleted' : false }")
    List<Activity> findByEntityId(String entityId, Pageable pageable);


    @Query("{'status': { $ne: 'COMPLETED' }, 'deleted': false, 'assignedTo': ?0 }")
    Page<Activity> findActivitiesForUser(String employeeId, Pageable pageable);

    @Query("{'status': { $ne: 'COMPLETED' }, 'deleted': false,'entityId': ?0 }")
    List<Activity> findIncompleteActivities(String entityId);

    List<Activity> findByOrderIdAndDependentOn(String orderId, String dependentOn);

    List<Activity> findByOrderId(String orderId);

    @Aggregation(pipeline = {
        "{ $match: ?0 }",  // Dynamic match stage for all filters
        "{ $sort: { 'createdAt': 1 } }",
        "{ $group: { " +
            "_id: '$orderId', " +
            "activities: { $push: '$$ROOT' }, " +
            "latestCreatedAt: { $max: '$createdAt' }" +
        "} }",
        "{ $sort: { 'latestCreatedAt': -1 } }"
    })
    List<ActivityGroup> findGroupedActivities(Document matchCriteria);

    @Query("{ 'secondaryId' : ?0 , 'deleted' : false }")
    List<Activity> findBySecondaryId(String secondaryId);

    @Query("{ 'customerName' : ?0, 'name' : 'Invoice Upload', 'status' : { $in: ['TODO', 'IN_PROGRESS'] } }")
    List<Activity> findPendingInvoiceUploadActivitiesByCustomer(String customerName);

}
