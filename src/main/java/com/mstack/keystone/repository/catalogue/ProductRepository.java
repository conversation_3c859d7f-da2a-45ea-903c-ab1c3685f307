package com.mstack.keystone.repository.catalogue;

import com.mstack.keystone.model.repository.Product;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface ProductRepository extends MongoRepository<Product, String> {

    @Query("{'tradeName': {$regex: ?0, $options: 'i' }}")
    Product findByTradeName(String productName);

    @Query("{'tradeName':?0 , 'deleted':false}")
    Product findByExactTradeName(String productName);

}
