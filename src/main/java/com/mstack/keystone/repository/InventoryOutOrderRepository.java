package com.mstack.keystone.repository;

import com.mstack.keystone.model.repository.inventory.InventoryOutOrder;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface InventoryOutOrderRepository extends MongoRepository<InventoryOutOrder, String> {
    @Query("{ 'customerDispatchOrderId' : { $in : ?0 } , 'deleted':false }")
    List<InventoryOutOrder> getInventoryOutOrdersByCustomerOrderId(List<String> ids);

}
