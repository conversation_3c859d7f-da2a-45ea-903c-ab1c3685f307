package com.mstack.keystone.repository;

import com.mstack.keystone.model.repository.PermissionsGroup;
import lombok.NonNull;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface PermissionsGroupRepository extends MongoRepository<PermissionsGroup,String> {
    @Query("{'name':?0}")
    PermissionsGroup findByPermissionsGroupName(String groupName);
}
