package com.mstack.keystone.repository.order;

import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface SupplierOrderBookRepo extends MongoRepository<SupplierOrderBook, String> {
    @Query("{'purchaseOrderNumber': ?0, 'deleted':false}")
    SupplierOrderBook findByPONumber(String poNumber);

    @Query("{'purchaseOrderNumber':?0 , 'supplier._id':?1, 'deleted':false}")
    SupplierOrderBook findByPONumberAndSupplierId(String poNumber, ObjectId supplierId);

    @Query("{'supplier._id':?0, 'deleted':false}")
    List<SupplierOrderBook> findBySupplierId(ObjectId supplierId);

    @Query("{'linkedCOBs':?0 , 'deleted':false }")
    List<SupplierOrderBook> findByCustomerOrderBookId(String id);

    @Query("{ 'linkedCOBId': { $in: ?0 } }")
    List<Document> findByLinkedCOBIdIn(List<String> orderIds);
}
