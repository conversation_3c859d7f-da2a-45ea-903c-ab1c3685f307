package com.mstack.keystone.repository.order;

import com.mstack.keystone.model.repository.order.SupplierOrder;
import java.util.List;

import com.mstack.keystone.model.repository.order.SupplierOrderBook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface SupplierOrderRepository extends MongoRepository<SupplierOrder, String> {
    List<SupplierOrder> findByDeletedFalse();

    @Query("{$or: [ {'orderId': {$regex: ?0, $options: 'i' }}, {'purchaseOrderNumber': {$regex: ?0, $options: 'i' }} ], deleted:false }")
    Page<SupplierOrder> findByOrderNumberOrPoNumber(String searchText, Pageable pageable);


    @Query("{'products.linkedOrderIds': ?0 }")
    List<SupplierOrder> findAllOrdersWithDispatchOrderId(String id);

    @Query("{'purchaseOrderNumber': ?0 }")
    SupplierOrderBook findByPONumber(String id);

    @Query("{'linkedSupplierOrderBookId': ?0, 'deleted':false}")
    List<SupplierOrder> findSupplierOrdersByOrderBookId(String id);

    @Query("{'purchaseOrderNumber': {$in : ?0 }, 'deleted':false}")
    List<SupplierOrder> findAllBySupplierPos(List<String> id);
}
