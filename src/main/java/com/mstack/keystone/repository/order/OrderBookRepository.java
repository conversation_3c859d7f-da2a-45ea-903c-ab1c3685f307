package com.mstack.keystone.repository.order;

import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface OrderBookRepository extends MongoRepository<OrderBook, String> {
    @Query("{'purchaseOrderNumber': ?0 , 'deleted' : false}")
    OrderBook findByPONumber(String poNumber);

    @Query("{'purchaseOrderNumber':?0 , 'customer._id':?1 ,'deleted' : false}")
    OrderBook findByPONumberAndCustomerId(String poNumber, ObjectId customerId);

    @Query("{'customer._id':?0 ,'deleted' : false}")
    List<OrderBook> findByCusotmerId(ObjectId customerId);

    // TODO GB SHOULD THIS INCLUDE DELETED COUNT ?
    @Query("{'inventoryId': {$exists: true}}")
    long countOfInventoryOrderbook();

    @Query("{'deleted':false}")
    List<OrderBook> findAllNonDeletedOrdersBook();

    @Query("{ 'purchaseOrderNumber':?0, 'inventoryId':?1 , 'deleted':false }")
    OrderBook findByPONumberAndInventoryId(String poNumber, String inventoryId);

    @Query("{ 'orderBookId':?0 , 'deleted':false }")
    List<OrderBook> findByOrderBookId(String cobId);
}
