package com.mstack.keystone.repository.order;

import com.mstack.keystone.model.repository.order.CustomerOrder;

import java.util.Date;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface CustomerOrderRepository extends MongoRepository<CustomerOrder, String> {
    List<CustomerOrder> findByDeletedFalse();

    @Query("{'status': { $ne: 'PAYMENT_COMPLETED' }, 'customer.id': ?0 }")
    List<CustomerOrder> findDispatchOrdersWithStatusNotCompletedAndCustomerId(ObjectId customerId);

    @Query("{$or: [ {'orderId': {$regex: ?0, $options: 'i' }}, {'purchaseOrderNumber': {$regex: ?0, $options: 'i' }} ],'deleted':false}")
    Page<CustomerOrder> findByOrderNumberOrPoNumber(String searchText, Pageable pageable);

    @Query("{'orderId': ?0 , 'deleted':false }")
    CustomerOrder findByOrderId(String orderId);

    @Query("{'orderId': ?0 , 'deleted':false }")
    List<CustomerOrder> findListByOrderId(String orderId);


    @Query("{'purchaseOrderNumber': ?0 , 'deleted' : false}")
    List<CustomerOrder> findOrdersByPoNumber(String poNumber);

    @Query("{'customer._id': ?0 ,'purchaseOrderNumber': ?1, 'deleted' : false}")
    List<CustomerOrder> findOrdersBycustomerIdAndPoNumber(String customerId, String poNumber);

    @Query("{'inventoryId': ?0 ,'purchaseOrderNumber': ?1, 'deleted' : false}")
    List<CustomerOrder> findOrdersByInventoryIdAndPoNumber(String inventoryId, String poNumber);

    //Filter by PO date
    @Query("{ 'purchaseOrderDate': { $gte: ?0, $lte: ?1 }, 'deleted' : false }")
    List<CustomerOrder> findByPoDateBetween(Date date1, Date date2);

    @Query("{'purchaseOrderDate': {$gte: ?0, $lte: ?1}, 'deleted': false, $and: [{'mstackInvoiceNumber': {$exists: false}}, {'chemstackInvoiceNumber': {$exists: false}}]}")
    List<CustomerOrder> findOpenOrdersByPoDateBetween(Date from, Date to);

    //Filter by Mstack invoice date
    @Query("{ 'invoiceDate': { $gte: ?0, $lte: ?1, $ne: null }, 'deleted' : false }")
    List<CustomerOrder> findByInvoiceDateBetween(Date from, Date to);

    // Filter by Chemstack invoice date
    @Query("{ 'chemstackInvoiceDate': { $gte: ?0, $lte: ?1, $ne: null }, 'deleted' : false }")
    List<CustomerOrder> findByChemstackInvoiceDateBetween(Date from, Date to);


    @Query("{'deleted':false}")
    List<CustomerOrder> findAllNonDeletedOrders();

    @Query("{'mstackInvoiceNumber': ?0 , 'deleted':false }")
    CustomerOrder findByMstackInvoiceNumber(String mstackInvoiceNumber);

    @Query("{'chemstackInvoiceNumber': ?0 , 'deleted':false }")
    CustomerOrder findByChemstackInvoiceNumber(String chemstackInvoiceNumber);

}
