package com.mstack.keystone.repository;

import com.mstack.keystone.model.repository.inventory.OBProductBatch;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface OBProductBatchRepository extends MongoRepository<OBProductBatch, String> {
    @Query("{ 'productId' : ?0,'inventoryId':?1,'packagingId':?2 'type': ?3, $or: [ { 'customerOrderBookId': { $exists: false } }, { 'customerOrderBookId': null } ], 'deleted': false }")
    List<OBProductBatch> findUnAllocatedBatchesByProductIdAndInventoryIdAndType(String productId, String inventoryId, String packagingId, String type);

    @Query("{ 'productId' : ?0,'inventoryId':?1,'packagingId':?2 , 'customerOrderBookId': ?3 ,'supplierOrderBookId':?4 , 'deleted': false }")
    OBProductBatch findAllocatedBatchForOrderBookAndSupplier(String productId, String inventoryId, String packagingId, String customerOrderbookId, String supplierId);

    @Query("{ 'productId' : ?0,'inventoryId':?1,'packagingId':?2 ,'supplierOrderBookId':?3 , 'deleted': false }")
    OBProductBatch findUnAllocatedBatchForSupplierOrderBook(String productId, String inventoryId, String packagingId, String supplierId);

    @Query("{ 'productId' : ?0,'packagingId':?1 , 'customerOrderBookId': ?2 , 'deleted': false  }")
    List<OBProductBatch> findAllocatedBatchForOrderBookID(String productId, String packagingId, String customerOrderbookId);

    @Query("{ 'productId' : ?0,'packagingId':?1 , 'customerOrderBookId': ?2 , 'deleted': false ,'inventoryInOrderId': { $exists: true }  }")
    List<OBProductBatch> findAllocatedBatchForOrderBookIDViaInventory(String productId, String packagingId, String customerOrderbookId);

    @Query("{ 'productId' : ?0,'packagingId':?1 'type': ?2, 'supplierOrderBookId':?3 , $or: [ { 'customerOrderBookId': { $exists: false } }, { 'customerOrderBookId': null } ], 'deleted': false }")
    OBProductBatch findUnAllocatedBatchForSO(String productId,String packagingId,String type,String supplierOrderId);

    @Query("{ 'productId' : ?0,'packagingId':?1 'type': ?2, $or: [ { 'customerOrderBookId': { $exists: false } }, { 'customerOrderBookId': null } ], 'deleted': false }")
    List<OBProductBatch> findUnAllocatedBatchesByProductIdAndPackagingId(String productId, String packagingId, String type);

}
