package com.mstack.keystone.repository.accounting;

import com.mstack.keystone.model.repository.Accounting.Expense;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ExpenseRepository extends MongoRepository<Expense,String> {
    @Query("{'invoiceId':?0 }")
    List<Expense> findByInvoiceId(String invoiceId);

    @Query("{ 'meta.productId': ?0, 'meta.dispatchOrderId': ?1, 'meta.poNumber': ?2 }")
    List<Expense> findByMetaProductIdAndDispatchOrderIdAndPoNumber(String productId, String dispatchOrderId, String poNumber);

}
