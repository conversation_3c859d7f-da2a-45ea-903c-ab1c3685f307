package com.mstack.keystone.repository.accounting;

import com.mstack.keystone.model.repository.Accounting.Invoice;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface InvoiceRepository extends MongoRepository<Invoice,String> {
    @Query("{ 'items.dispatchOrderId': ?0,'items.productId': ?1, 'items.poNumber': ?2 }")
    List<Invoice> findInvoicesByDispatchOrderIdAndProductIdAndPoNumber(String dispatchOrderId, String productId, String poNumber);
}
