package com.mstack.keystone.repository.accounting;

import com.mstack.keystone.model.repository.Accounting.OperationalInvoices;
import org.springframework.data.mongodb.repository.MongoRepository;
import java.util.Optional;

public interface InvoiceRepositoryV2 extends MongoRepository<OperationalInvoices,String> {
    Optional<OperationalInvoices> findByCustomerPurchcaseOrderId(String customerPurchcaseOrderId);
}
