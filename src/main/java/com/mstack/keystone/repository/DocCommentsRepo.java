package com.mstack.keystone.repository;

import com.mstack.keystone.model.dto.DocComment;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.*;

public interface DocCommentsRepo extends MongoRepository<DocComment, String> {
    @Query(value = "{'fileId': ?0 ,'deleted':false}", sort = "{ 'createdAt': -1 }")
    List<DocComment> findByFileId(String fileId);

    @Query(value = "{'fileId': {$in:?0} ,'resolved':false, 'deleted':false}", sort = "{ 'createdAt': -1 }")
    List<DocComment> findByFileIds(List<String> fileId);
}
