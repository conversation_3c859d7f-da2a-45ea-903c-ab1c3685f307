package com.mstack.keystone.repository.custom;

import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.repository.CustomerCustomRepository;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerRepositoryCustomImpl implements CustomerCustomRepository {

    private final MongoTemplate mongoTemplate;

    public CustomerRepositoryCustomImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Page<Customer> findByFilters(
        List<String> countries,
        String accountOwnerName,
        String customerName,
        Pageable pageable
    ) {
        Query query = new Query();

        // Add filters conditionally
        if (countries != null && !countries.isEmpty()) {
            query.addCriteria(Criteria.where("country").in(countries));
        }
        if (accountOwnerName != null && !accountOwnerName.isEmpty()) {
            query.addCriteria(Criteria.where("accountOwner").regex(accountOwnerName, "i"));
        }
        if (customerName != null && !customerName.isEmpty()) {
            query.addCriteria(Criteria.where("name").regex(customerName, "i"));
        }

        // Implement pagination
        query.with(pageable);

        // Execute the query and return a Page
        List<Customer> results = mongoTemplate.find(query, Customer.class);
        long totalCount = mongoTemplate.count(query, Customer.class);

        return PageableExecutionUtils.getPage(results, pageable, () -> totalCount);
    }
}
