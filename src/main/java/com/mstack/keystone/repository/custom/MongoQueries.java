package com.mstack.keystone.repository.custom;

import com.mongodb.internal.operation.AggregateOperation;
import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.exception.ObjectNotFoundException;
import com.mstack.keystone.exception.ServiceException;
import com.mstack.keystone.model.dto.TabConfig;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.model.repository.UserConfig;
import com.mstack.keystone.repository.user.UserRepository;
import com.mstack.keystone.service.UserConfigService;
import lombok.SneakyThrows;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

@Component
public class MongoQueries {

    private final MongoTemplate mongoTemplate;

    @Autowired
    RequestConfig requestConfig;

    @Autowired
    public MongoQueries(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Autowired
    private UserConfigService userConfigService;
    @Autowired
    private UserRepository userRepository;
    @SneakyThrows
    private Criteria getUserConfigCriteria(String entityName) {
        // disabling user config
        return null;
        // find the user config
//        UserConfig userConfig=userConfigService.getUserConfig(requestConfig.getEntityId());
//        if (userConfig == null)
//            throw new ServiceException("User config is not present , please get user config added from admin", 400);
//        TabConfig tabConfig=userConfig.getTabConfigs().get(entityName);
//        // if user doesnot have config for current tab access throw forbidden error
//        if (tabConfig == null)
//            throw new ServiceException("Tab config for given " + entityName + " tab is not present ", 403);
//        // if complete access is avialable then return null
//        if(tabConfig.isCompleteAcessAvailable())
//            return null;
//        // if complete access is not avialable but rules are empty throw error
//        if (tabConfig.getRules().isEmpty())
//            throw new ServiceException("No rules configured for given " + entityName + " tab. Please get rules updated from admin ", 400);
//
//        Criteria combinedCriteria=new Criteria();
//        List<Criteria> criteriaList = new ArrayList<>();
//        // else add criteria for each rules
//        tabConfig.getRules().forEach(rule->{
//            Criteria ruleCrteria = new Criteria();
//            rule.keySet().forEach(key->{
//                ruleCrteria.and(key.replace("*", ".")).in(rule.get(key));
//            });
//            criteriaList.add(ruleCrteria);
//        });
//        combinedCriteria.orOperator(criteriaList);
//        return combinedCriteria;
    }


    public <T> Page<T> findByFiltersAndSearch(
        HashMap<String, List<String>> filters,
        HashMap<String, String> search,
        Pageable pageable,
        Class<T> entityClass,
        Sort.Direction sort
    ) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();
        // check if is user admin
//        User user = userRepository.findByEntityId(requestConfig.getEntityId());
//        if (!user.getPermissions().contains("MSTACK_ADMIN")) {
//            Criteria userConfigCriteria = getUserConfigCriteria(entityClass.getSimpleName().toLowerCase());
//            if (userConfigCriteria != null)
//                criteriaList.add(userConfigCriteria);
//        }

        if (filters != null) {
            for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
                String key = entry.getKey();
                List<String> values = entry.getValue();
                if (values != null && !values.isEmpty()) {
                    criteriaList.add(Criteria.where(key).in(values));
                }
            }
        }
        if (search != null) {
            for (Map.Entry<String, String> entry : search.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (value != null && !value.isEmpty()) {
                    criteriaList.add(Criteria.where(key).regex(value, "i")); // Case-insensitive search
                }
            }
        }
        criteriaList.add(new Criteria().orOperator(
                Criteria.where("deleted").is(false),
                Criteria.where("deleted").exists(false)
        ));
        query.addCriteria(new Criteria().andOperator(criteriaList));
        long totalCount = mongoTemplate.count(query, entityClass);
        query.with(pageable);
       // query.with(Sort.by(Sort.Order.desc("id")));
        query.with(Sort.by(sort,"id"));
        List<T> results = mongoTemplate.find(query, entityClass);
        return PageableExecutionUtils.getPage(results, pageable, () -> totalCount);
    }

    public <T> Page<T> findByFiltersAndSearch(
        HashMap<String, List<String>> filters,
        HashMap<String, String> search,
        Pageable pageable,
        Class<T> entityClass){
        return findByFiltersAndSearch(filters,search,pageable,entityClass, Direction.DESC);
    }

    public <T> Page<T> findBySearch(
            HashMap<String, Object> search,
            Pageable pageable,
            Class<T> entityClass
    ) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();
        if (search != null) {
            for (Map.Entry<String, Object> entry : search.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null) {
                    criteriaList.add(Criteria.where(key).is(value)); // Case-insensitive search
                }
            }
        }
        criteriaList.add(new Criteria().orOperator(
                Criteria.where("deleted").is(false),
                Criteria.where("deleted").exists(false)
        ));
        query.addCriteria(new Criteria().andOperator(criteriaList));
        long totalCount = mongoTemplate.count(query, entityClass);
        query.with(pageable);
        query.with(Sort.by(Sort.Order.desc("id")));
        List<T> results = mongoTemplate.find(query, entityClass);
        return PageableExecutionUtils.getPage(results, pageable, () -> totalCount);
    }

    public <T> List<T> findByFields(
            Map<String, List<Object>> filters,
            Map<String, Object> search,
            Class<T> entityClass
    ) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();
        if (search != null) {
            for (Map.Entry<String, Object> entry : search.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null) {
                    criteriaList.add(Criteria.where(key).is(value)); // Case-insensitive search
                }
            }
        }
        if (filters != null) {
            for (Map.Entry<String, List<Object>> entry : filters.entrySet()) {
                String key = entry.getKey();
                List<Object> values = entry.getValue();
                if (values != null && !values.isEmpty()) {
                    criteriaList.add(Criteria.where(key).in(values));
                }
            }
        }
//        criteriaList.add(new Criteria().orOperator(
//                Criteria.where("deleted").is(false),
//                Criteria.where("deleted").exists(false)
//        ));
        query.addCriteria(new Criteria().andOperator(criteriaList));
        long totalCount = mongoTemplate.count(query, entityClass);
        query.with(Sort.by(Sort.Order.desc("id")));
        return mongoTemplate.find(query, entityClass);
    }


    @SneakyThrows
    public <T> void softDeleteById(MongoRepository<T, String> repository, String id, Class<T> entityClass) {
        Optional<T> optionalEntity = repository.findById(id);

        if (optionalEntity.isPresent()) {
            T entity = optionalEntity.get();

            if (entityClass.isInstance(entity)) {
                // Use Java reflection to set the flag dynamically
                try {
                    Field field = entity.getClass().getDeclaredField("deleted");
                    field.setAccessible(true);
                    field.set(entity, true);
                    field = entity.getClass().getDeclaredField("lastUpdatedAt");
                    field.setAccessible(true);
                    field.set(entity, new Date());
                    field = entity.getClass().getDeclaredField("lastUpdatedBy");
                    field.setAccessible(true);
                    field.set(entity, requestConfig.getEntityId());
                } catch (IllegalAccessException | NoSuchFieldException e) {
                    throw new RuntimeException("Error setting the flag: " + e.getMessage(), e);
                }
                repository.save(entity);
            } else {
                throw new IllegalArgumentException("Entity does not match the specified class.");
            }
        } else {
            throw new ObjectNotFoundException("Entity with ID " + id + " not found.", HttpStatus.NOT_FOUND.value());
        }
    }

    @SneakyThrows
    public <T> T updateEntity(MongoRepository<T, String> repository, String id, T updatedEntityValues) {
        Optional<T> optionalEntity = repository.findById(id);
        if (optionalEntity.isPresent()) {
            T existingEntity = optionalEntity.get();
            //TODO - check for is deleted and throw exception if deleted
            Field isDeleted = existingEntity.getClass().getDeclaredField("deleted");
            Field[] fields = existingEntity.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object newValue = field.get(updatedEntityValues);
                if (newValue != null) {
                    field.set(existingEntity, newValue);
                }
            }
            Field field = existingEntity.getClass().getDeclaredField("lastUpdatedAt");
            field.setAccessible(true);
            field.set(existingEntity, new Date());
            // add null check for cases where requestConfig is null yet update is happing ex : while updating executing event manager on update critical path
            if (requestConfig != null && !requestConfig.getEntityId().isEmpty()) {
                field = existingEntity.getClass().getDeclaredField("lastUpdatedBy");
                field.setAccessible(true);
                field.set(existingEntity, requestConfig.getEntityId());
            }
            T updatedEntity = repository.save(existingEntity);
            return updatedEntity;
        } else {
            throw new ServiceException("No entity with id " + id, HttpStatus.NOT_FOUND.value());
        }
    }

    @SneakyThrows
    public <T> T getEntity(MongoRepository<T, String> repository, String id) {
        Optional<T> optionalEntity = repository.findById(id);
        if (optionalEntity.isPresent()) {
            T existingEntity = optionalEntity.get();
            return existingEntity;
        } else {
            throw new ServiceException("No entity with id " + id, HttpStatus.NOT_FOUND.value());
        }
    }

    @SneakyThrows
    public <T> List<T> getAllEntities(MongoRepository<T, String> repository, List<String> id) {
        List<T> existingEntity = repository.findAllById(id);
        return existingEntity;
    }

    public <T> long getCount(MongoRepository<T, String> repository) {
        long count = repository.count();
        return count;
    }

    @SneakyThrows
    public <T> T saveEntity(MongoRepository<T, String> repository, T entity) {
        Field field = entity.getClass().getDeclaredField("createdAt");
        field.setAccessible(true);
        field.set(entity, new Date());
        field = entity.getClass().getDeclaredField("createdBy");
        field.setAccessible(true);
        field.set(entity, requestConfig.getEntityId());
        entity = repository.save(entity);
        return entity;
    }

    public <T> long countEntityByCriteria(Class<T> entity, List<Criteria> criterias) {
        Query query = new Query();
        criterias.forEach(query::addCriteria);
        long count = mongoTemplate.count(query, entity);
        return count;
    }

    public List<Map<String, Object>> findBySupplierNameOrBoth(List<Document> pipeline, String supplierName, String customerName) {
        pipeline.add(new Document("$match", new Document("supplier.name", supplierName)));
        pipeline.add(new Document("$lookup", new Document()
                .append("from", "activity")
                .append("let", new Document("orderId", "$linkedCOBId"))
                .append("pipeline", List.of(
                        new Document("$match", new Document("$expr", new Document("$and", List.of(
                                new Document("$eq", List.of("$orderId", "$$orderId")),
                                new Document("$eq", List.of("$name", "Invoice Upload")),
                                new Document("$in", List.of("$status", List.of("TODO", "IN_PROGRESS")))
                        ))))
                ))
                .append("as", "pendingActivities")
        ));

        pipeline.add(new Document("$match", new Document("$expr",
                new Document("$gt", List.of(
                        new Document("$size", new Document("$ifNull", List.of("$pendingActivities", List.of()))),
                        0
                ))
        )));

        if (customerName != null && !customerName.trim().isEmpty()) {
            pipeline.add(new Document("$match", new Document("pendingActivities.customerName", customerName)));
        }

        pipeline.add(new Document("$addFields", new Document("matchedActivities", "$pendingActivities")));
        pipeline.add(new Document("$project", new Document("pendingActivities", 0)));

        List<Map> results = mongoTemplate.getDb()
                .getCollection("supplierOrderBook")
                .aggregate(pipeline)
                .into(new ArrayList<>());

        return (List<Map<String, Object>>) (List<?>) results;
    }

}
