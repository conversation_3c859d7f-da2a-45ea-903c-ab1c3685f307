package com.mstack.keystone.repository.docEngine;

import com.mstack.keystone.model.repository.docEngine.DocEngineTemplate;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface DocEngineTemplateRepository extends MongoRepository<DocEngineTemplate, String> {
    @Query("{'docType':?0}")
    DocEngineTemplate findTemplateByDocType(String docType);

    @Query("{'source': {$ne:'CriticalPath'},'category':{$ne:'Other Documents'}}")
    List<DocEngineTemplate> findGenerationTemplates();

    @Query("{'category':?0}")
    List<DocEngineTemplate> findTemplatesByCategoryType(String category);
}
