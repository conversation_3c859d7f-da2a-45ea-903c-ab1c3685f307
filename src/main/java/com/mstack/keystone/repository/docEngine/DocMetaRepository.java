package com.mstack.keystone.repository.docEngine;

import com.mstack.keystone.model.dto.DocComment;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface DocMetaRepository extends MongoRepository<DocMeta, String> {
    @Query(value = "{'entityId': ?0 ,deleted: false}", sort = "{ 'createdAt': -1 }")
    List<DocMeta> getAllFiles(String entityId);

    @Query(value = "{'entityId': ?0 ,viewers: 'CUSTOMER' }, deleted: false}", sort = "{ 'createdAt': -1 }")
    List<DocMeta> getAllDraftFiles(String entityId);

    @Query(value = "{'label': {$in : ?0} ,entityId: ?1 ,deleted: false} ")
    List<DocMeta> getFilesByDocType(List<String> docTypes,String id);

    @Query(value = "{'entityId': ?0 , 'docType' : ?1 ,'fileId': ?2 ,'deleted' :false}")
    DocMeta getFileByOrderId(String entityId,String docType,String fileId);

    @Query(value = "{' 'docType' : ?0 ,'deleted' :false}")
    List<DocMeta> getCountOfDocType(String docType);

    @Query(value = "{'fileId': ?0 ,'deleted' :false}")
    DocMeta getFileByFileId(String fileId);

    @Query(value = "{'docType': ?0 ,'entityId':?1,'deleted' :false}")
    List<DocMeta> getFileByDocTypeAndEntityId(String docType,String entityId);

    @Query(value = "{'entityType': ?0 ,'entityId':?1,'deleted' :false}")
    List<DocMeta> getFileByEntityTypeAndEntityId(String entityType, String entityId);
}
