package com.mstack.keystone.repository.enquiry;

import com.mstack.keystone.model.repository.enquiry.Enquiry;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface EnquiryRepository extends MongoRepository<Enquiry, String> {
    List<Enquiry> findByDeletedFalse();

    @Query("{'enquiryId':?0}")
    Enquiry findByEnquiryId(String enquiryId);

    @Query("{'_id':?0,'customerId':?1}")
    Enquiry findByEnquiryIdAndCustomerId(String enquiryId, String customerId);
}
