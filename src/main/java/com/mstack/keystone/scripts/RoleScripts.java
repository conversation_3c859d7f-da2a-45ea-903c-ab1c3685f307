package com.mstack.keystone.scripts;

import com.mstack.keystone.model.repository.PermissionsGroup;
import com.mstack.keystone.repository.PermissionsGroupRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;

@Service
public class RoleScripts {
    @Autowired
    PermissionsGroupRepository permissionsGroupRepository;

    public void updateRole() {
        PermissionsGroup permissionsGroup = permissionsGroupRepository.findByPermissionsGroupName("FulfilmentExecutive");
        HashSet<String> combinedPermisons = new HashSet<>(permissionsGroup.getPermissions());
        List<String> updatedPermission = List.of("VIEW_CUSTOMER",
                "CREATE_SUPPLIER",
                "VIEW_SUPPLIER",
                "CREATE_ORDER_BOOK",
                "UPDATE_ORDER_BOOK",
                "VIEW_ORDER_BOOK",
                "SUPPLIER_CREATE_ORDER_BOOK",
                "SUPPLIER_UPDATE_ORDER_BOOK",
                "SUPPLIER_VIEW_ORDER_BOOK",
                "CREATE_CUSTOMER_ORDER",
                "UPDATE_CUSTOMER_ORDER",
                "VIEW_CUSTOMER_ORDER",
                "UPDATE_DRAFT_DOCUMENT_CUSTOMER_ORDER",
                "CREATE_SUPPLIER_ORDER",
                "UPDATE_SUPPLIER_ORDER",
                "VIEW_SUPPLIER_ORDER",
                "CREATE_PRODUCT",
                "VIEW_PRODUCT",
                "VIEW_CRITICAL_PATH",
                "CREATE_TASK",
                "UPDATE_TASK",
                "GENERATE_CRITICAL_PATH",
                "UPDATE_CRITICAL_PATH",
                "GET_CONFIG",
                "UPLOAD_FILE",
                "DOWNLOAD_FILE",
                "VIEW_EMPLOYEE",
                "CREATE_PERMISSIONS_GROUP",
                "VIEW_PERMISSIONS_GROUP",
                "UPLOAD_SUPPLIER_DOCUMENT",
                "CREATE_ENQUIRY",
                "UPDATE_ENQUIRY",
                "VIEW_ENQUIRY",
                "ASSIGN_ENQUIRY",
                "APPROVE_ENQUIRY",
                "NEGOTIATE_ENQUIRY",
                "CREATE_QUOTATION",
                "APPROVE_QUOTATION",
                "VIEW_CUSTOMER_ENQUIRY",
                "GET_QUOTATION_HISTORY",
                "VIEW_USER_CONFIG",
                "CONVERT_HTML_TO_PDF",
                "CREATE_DOC_ENGINE_TEMPLATE",
                "VIEW_DOC_ENGINE_TEMPLATE",
                "UPDATE_DOC_ENGINE_TEMPLATE",
                "CREATE_DOC_META",
                "VIEW_DOC_META",
                "UPDATE_DOC_META",
                "CREATE_INVOICE",
                "GET_INVOICE",
                "CREATE_INVENTORY",
                "UPDATE_INVENTORY",
                "VIEW_INVENTORY",
                "SEARCH_INVENTORY",
                "CREATE_INVENTORY_PRODUCT",
                "UPDATE_INVENTORY_PRODUCT",
                "VIEW_INVENTORY_PRODUCT",
                "SEARCH_INVENTORY_PRODUCT",
                "CREATE_PACKAGING",
                "UPDATE_PACKAGING",
                "VIEW_PACKAGING",
                "SEARCH_PACKAGING",
                "CREATE_PRODUCT_BATCH",
                "UPDATE_PRODUCT_BATCH",
                "VIEW_PRODUCT_BATCH",
                "RESET_BATCH",
                "VIEW_SUPPLIER_TAB",
                "VIEW_PRODUCT_TAB",
                "VIEW_CUSTOMER_TAB",
                "VIEW_SUPPLIER_ORDERBOOK_TAB",
                "VIEW_SUPPLIER_DISPATCH_TAB",
                "VIEW_CUSTOMER_ORDERBOOK_TAB",
                "VIEW_CUSTOMER_DISPATCH_TAB",
                "VIEW_ENQUIRY_TAB",
                "VIEW_ENQUIRY_TAB");
        combinedPermisons.addAll(updatedPermission);
        permissionsGroup.setPermissions(combinedPermisons.stream().toList());
        permissionsGroupRepository.save(permissionsGroup);
    }
}
