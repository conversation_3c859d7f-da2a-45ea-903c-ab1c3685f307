package com.mstack.keystone.scripts;

import com.mstack.keystone.config.RequestConfig;
import com.mstack.keystone.constants.AppConstants;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.docEngine.TemplatePlaceholder;
import com.mstack.keystone.model.dto.report.GenerateReportRequest;
import com.mstack.keystone.model.repository.Employee;
import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.User;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.user.EmployeeRepository;
import com.mstack.keystone.repository.user.UserRepository;
import com.mstack.keystone.service.aws.AwsSesService;
import com.mstack.keystone.service.docEngine.DocEngineTemplateService;
import com.mstack.keystone.service.docEngine.DocMetaService;
import com.mstack.keystone.service.order.OrderBookService;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import com.mstack.keystone.utils.CommonUtils;
import com.opencsv.CSVWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.mstack.keystone.constants.AppConstants.ANALYTICS_GROUP_MAIL_ID;

@Service
@Slf4j
public class InvoiceScripts {
    @Autowired
    CustomerOrderService customerOrderService;

    @Autowired
    EmployeeRepository employeeRepository;

    @Autowired
    OrderBookService orderBookService;

    @Autowired
    DocMetaService docMetaService;

    @Autowired
    DocEngineTemplateService docEngineTemplateService;
    @Autowired
    CommonUtils commonUtils;


    @Autowired
    UserRepository userRepository;

    @Autowired
    AwsSesService awsSesService;

    @Autowired
    CustomerOrderRepository customerOrderRepository;

    @Value("${spring.profiles.active}")
    String envType;


    public void generateMstackInvoiceReportCsv(GenerateReportRequest generateReportRequest) throws IOException {
        User user = userRepository.findByEntityId(generateReportRequest.getUserId());
        String filePath = "mstack_invoice_report.csv";
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = getMstackInvoiceHeaderKeys();
        String[] orderedHeaderKeys = getMstackInvoiceOrderedHeaderKeys();
        writer.writeNext(orderedHeaderKeys);
        List<CustomerOrder> customerOrders;
        if(generateReportRequest.getDateType().equals( "PO_DATE")) {
            customerOrders = customerOrderRepository.findByPoDateBetween(generateReportRequest.getFrom(), generateReportRequest.getTo());
        } else {
            customerOrders = customerOrderRepository.findByInvoiceDateBetween(generateReportRequest.getFrom(), generateReportRequest.getTo());
        }

        for (CustomerOrder customerOrder : customerOrders) {
            List<DocMeta> docMetaList = docMetaService.findByCustomerOrderIdAndDocType(customerOrder.getId(), "MSTACK_INVOICE");
            if (docMetaList.isEmpty()) continue;

            HashMap<String, Object> request = new HashMap<>();
            request.put("docType", "MSTACK_INVOICE");
            request.put("meta", new HashMap<>(
                    Map.ofEntries(
                            Map.entry("customerOrderId", customerOrder.getId()),
                            Map.entry("requestedBy", "system")
                    )
            ));
            List<TemplatePlaceholder> templatePlaceholders = null;
            try {
                templatePlaceholders = docEngineTemplateService.aggregateDataForDocument(request);
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
                continue;
            }
            Map<String, TemplatePlaceholder> templateMap = getTemplateMapFromList(templatePlaceholders);
            if (!templateMap.containsKey("PRODUCTS_DATA")) continue;
            List<HashMap<String, Object>> productsData = (List<HashMap<String, Object>>) templateMap.get("PRODUCTS_DATA").getValue();
            for (HashMap<String, Object> productData : productsData) {
                HashMap<String, String> map = new HashMap<>();
                map.put("INVOICE_STATE", docMetaList.get(0).getState());
                map.put("INVOICE_STATUS", docMetaList.get(0).getStatus() == null ? "NA" : docMetaList.get(0).getStatus().toString());
                map.put("INVOICE_CREATED_BY", getEmployeeName(docMetaList.get(0).getCreatedBy()));
                map.put("INVOICE_CREATED_AT",
                        customerOrder.getInvoiceDate() != null
                                ? commonUtils.formatDate(customerOrder.getInvoiceDate())
                                : commonUtils.formatDate(docMetaList.get(0).getCreatedAt())
                );
                map.put("IS_APPROVED", docMetaList.get(0).isApproved() ? "YES" : "NO");
                map.put("ORDER_BOOK_NUMBER", orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId()).getOrderBookId());
                map.put("PURCHASE_ORDER_NUMBER", customerOrder.getPurchaseOrderNumber());
                if (customerOrder.getPurchaseOrderDate() != null) {
                    try {
                        map.put("PURCHASE_ORDER_DATE", commonUtils.formatDate(customerOrder.getPurchaseOrderDate()));
                    } catch (Exception ex) {
                        log.error(ex.getMessage());
                    }
                }
                map.put("CUSTOMER_ORDER_NUMBER", customerOrder.getOrderId());
                map.put("CUSTOMER_NAME", customerOrder.getCustomer().getName());
                map.put("ACCOUNT_OWNER", customerOrder.getCustomer().getAccountOwner());
                map.put("ITEM", productData.get("ITEM").toString());
                map.put("PACKAGING", productData.get("PACKAGING").toString());
                map.put("QUANTITY", productData.get("QUANTITY_VALUE").toString());
                map.put("UOM", productData.get("QUANTITY_UOM").toString());
                map.put("UNIT_PRICE", productData.get("UNIT_PRICE").toString());
                map.put("TOTAL_PRODUCT_PRICE", productData.get("TOTAL_PRICE").toString());
                for (int i = 15; i < headerKeys.length; i++) {
                    String key = headerKeys[i];
                    if (templateMap.containsKey(key)) {
                        if (templateMap.get(key).getValue() != null) {
                            map.put(key, templateMap.get(key).getValue().toString());
                        } else map.put(key, "");
                    } else map.put(key, "");
                }
                writer.writeNext(getHeaderWiseValues(map, orderedHeaderKeys));
            }
        }
        writer.close();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        Date from = generateReportRequest.getFrom();
        Date to = generateReportRequest.getTo();
        try {
            awsSesService.sendEmailWithAttachments(
                    "<EMAIL>",
                    List.of(user.getUsername()),
                    "Regarding " + generateReportRequest.getReportType() + " report from date " + dateFormat.format(from) + " to " + dateFormat.format(to),
                    "PFA report from date " + dateFormat.format(from) + " to " + dateFormat.format(to) + " ",
                    List.of(file.getAbsolutePath())
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }

//        // un comment to delete file after processing
        if (file.exists()) {
            file.delete();
        }

    }

    // @Scheduled(cron = "${invoiceScripts.cron.expression}")
    public void generateMstackInvoiceDailyReportCsv() throws IOException {
        String filePath = "mstack_daily_invoice_report.csv";
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));
        String[] headerKeys = getMstackInvoiceHeaderKeys();
        String[] orderedHeaderKeys = getMstackInvoiceOrderedHeaderKeys();
        writer.writeNext(orderedHeaderKeys);
        List<CustomerOrder> customerOrders = customerOrderRepository.findAllNonDeletedOrders();
        for (CustomerOrder customerOrder : customerOrders) {
            HashMap<String, Object> request = new HashMap<>();
            List<DocMeta> docMetaList = docMetaService.findByCustomerOrderIdAndDocType(customerOrder.getId(), "MSTACK_INVOICE");
            if (docMetaList.isEmpty()) continue;
            request.put("docType", "MSTACK_INVOICE");
            request.put("meta", new HashMap<>(
                    Map.ofEntries(
                            Map.entry("customerOrderId", customerOrder.getId())
                    )
            ));
            List<TemplatePlaceholder> templatePlaceholders = null;
            try {
                templatePlaceholders = docEngineTemplateService.aggregateDataForDocument(request);
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
                continue;
            }
            Map<String, TemplatePlaceholder> templateMap = getTemplateMapFromList(templatePlaceholders);
            if (!templateMap.containsKey("PRODUCTS_DATA")) continue;
            List<HashMap<String, Object>> productsData = (List<HashMap<String, Object>>) templateMap.get("PRODUCTS_DATA").getValue();
            for (HashMap<String, Object> productData : productsData) {
                DocMeta temp = docMetaList.get(0);
                if (temp.getLastUpdatedAt() == null) {
                    if (temp.getCreatedAt() != null && commonUtils.isMoreThan24Hours(temp.getCreatedAt())) continue;
                } else if (commonUtils.isMoreThan24Hours(temp.getLastUpdatedAt())) continue;
                HashMap<String, String> map = new HashMap<>();
                map.put("INVOICE_STATE", docMetaList.get(0).getState());
                map.put("INVOICE_STATUS", temp.getStatus() == null ? "NA" : temp.getStatus().toString());
                map.put("INVOICE_CREATED_BY", getEmployeeName(docMetaList.get(0).getCreatedBy()));
                map.put("INVOICE_CREATED_AT", commonUtils.formatDate(docMetaList.get(0).getCreatedAt()));
                map.put("IS_APPROVED", docMetaList.get(0).isApproved() ? "YES" : "NO");
                map.put("ORDER_BOOK_NUMBER", orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId()).getOrderBookId());
                map.put("PURCHASE_ORDER_NUMBER", customerOrder.getPurchaseOrderNumber());
                if (customerOrder.getPurchaseOrderDate() != null) {
                    try {
                        map.put("PURCHASE_ORDER_DATE", commonUtils.formatDate(customerOrder.getPurchaseOrderDate()));
                    } catch (Exception ex) {
                        log.error(ex.getMessage());
                    }
                }
                map.put("CUSTOMER_ORDER_NUMBER", customerOrder.getOrderId());
                map.put("CUSTOMER_NAME", customerOrder.getCustomer().getName());
                map.put("ACCOUNT_OWNER", customerOrder.getCustomer().getAccountOwner());
                map.put("ITEM", productData.get("ITEM").toString());
                map.put("PACKAGING", productData.get("PACKAGING").toString());
                map.put("QUANTITY", productData.get("QUANTITY_VALUE").toString());
                map.put("UOM", productData.get("QUANTITY_UOM").toString());
                map.put("UNIT_PRICE", productData.get("UNIT_PRICE").toString());
                map.put("TOTAL_PRODUCT_PRICE", productData.get("TOTAL_PRICE").toString());
                for (int i = 15; i < headerKeys.length; i++) {
                    String key = headerKeys[i];
                    if (templateMap.containsKey(key)) {
                        if (templateMap.get(key).getValue() != null) {
                            map.put(key, templateMap.get(key).getValue().toString());
                        } else map.put(key, "");
                    } else map.put(key, "");
                }
                writer.writeNext(getHeaderWiseValues(map, orderedHeaderKeys));
            }
        }
        writer.close();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        Date date = new Date();
        String sendersAddress = envType.equalsIgnoreCase("dev") || envType.equalsIgnoreCase("local") ? "<EMAIL>" : ANALYTICS_GROUP_MAIL_ID;
        try {
            awsSesService.sendEmailWithAttachments(
                    "<EMAIL>",
                    List.of(sendersAddress),
                    "Daily Mstack invoice report " + dateFormat.format(date),
                    "PFA mstack invoice report ",
                    List.of(file.getAbsolutePath())
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }

        // un comment to delete file after processing
        if (file.exists()) {
            file.delete();
        }

    }

    public void generateChemstackInvoiceReportCsv(GenerateReportRequest generateReportRequest) throws IOException {
        User user = userRepository.findByEntityId(generateReportRequest.getUserId());

        String filePath = "chemstack_invoice_report.csv";
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));

        String[] headerKeys = getChemstackInvoiceOrderedHeaderKeys();
        String[] orderedHeaderKeys = getChemstackInvoiceOrderedHeaderKeys();

        writer.writeNext(orderedHeaderKeys);
        List<CustomerOrder> customerOrders;
        if(generateReportRequest.getDateType().equals( "PO_DATE")) {
            customerOrders = customerOrderRepository.findByPoDateBetween(generateReportRequest.getFrom(), generateReportRequest.getTo());
        } else {
            customerOrders = customerOrderRepository.findByChemstackInvoiceDateBetween(generateReportRequest.getFrom(), generateReportRequest.getTo());
        }
        for (CustomerOrder customerOrder : customerOrders) {
            List<DocMeta> docMetaList = docMetaService.findByCustomerOrderIdAndDocType(customerOrder.getId(), "CHEMSTACK_TAX_INVOICE");
            if (docMetaList.isEmpty()) continue;
            HashMap<String, Object> request = new HashMap<>();
            request.put("docType", "CHEMSTACK_TAX_INVOICE");
            request.put("meta", new HashMap<>(
                    Map.ofEntries(
                            Map.entry("customerOrderId", customerOrder.getId()),
                            Map.entry("requestedBy", "system")
                    )
            ));
            List<TemplatePlaceholder> templatePlaceholders = null;
            try {
                templatePlaceholders = docEngineTemplateService.aggregateDataForDocument(request);
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
                continue;
            }
            Map<String, TemplatePlaceholder> templateMap = getTemplateMapFromList(templatePlaceholders);
            HashMap<String, String> map = new HashMap<>();
            map.put("INVOICE_STATE", docMetaList.get(0).getState());
            map.put(
                    "INVOICE_CREATED_AT",
                    customerOrder.getChemstackInvoiceDate() != null
                            ? commonUtils.formatDate(customerOrder.getChemstackInvoiceDate())
                            : commonUtils.formatDate(docMetaList.get(0).getCreatedAt())
            );
            map.put("INVOICE_CREATED_BY", String.valueOf(docMetaList.get(0).getCreatedBy()));
            map.put("VESSEL_FLIGHT_NO", customerOrder.getVesselNumber());
//            map.put("ORDER_BOOK_NUMBER", orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId()).getOrderBookId());
            OrderBook orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
            String orderBookId = "";
            if (orderBook != null){
                orderBookId = orderBook.getOrderBookId();
            }
            map.put("ORDER_BOOK_NUMBER",orderBookId );

            map.put("INVOICE_STATUS", docMetaList.get(0).getStatus() == null ? "NA" : docMetaList.get(0).getStatus().toString());

            Object productOriginData = templateMap.get("PRODUCT_ORIGIN_DATA").getValue();
            Object value = ((HashMap<?, ?>) ((ArrayList<?>) productOriginData).get(0)).get("PRODUCT");
            map.put("PRODUCT_NAME", String.valueOf(value));
            map.put("IS_APPROVED", docMetaList.get(0).isApproved() ? "YES" : "NO");

            map.put("PURCHASE_ORDER_NUMBER", customerOrder.getPurchaseOrderNumber());
            if (customerOrder.getPurchaseOrderDate() != null) {
                try {
                    map.put("PURCHASE_ORDER_DATE", commonUtils.formatDate(customerOrder.getPurchaseOrderDate()));
                } catch (Exception ex) {
                    log.error(ex.getMessage());
                }
            }
            map.put("CUSTOMER_ORDER_NUMBER", customerOrder.getOrderId());
            map.put("CUSTOMER_NAME", customerOrder.getCustomer().getName());
            map.put("ACCOUNT_OWNER", customerOrder.getCustomer().getAccountOwner());


            for (int i = 0; i < headerKeys.length; i++) {
                String key = headerKeys[i];
                if (templateMap.containsKey(key)) {
                    if (templateMap.get(key).getValue() != null) {
                        map.put(key, templateMap.get(key).getValue().toString());
                    } else {
                        if(map.containsKey("INVOICE_CREATED_AT") || map.containsKey("CUSTOMER_NAME") || map.containsKey("PRODUCT_NAME") ||
                                map.containsKey("INVOICE_STATE")||map.containsKey("INVOICE_CREATED_BY") || map.containsKey("ORDER_BOOK_NUMBER")
                                || map.containsKey("INVOICE_STATUS") || map.containsKey("VESSEL_FLIGHT_NO") ) continue;
                        map.put(key, "");

                    }

                }
            }
            writer.writeNext(getHeaderWiseValues(map, orderedHeaderKeys));


        }
        writer.close();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        Date from = generateReportRequest.getFrom();
        Date to = generateReportRequest.getTo();
        try {
            awsSesService.sendEmailWithAttachments(
                    "<EMAIL>",
                    List.of(user.getUsername()),
                    "Regarding " + generateReportRequest.getReportType() + " report from date " + dateFormat.format(from) + " to " + dateFormat.format(to),
                    "PFA report from date " + dateFormat.format(from) + " to " + dateFormat.format(to) + " ",
                    List.of(file.getAbsolutePath())
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }

//        // un comment to delete file after processing
        if (file.exists()) {
            file.delete();
        }

    }


    //Shipment Created On	Consignee Details	Notify Party Details
    // PO Advance (in %)	PO Payment Terms	Credit amount(in %)	Credit Days	Currency Type	Tax Percentage	Category
    // IGST Amount	Pallet Wt (Chemstack)	Pallet Wt (Mstack)	Marks & Containers
    // Delivery Address	Remarks	Product Name	Unit of Measurement	Qty (Total Weight)	Price per unit





    public void generateSalesOrderReportCsv(GenerateReportRequest request) throws IOException {
        generateOrderReport(request, "salesorder_report.csv", false);
    }


    public void generateOpenSalesOrderReportCsv(GenerateReportRequest request) throws IOException {
        generateOrderReport(request, "opensalesorder_report.csv", true);
    }


    public void generateOrderReport(GenerateReportRequest generateReportRequest,String filePath, boolean openOrdersOnly) throws IOException {
        User user = userRepository.findByEntityId(generateReportRequest.getUserId());
        File file = new File(filePath);
        CSVWriter writer = new CSVWriter(new FileWriter(filePath));

        String[] headerKeys = getSalesOrderInvoiceOrderedHeaderKeys();
        String[] orderedHeaderKeys = getSalesOrderInvoiceOrderedHeaderKeys();

        writer.writeNext(orderedHeaderKeys);
        // Use repository method that fetches only required records
        List<CustomerOrder> customerOrders = openOrdersOnly ?
                customerOrderRepository.findOpenOrdersByPoDateBetween(generateReportRequest.getFrom(), generateReportRequest.getTo()) :
                customerOrderRepository.findByPoDateBetween(generateReportRequest.getFrom(), generateReportRequest.getTo());
        for (CustomerOrder customerOrder : customerOrders) {
            HashMap<String, String> map = new HashMap<>();
            for (OrderedProduct custProduct: customerOrder.getProducts()){
                map.put("Shipment Created On", String.valueOf(customerOrder.getShipmentCreatedAtDate()));
                map.put("Consignee Details", customerOrder.getConsignee());
                map.put("Notify Party Details", customerOrder.getNotifyParty());

                // Safely handle potential null value for paymentTerms
                if (customerOrder.getPaymentTerms() != null) {
                    map.put("PO Advance", String.valueOf(customerOrder.getPaymentTerms().getAdvanceAmount()));
                    map.put("PO Payment Terms", String.valueOf(customerOrder.getPaymentTerms()));
                    map.put("Credit amount", String.valueOf(customerOrder.getPaymentTerms().getCreditAmount()));
                    map.put("Credit Days", String.valueOf(customerOrder.getPaymentTerms().getCreditorDays()));
                } else {
                    // Set default values for null PaymentTerms
                    map.put("PO Advance", "N/A");
                    map.put("PO Payment Terms", "N/A");
                    map.put("Credit amount", "N/A");
                    map.put("Credit Days", "N/A");
                }

                map.put("Currency Type", customerOrder.getBuyerCurrency());

                OrderBook orderBook = orderBookService.getByPONumberAndCustomerId(customerOrder.getPurchaseOrderNumber(), customerOrder.getCustomer().getId());
                String taxPercent = "";
                if (orderBook != null){
                    taxPercent = String.valueOf(orderBook.getTaxPercent());
                }

                map.put("Tax Percentage", taxPercent);
                map.put("Category",
                        Optional.ofNullable(custProduct)
                                .map(OrderedProduct::getProduct)
                                .map(Product::getCategories)
                                .filter(categories -> !categories.isEmpty())
                                .map(categories -> categories.get(0))
                                .map(category -> category.get("category"))
                                .orElse("Unknown Category"));

                map.put("IGST Amount", String.valueOf(customerOrder.getIgstAmt()));
                map.put("Pallet Wt (Chemstack)", String.valueOf(customerOrder.getPalletWt()));
                map.put("Pallet Wt (Mstack)", String.valueOf(customerOrder.getMstackPalletWt()));
                map.put("Marks & Containers", customerOrder.getMarksAndContainers());
                map.put("Delivery Address", customerOrder.getDeliveryAddress());

                if (custProduct != null) {
                    Product product = custProduct.getProduct();
                    map.put("Product Name", Optional.ofNullable(product)
                            .map(Product::getTechnicalName)
                            .orElse(""));
                    map.put("Unit of Measurement", String.valueOf(custProduct.getUom()));
                    map.put("Qty (Total Weight)", String.valueOf(custProduct.getQuantity()));
                    map.put("Price per unit", String.valueOf(custProduct.getUnits()));
                    map.put("Packaging Details", String.valueOf(custProduct.getPackaging()));
                    map.put("Total number of packaging units", String.valueOf(custProduct.getUnits()));
                    map.put("Quantity per packaging type", String.valueOf(custProduct.getQuantityPerUnit()));
                    map.put("Chemstack Price per unit", String.valueOf(custProduct.getPrice()));
                    map.put("Per Unit Kg value (for chemstack invoice)", String.valueOf(custProduct.getPerUnitKgValue()));
                    map.put("HSN code", String.valueOf(custProduct.getHsCode()));
                    map.put("Product Name Alias", custProduct.getProductNameAlias());
                    map.put("Country Of Origin", custProduct.getCountryOfOrigin());
                    map.put("Label Description", String.valueOf(custProduct.getLabel()));
                    map.put("Batch Date visibility", String.valueOf(custProduct.getBatchDateVisibilty()));
                }
            }


            writer.writeNext(getHeaderWiseValues(map, orderedHeaderKeys));
        }
        writer.close();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        Date from = generateReportRequest.getFrom();
        Date to = generateReportRequest.getTo();
        try {
            awsSesService.sendEmailWithAttachments(
                    "<EMAIL>",
                    List.of(user.getUsername()),
                    "Regarding " + generateReportRequest.getReportType() + " report from date " + dateFormat.format(from) + " to " + dateFormat.format(to),
                    "PFA report from date " + dateFormat.format(from) + " to " + dateFormat.format(to) + " ",
                    List.of(file.getAbsolutePath())
            );
        } catch (Exception ex) {
            System.out.println("Expection occured while sending mail:  " + ex.getMessage());
        }

// un comment to delete file after processing
        if (file.exists()) {
            file.delete();
        }
    }




    private String[] getHeaderWiseValues(HashMap<String, String> map, String[] orderedHeaderKeys) {
        ArrayList<String> arr = new ArrayList<>();
        for (String headerKey : orderedHeaderKeys) {
            arr.add(map.getOrDefault(headerKey, ""));
        }
        String[] resp = new String[arr.size()];
        for (int i = 0; i < arr.size(); i++) resp[i] = arr.get(i);
        return resp;
    }


    private String[] getMstackInvoiceOrderedHeaderKeys() {
        return new String[]{
                "ORDER_BOOK_NUMBER",
                "CUSTOMER_ORDER_NUMBER",
                "PURCHASE_ORDER_NUMBER",
                "PURCHASE_ORDER_DATE",
                "INVOICE_NUMBER",
                "INVOICE_STATE",
                "INVOICE_STATUS",
                "INVOICE_DATE",
                "CUSTOMER_NAME",
                "ACCOUNT_OWNER",
                "ITEM",
                "UOM",
                "QUANTITY",
                "UNIT_PRICE",
                "TOTAL_PRODUCT_PRICE",
                "TOTAL_INVOICE_AMOUNT",
                "CURRENCY",
                "PACKAGING",
                "CREDITOR_DAYS",
                "CREDITOR_AMOUNT",
                "PAYMENT_TERMS_BASED_ON",
                "TERMS",
                "DELIVERY_DATE",
                "BILL_OF_LADING_DATE",
                "BILL_OF_LADING_NUMBER",
                "DUE_DATE",
                "INVOICE_CREATED_AT",
                "INVOICE_CREATED_BY",
                "IS_APPROVED",
                "INVOICE_APPROVED_BY",
                "INCOTERM_TYPE",
                "INCOTERM",
                "SHIPING_ADDRESS",
                "BILLING_ADDRESS",
                "TOTAL_IN_WORDS",
                "COUNTRY_OF_ORIGIN",
                "FREIGHT_TERM",
                "PORT_OF_LOADING",
                "PORT_OF_DISCHARGE",
                "FREIGHT_COST",
                "INSURANCE_COST",
                "FOB_COST",
                "ACCOUNT_NAME",
                "ACCOUNT_NUMBER",
                "BANK",
                "BANK_ADDRESS",
                "SWIFT_CODE",
                "ACH_ROUTING_NUMBER",
                "MSTACK_ADDRESS"
        };

    }

    private String getEmployeeName(String createdBy) {
        if (createdBy == null || createdBy.isBlank()) return "";
        Employee employee = employeeRepository.findById(createdBy).orElse(null);
        if (employee == null) {
            System.out.println("Invoice created from invalid mail id " + createdBy);
            return "";
        }
        return employee.getName();
    }

    private static String[] getMstackInvoiceHeaderKeys() {
        return new String[]{
                "INVOICE_STATE",
                "INVOICE_CREATED_BY",
                "INVOICE_CREATED_AT",
                "IS_APPROVED",
                "ORDER_BOOK_NUMBER",
                "PURCHASE_ORDER_NUMBER",
                "CUSTOMER_ORDER_NUMBER",
                "CUSTOMER_NAME",
                "ACCOUNT_OWNER",
                "ITEM",
                "PACKAGING",
                "QUANTITY",
                "UOM",
                "UNIT_PRICE",
                "TOTAL_PRODUCT_PRICE"
                , "INVOICE_NUMBER"
                , "INVOICE_DATE"
                , "CURRENCY"
                , "TERMS"
                , "DUE_DATE"
                , "INCOTERM",
                "INCOTERM_TYPE"
                , "COUNTRY_OF_ORIGIN"
                , "FREIGHT_TERM"
                , "SHIPING_ADDRESS"
                , "PORT_OF_LOADING"
                , "PORT_OF_DISCHARGE"
                , "FREIGHT_COST"
                , "INSURANCE_COST"
                , "FOB_COST"
                , "TOTAL_IN_WORDS"
                , "TOTAL_INVOICE_AMOUNT"
                , "ACCOUNT_NAME"
                , "ACCOUNT_NUMBER"
                , "BANK"
                , "BANK_ADDRESS"
                , "SWIFT_CODE"
                , "ACH_ROUTING_NUMBER",
                "MSTACK_ADDRESS"
                , "BILLING_ADDRESS",
                "BILL_OF_LADING_DATE",
                "BILL_OF_LADING_NUMBER",
                "DELIVERY_DATE",
                "CREDITOR_DAYS",
                "CREDITOR_AMOUNT",
                "PAYMENT_TERMS_BASED_ON"
        };
    }


    private String[] getChemstackInvoiceOrderedHeaderKeys() {
        return new String[]{
                "INVOICE_CREATED_AT",
                "INVOICE_DATE",
                "INVOICE_NUMBER",
                "CUSTOMER_NAME",
                "CONSIGNEE_ADDRESS",
                "PRODUCT_NAME",
                "TOTAL_BEFORE_GST",
                "IGST",
                "TOTAL_AFTER_GST",
                "TOTAL_IN_WORDS",
                "ARN_NUMBER",
                "MOBILE_NUMBER",
                "TOTAL_IN_INR",
                "SIGNATURE_1",
                "DELIVERY_TERMS",
                "COUNTRY_OF_ORIGIN_OF_GOODS",
                "IS_APPROVED",
                "INVOICE_STATUS",
                "ACCOUNT_OWNER",
                "GSTIN_NO",
                "VESSEL_FLIGHT_NO",
                "PLACE_OF_DELIVERY",
                "STATE_OF_ORIGIN",
                "CHEMSTACK_LOGO",
                "CHEMSTACK_ADDRESS",
                "DELIVERY_DATE",
                "CIN_NO",
                "CUSTOMER_ORDER_NUMBER",
                "INVOICE_CREATED_BY",
                "COUNTRY_OF_FINAL_DESTINATION",
                "DOCUMENT_NAME",
                "PURCHASE_ORDER_DATE",
                "DISTRICT_OF_ORIGIN",
                "PORT_OF_LOADING",
                "PURCHASE_ORDER_NUMBER",
                "NOTIFY_PARTY",
                "TOTAL",
                "COUNTRY_OF_DESTINATION",
                "TABLE_DATA",
                "ORDER_BOOK_NUMBER",
                "PAYMENT_TERM",
                "PORT_OF_DISCHARGE",
                "INVOICE_STATE",
                "PRECARRIAGE",
                "CONTAINER_NUMBER",
                "TRADE_CODE",
                "PAN_NO"
        };

    }


    //Shipment Created On	Consignee Details	Notify Party Details
    // PO Advance (in %)	PO Payment Terms	Credit amount(in %)	Credit Days	Currency Type	Tax Percentage	Category
    // IGST Amount	Pallet Wt (Chemstack)	Pallet Wt (Mstack)	Marks & Containers
    // Delivery Address	Remarks	Product Name	Unit of Measurement	Qty (Total Weight)	Price per unit
    private String[] getSalesOrderInvoiceOrderedHeaderKeys() {
        return new String[]{
                "Shipment Created On",
                "Consignee Details",
                "Notify Party Details",
                "PO Advance",
                "PO Payment Terms",
                "Credit amount",
                "Credit Days",
                "Currency Type",
                "Tax Percentage",
                "Category",
                "IGST Amount",
                "Pallet Wt (Chemstack)",
                "Pallet Wt (Mstack)",
                "Marks & Containers",
                "Delivery Address",
                "Product Name",
                "Unit of Measurement",
                "Qty (Total Weight)",
                "Price per unit",
                "Packaging Details",
                "Total number of packaging units",
                "Quantity per packaging type",
                "Chemstack Price per unit",
                "Per Unit Kg value (for chemstack invoice)",
                "HSN code",
                "Product Name Alias",
                "Country Of Origin",
                "Label Description",
                "Batch Date visibility"

        };

    }

    private Map<String, TemplatePlaceholder> getTemplateMapFromList(List<TemplatePlaceholder> templatePlaceholders) {
        Map<String, TemplatePlaceholder> map = new HashMap<>();
        templatePlaceholders.forEach(templatePlaceholder -> {
            map.put(templatePlaceholder.getKey(), templatePlaceholder);
        });
        return map;
    }


}
