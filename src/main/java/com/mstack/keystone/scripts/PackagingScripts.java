package com.mstack.keystone.scripts;

import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Supplier;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.repository.custom.MongoQueries;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.repository.order.OrderBookRepository;
import com.mstack.keystone.repository.packaging.PackagingRepository;
import com.mstack.keystone.repository.user.SupplierRepository;
import com.opencsv.CSVReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.util.*;

@Service
public class PackagingScripts {
    @Autowired
    PackagingRepository packagingRepository;

    @Autowired
    OrderBookRepository orderBookRepository;

    @Autowired
    CustomerOrderRepository customerOrderRepository;
    @Autowired
    SupplierRepository supplierRepository;

    @Autowired
    MongoQueries mongoQueries;

    public void addPackaging() throws FileNotFoundException {
        String filePath = "PACKAGING.csv";
        File file = new File(filePath);
        CSVReader csvReader = new CSVReader(new FileReader(file));
        for (String[] row : csvReader) {
            Packaging packaging = getPackaging(row);
            mongoQueries.saveEntity(packagingRepository, packaging);
        }
        System.out.println("Packaging added ");
    }

    public void updatePackagingInOrderBook() throws FileNotFoundException {
        String filePath = "ORDER_BOOK.csv";
        File file = new File(filePath);
        CSVReader csvReader = new CSVReader(new FileReader(file));
        for (String[] row : csvReader) {
            OrderBook orderBook = getUpdatedOrderBook(row);
            if (orderBook != null)
                orderBookRepository.save(orderBook);
        }
        System.out.println("order book updated ");
    }

    public void updatePackagingInOrder() throws FileNotFoundException {
        String filePath = "ORDER.csv";
        File file = new File(filePath);
        CSVReader csvReader = new CSVReader(new FileReader(file));
        for (String[] row : csvReader) {
            CustomerOrder order = getUpdatedCustomerOrder(row);
            if (order != null)
                customerOrderRepository.save(order);
        }
        System.out.println("order updated");
    }

    public void updatePackagingInSupplier() throws FileNotFoundException {
        String filePath = "SUPPLIER.csv";
        File file = new File(filePath);
        CSVReader csvReader = new CSVReader(new FileReader(file));
        for (String[] row : csvReader) {
            Supplier supplier = getUpdatedSupplier(row);
            if (supplier != null)
                supplierRepository.save(supplier);
        }
        System.out.println("supplier updated");
    }

    private Supplier getUpdatedSupplier(String[] row) {
//        Supplier Id
        String supplierId = row[0];
//        Product Id
        String productId = row[1];
//        Product Trade Name
        String productTradeName = row[2];
//        Packaging idx
        int packagingIdx = Integer.parseInt(row[3]);
//        Type
        String type = row[4];
//        Pack Size
        String packSize = row[5];
//        Tare Weight
        String tareWeight = row[6];
//        Dimension
        String dimension = row[7];
//        Updated Packaging Name
        String updatedPackagingName = row[8];
        List<Supplier> supplierList = supplierRepository.findBySupplierId(supplierId);
        if (supplierList == null || supplierList.isEmpty()) return null;
        supplierList.get(0).getProducts().forEach(supplierProduct -> {
            if (supplierProduct != null && supplierProduct.getProduct() != null && supplierProduct.getProduct().getTradeName() != null && supplierProduct.getProduct().getTradeName().equals(productTradeName)) {
                Packaging updatedPackaging = packagingRepository.findByPackagingName(updatedPackagingName);
                supplierProduct.getPackaging().set(packagingIdx, updatedPackaging);
            }
        });
        return supplierList.get(0);
    }

    private CustomerOrder getUpdatedCustomerOrder(String[] row) {
        // Customer Order Id
        String coid = row[0];
        // Ordered Product Id
        String productId = row[1];
        // Product Trade Name
        String tradeName = row[2];
        // Type
        String type = row[3];
        // Pack Size
        String packSize = row[4];
        // Tare Weight
        String tareWeight = row[5];
        // Dimension
        String dimension = row[6];
        // Updated Packaging Name
        String updatedPackagingName = row[7];

        List<CustomerOrder> customerOrderList = customerOrderRepository.findListByOrderId(coid);
        if (customerOrderList == null || customerOrderList.isEmpty()) return null;
        customerOrderList.get(0).getProducts().forEach(orderBookEntry -> {
            if (orderBookEntry != null && orderBookEntry.getId() != null && orderBookEntry.getId().equals(productId)) {
                Packaging updatedPackaging = packagingRepository.findByPackagingName(updatedPackagingName);
                orderBookEntry.setPackaging(updatedPackaging);
            }
        });
        return customerOrderList.get(0);

    }

    private OrderBook getUpdatedOrderBook(String[] row) {
        // Customer Order Id
        String COBId = row[0];
        // Ordered Product Id
        String productId = row[1];
        // Product Trade Name
        String tradeName = row[2];
        // Type
        String type = row[3];
        // Pack Size
        String packSize = row[4];
        // Tare Weight
        String tareWeight = row[5];
        // Dimension
        String dimension = row[6];
        // Updated Packaging Name
        String updatedPackagingName = row[7];

        List<OrderBook> orderBookList = orderBookRepository.findByOrderBookId(COBId);
        if (orderBookList == null || orderBookList.isEmpty()) return null;
        orderBookList.get(0).getProducts().forEach(orderBookEntry -> {
            if (orderBookEntry != null && orderBookEntry.getId() != null && orderBookEntry.getId().equals(productId)) {
                Packaging updatedPackaging = packagingRepository.findByPackagingName(updatedPackagingName);
                orderBookEntry.setPackaging(updatedPackaging);
            }
        });
        return orderBookList.get(0);
    }

    private Packaging getPackaging(String[] row) {
        Packaging packaging = new Packaging();
        packaging.setPackagingName(row[0].toUpperCase());  //  Packaging Name
        packaging.setType(row[1].toUpperCase());  // Type
        packaging.setPSize(Double.parseDouble(row[2])); // Pack Size
        packaging.setPUom(getAbbreviation(getUOM(row[3]))); // UOM Pack size
        packaging.setTWeight(Double.parseDouble(row[4])); // Tare Weight
        packaging.setTWeightUom(getAbbreviation(getUOM(row[5]))); // UOM Tare weight
        packaging.setPackSize(packaging.getPSize() + " " + packaging.getPUom()); // Pack Size
        packaging.setTareWeight(packaging.getTWeight() + " " + packaging.getTWeightUom()); // Tare weight
        packaging.setUOM(packaging.getTWeightUom());
        return packaging;
    }

    private UnitOfMeasure getUOM(String s) {
        return switch (s) {
            case "KG" -> UnitOfMeasure.KILOGRAM;
            case "MT" -> UnitOfMeasure.METRIC_TON;
            case "L" -> UnitOfMeasure.LITRE;
            case "KL" -> UnitOfMeasure.KILOLITRE;
            case "GAL" -> UnitOfMeasure.GALLON;
            case "LB" -> UnitOfMeasure.POUND;
            default -> null;
        };
    }

    public String getAbbreviation(UnitOfMeasure unitOfMeasure) {
        return switch (unitOfMeasure) {
            case METRIC_TON -> "MT";
            case POUND -> "LB";
            case GALLON -> "GAL";
            case LITRE -> "L";
            case KILOLITRE -> "KL";
            case KILOGRAM -> "KG";
            default -> "";
        };
    }

}
