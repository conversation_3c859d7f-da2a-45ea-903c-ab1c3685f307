package com.mstack.keystone.model.enums;

public enum CurrencyType {
    DOLLAR("USD","$"), RUPEE("Rupee","₹"), YEN("Yen","¥"),EURO("Euro","€"), AED("AED","AED"), YUAN("Yuan","¥");

    private final String abbreviation;
    private final String sign;
    CurrencyType(String abbreviation,String sign) {
        this.abbreviation = abbreviation;
        this.sign=sign;
    }

    public String getAbbreviation() {
        return abbreviation;
    }
    public String getSign() {
        return sign;
    }
}
