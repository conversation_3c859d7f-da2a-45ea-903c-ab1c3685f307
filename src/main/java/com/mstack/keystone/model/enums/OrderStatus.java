package com.mstack.keystone.model.enums;

public enum OrderStatus {
    INITIATED(10),
    UNDER_PRODUCTION(20),
    PRODUCTION_COMPLETED(30),
    UNDER_TESTING(40),
    MANUFACTURED_AND_TESTED(50),
    DISPATCHED(60),
    DELIVERED(70),
    PAYMENT_COMPLETED(80);

    private final int value;

    OrderStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}