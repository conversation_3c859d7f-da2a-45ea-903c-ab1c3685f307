package com.mstack.keystone.model.enums;

import lombok.Getter;

@Getter
public enum AddressType {
    CHEMSTACK_GUJARAT("Second Floor, Office No. SF-11,12, Omkar Complex,Valia <PERSON>di,GIDC Ankleshwar, Ankleshwar, Bharuch, Gujarat, 393002"),
    CHEMSTACK_MUMBAI("8th Floor, RCity Office, Awfis, Lal Bahadur S<PERSON> Marg, adjoining RCity Mall, Mumbai, Maharashtra, Pin Code 400086"),
    MSTACK("Mstack Inc. , 21st Floor, Wework 2700 Post Oak Blvd Galleria Office Tower I, Houston, TX 77056 | Tel. No.: +91-9726939356 ");

    // Add more addresses as needed

    private final String addressValue;

    AddressType(String addressValue) {
        this.addressValue = addressValue;
    }

}

