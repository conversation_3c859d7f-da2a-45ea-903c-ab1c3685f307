package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.enums.UnitOfMeasure;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class CustomerEnquiry {
    private String id;
    private String enquiryId;
    private String productName;
    private String casNumber;
    private String grade;
    private Date createdAt;
    private String createdBy;
    private String lastUpdatedBy;
    private Date lastUpdatedAt;
    private float quantity;
    private UnitOfMeasure unitOfMeasure;
    private String application;
    private CustomerEnquiryStatus enquiryStatus;
    private float totalCost;
    private int expectedDeliveryTime;
    private IncoTerms enquiryIncoterms;
    private IncoTerms supplierIncoterms;
    private Packaging packaging;
    private List<SupplierDocument> documents;

}
