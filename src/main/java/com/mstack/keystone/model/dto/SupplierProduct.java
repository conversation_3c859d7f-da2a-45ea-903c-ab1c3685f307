package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.enums.SupplierType;
import com.mstack.keystone.model.repository.Product;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

@Data
public class SupplierProduct {

    Product product;
    private List<SupplierDocument> mstackDocuments;
    Address address;
    double capacityAvailable;
    double totalCapacity;
    SupplierType supplierType;
    List<SupplierDocument> documents;
    int leadTime;
    int typicalOrderSize;
    String hsCode;
    boolean hazardous;
    List<String> hazardousLevel;
    List<Packaging> packaging;
    List<SupplierDocument> certificateDocuments;
    List<String> exportApprovedRemarks;
    List<String> dutyRemarks;
}
