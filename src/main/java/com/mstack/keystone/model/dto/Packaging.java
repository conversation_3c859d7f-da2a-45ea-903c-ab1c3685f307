package com.mstack.keystone.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class Packaging {

    String type;
    String packSize;
    String tareWeight;
    String dimension;
    String UOM;
    String id;
    String packagingName;
    Date createdAt;
    Date lastUpdatedAt;
    String createdBy;
    String lastUpdatedBy;
    boolean deleted;
    double pSize;
    String pUom;
    double tWeight;
    String tWeightUom;
    String otherPackagingDetails;



    public void softDelete() {
        this.deleted = true;
    }

    public String convertToString() {
        return "Type: " + type + ", Pack Size: " + packSize + ", Tare Weight: " + tareWeight + ", Dimension: " + dimension;
    }
}
