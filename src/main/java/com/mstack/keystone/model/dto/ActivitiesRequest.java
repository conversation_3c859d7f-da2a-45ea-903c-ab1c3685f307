package com.mstack.keystone.model.dto;

import java.util.Map;
import lombok.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivitiesRequest {

  private String orderId;
  private String entityId;
  private Map<String, Date> dueDateFields;
  private String templateName;
  private String categoryName;
  private String entityName;
  private String secondaryId;
  private String orderType;
  private String category;
  private String customerName;
  private String productName;
  private Boolean poGenrationRequired;
}
