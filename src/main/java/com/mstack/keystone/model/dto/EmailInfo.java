package com.mstack.keystone.model.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data structure for storing email information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailInfo {
    private String salesEmail;
    private String accountEmail;
    private List<String> optionalEmails;
}