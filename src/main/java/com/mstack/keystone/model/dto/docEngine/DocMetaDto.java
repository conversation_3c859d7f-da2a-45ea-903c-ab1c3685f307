package com.mstack.keystone.model.dto.docEngine;

import com.mstack.keystone.model.dto.DocComment;
import com.mstack.keystone.model.dto.Event;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.enums.docEngine.DocStatus;
import com.mstack.keystone.model.repository.docEngine.DocMeta;
import lombok.Data;
import lombok.NonNull;
import org.springframework.data.annotation.Id;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
public class DocMetaDto {
    String id;
    String docType; //BL
    String label;
    List<EntityType> viewers;
    String category;
    String state;
    DocStatus status;
    String mode;
    String fileName; //1234
    String fileId;  //  1234
    EntityType entityType; //Customer ORder
    String entityId; // customerOrder Id
    Date createdAt;
    boolean deleted;
    Date lastUpdatedAt;
    String createdBy;
    String updatedBy;
    String approvalStatus;
    Boolean approved;
    Integer unresolvedComments;
    HashMap meta;
    List<Event> eventsHistory;


    public DocMetaDto(DocMeta other, int comments,boolean approved) {
        this.id = other.getId();
        this.docType = other.getDocType();
        this.label = other.getLabel();
        this.meta=other.getMeta();
        this.mode=other.getMode();
        this.approved=approved;
        this.status = other.getStatus();
        // Copy viewers list
        if (other.getViewers() != null) {
            this.viewers = new ArrayList<>(other.getViewers());
        } else {
            this.viewers = null;
        }

        this.category = other.getCategory();
        this.state = other.getState();
        this.fileName = other.getFileName();
        this.fileId = other.getFileId();
        this.entityType = other.getEntityType();
        this.entityId = other.getEntityId();

        // Copy Date objects
        this.createdAt = (other.getCreatedAt() != null) ? new Date(other.getCreatedAt().getTime()) : null;
        this.lastUpdatedAt = (other.getLastUpdatedAt() != null) ? new Date(other.getLastUpdatedAt().getTime()) : null;

        this.deleted = other.isDeleted();
        this.createdBy = other.getCreatedBy();
        this.unresolvedComments=comments;
        if(approved){
            this.approvalStatus="Approved";
        }
        else if(this.unresolvedComments!=null&&this.unresolvedComments>0){
            this.approvalStatus="In Progress";
        }
        else {
            this.approvalStatus="new";
        }
        this.eventsHistory = other.getEventsHistory();
    }
}
