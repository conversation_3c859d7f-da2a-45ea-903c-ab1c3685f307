package com.mstack.keystone.model.dto;

import java.util.Date;
import java.util.HashMap;

import lombok.Data;

@Data
public class DocComment {
    String id;
    String fileId;
    HashMap<String,Object> content;
    HashMap<String,Object> position;
    HashMap<String,Object> comment;
    Date createdAt;
    String createdBy;
    String lastUpdatedBy;
    Date lastUpdatedAt;
    String entityType;
    boolean deleted;
    boolean resolved;


}