package com.mstack.keystone.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
@JsonSerialize
@JsonDeserialize
public class Address {

    private String street;
    private String city;
    private String state;
    private String postalCode;
    private String country;

    public String getAddressAsString() {
        String addr = "";
        if (street != null && !street.isBlank())
            addr += street + ", ";
        if (city != null && !city.isBlank())
            addr += city + ", ";
        if (state != null && !state.isBlank())
            addr += state + ", ";
        if (postalCode != null && !postalCode.isBlank())
            addr += "pincode : " + postalCode + ", ";
        if (country != null && !country.isBlank())
            addr += country;
        return addr;
    }
}
