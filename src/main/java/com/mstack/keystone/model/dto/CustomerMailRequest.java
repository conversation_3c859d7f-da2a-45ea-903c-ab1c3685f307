package com.mstack.keystone.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CustomerMailRequest {
    private String customerId;
    private List<String> selectedOrderBookIds;
    private List<String> selectedDispatchOrderIds;
    private String mailTemplate;
    private List<String> tos;
    private List<String> ccs;
    private Map<String, List<String>> attachments;
    private String salesEmail;
    private String accountEmail;
    private List<String> optionalEmails;
}
