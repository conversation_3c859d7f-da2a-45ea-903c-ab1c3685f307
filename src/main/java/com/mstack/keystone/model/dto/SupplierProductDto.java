package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.repository.Supplier;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierProductDto {

    String id;
    String supplierId;
    String name;
    String email;
    String mobile;
    Address address;
    SupplierProduct product;
    float totalCapacity;
    float revenue;

    public SupplierProductDto(Supplier supplier,String productId) {
        this.id = supplier.getId();
        this.supplierId = supplier.getSupplierId();
        this.name = supplier.getName();
        this.email = supplier.getEmail();
        this.mobile = supplier.getMobile();
        this.address = supplier.getAddress();
        for (SupplierProduct supplierProduct: supplier.getProducts()) {
            if(supplierProduct.getProduct().getId().equals(productId)){
                this.product=supplierProduct;
                this.product.setProduct(null);
            }
        }
        this.totalCapacity = supplier.getTotalCapacity();
        this.revenue = supplier.getRevenue();
    }

    public SupplierProductDto(Supplier supplier,String productId,boolean product) {
        this.id = supplier.getId();
        this.supplierId = supplier.getSupplierId();
        this.name = supplier.getName();
        this.email = supplier.getEmail();
        this.mobile = supplier.getMobile();
        this.address = supplier.getAddress();
        for (SupplierProduct supplierProduct: supplier.getProducts()) {
            if(supplierProduct.getProduct().getId().equals(productId)){
                this.product=supplierProduct;
            }
        }
        this.totalCapacity = supplier.getTotalCapacity();
        this.revenue = supplier.getRevenue();
    }

}
