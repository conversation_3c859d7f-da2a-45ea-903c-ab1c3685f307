package com.mstack.keystone.model.dto;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class LinkedOrder{
    String orderId;
    String units;
    String id;

    public String getId() {
        return id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setId(String id) {
        this.id = id;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    double quantity;
}