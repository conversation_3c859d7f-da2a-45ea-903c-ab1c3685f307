package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.enums.UnitOfMeasure;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplierQuotation {
    // determines which number qutotation it is
    int quotationNumber;
    float materialCost;
    float logisticCost;
    float quotedPrice;
    UnitOfMeasure uom;
    String supplierId;
    String productId;
    SupplierProductDto supplier;
    Packaging packaging;
    int leadTime;
    int transitTime;
    IncoTerms incoterms;
    List<SupplierDocument> documents;
    SupplierQuotationStatus supplierQuotationStatus;
    String rejectionReason;
    String productName;

    public SupplierQuotation(SupplierQuotation original) {
        this.quotationNumber = original.quotationNumber;
        this.materialCost = original.materialCost;
        this.logisticCost = original.logisticCost;
        this.uom = original.uom;
        this.supplierId = original.supplierId; // Creating a new String to avoid sharing references
        this.packaging = original.packaging; // Assuming Packaging also has a copy constructor
        this.leadTime = original.leadTime;
        this.transitTime = original.transitTime;
        this.incoterms = original.incoterms; // Assuming IncoTerms also has a copy constructor
        this.quotedPrice=original.quotedPrice;
        // Copying the list of documents
        this.documents =original.documents;
        this.supplierQuotationStatus = original.supplierQuotationStatus;
        this.rejectionReason = original.rejectionReason;
        this.productName = original.productName;
    }

    public boolean isQuotedPriceValid(float quotedPrice) {
        return quotedPrice >= ((this.getMaterialCost() + this.getLogisticCost()) * 1.05);
    }
}
