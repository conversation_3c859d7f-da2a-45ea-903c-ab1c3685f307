package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.repository.Activity;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.model.repository.order.OrderBook;
import com.mstack.keystone.model.dto.ActivityRemark;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
public class ActivityDto {

    String id;
    EntityType entityType;
    String entityId;
    String secondaryId;
    String name;
    String description;
    String assignedBy;
    String assignedTo;
    Date dueDate;
    String orderBookId;
    String dueDateLogic;
    List<Event> events;
    Date displayDate;
    List<HashMap<String,Object>> taskConfig;
    boolean deleted;
    List<HashMap> history;
    List<ActivityRemark> remarks;
    String category;
    ActivityStatus status;
    String createdBy;
    HashMap<String, Object> additionalData;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;
    String customer;
    String products;
    List<ActivityRemark> remarksGiven;
    List<ActivityRemark> remarksReceived;
    private List<PrerequisiteTask> prerequisiteTasks; // List of taskId and taskName


    public ActivityDto(Activity activity, CustomerOrder customerOrder, OrderBook orderBook) {
        this.id = activity.getId();
        this.entityType = activity.getEntityType();
        this.entityId = activity.getEntityId();
        this.secondaryId = activity.getSecondaryId();
        this.name = activity.getName();
        this.displayDate=activity.getDisplayDate();
        this.taskConfig=activity.getTaskConfig();
        this.description = activity.getDescription();
        this.assignedBy = activity.getAssignedBy();
        this.assignedTo = activity.getAssignedTo();
        this.dueDate = activity.getDueDate();
        this.dueDateLogic = activity.getDueDateLogic();
        this.events = activity.getEvents();
        this.deleted = activity.isDeleted();
        this.history = activity.getHistory();
        this.remarks = activity.getRemarks();
        this.category = activity.getCategory();
        this.status = activity.getStatus();
        this.createdBy = activity.getCreatedBy();
        this.additionalData = activity.getAdditionalData();
        this.lastUpdatedBy = activity.getLastUpdatedBy();
        this.createdAt = activity.getCreatedAt();
        this.lastUpdatedAt = activity.getLastUpdatedAt();
        this.customer=customerOrder.getCustomer().getName();
        this.products= customerOrder.getProductString();
        this.orderBookId=orderBook!=null?orderBook.getId():null;
    }

}
