package com.mstack.keystone.model.dto;

import java.util.Date;
import java.util.HashMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Event {

    String name;
    String trigger;
    HashMap<String, Object> context;
    String createdBy;
    Date createdAt;

    public Event(String name, String description, String createdBy, String trigger) {
        this.name = name;
        if (context == null) {
            context = new HashMap<>();
        }
        this.trigger = trigger;
        context.put("description", description);
        this.createdBy = createdBy;
        this.createdAt = new Date();
    }

    public static Event createApiEvent(String name, String description, String createdBy) {
        return new Event(name, description, createdBy, "API_EVENT");
    }


    public Event(Event event) {
        this.name = event.getName();
        this.trigger = event.getTrigger();
        this.context = event.getContext();
    }
}
