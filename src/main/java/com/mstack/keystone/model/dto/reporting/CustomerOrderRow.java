package com.mstack.keystone.model.dto.reporting;

import lombok.Data;

@Data
public class CustomerOrderRow {
    String orderId;
    String orderedProductId;
    String productTradeName;
    String type;
    String packSize;
    String tareWeight;
    String dimension;
    String updatedPackagingName;

    public CustomerOrderRow(String orderId, String orderedProductId, String productTradeName, String type, String packSize, String tareWeight, String dimension, String updatedPackagingName) {
        this.orderId = orderId;
        this.orderedProductId = orderedProductId;
        this.productTradeName = productTradeName;
        this.type = type;
        this.packSize = String.valueOf(packSize);
        this.tareWeight = String.valueOf(tareWeight);
        this.dimension = dimension;
        this.updatedPackagingName = updatedPackagingName;
    }

    public String[] toStringArray() {
        return new String[]{
                orderId, orderedProductId, productTradeName, type, packSize, tareWeight, dimension, updatedPackagingName
        };
    }
}
