package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.enquiry.Enquiry;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EnquiryDto {
    String id;
    String enquiryId;
    Customer customer;
    EnquiryStatus enquiryStatus;
    String productName;
    String grade;
    float quantity;
    UnitOfMeasure unitOfMeasure;
    String customerId;
    SupplierQuotation quotation;
    List<SupplierQuotation> quotationHistory;
    String casNumber;
    IncoTerms incoterms;
    String application;
    String assignedTo;
    String assignedBy;
    Date createdAt;
    String createdBy;
    Date lastUpdatedAt;
    String lastUpdatedBy;
    EnquiryPriority enquiryPriority;
    List<String> categories;
    String rejectionRemark;
    String negotiationRemark;
    boolean deleted;

    public EnquiryDto(Enquiry enquiry,Customer customer) {
        this.id = enquiry.getId();
        this.enquiryId = enquiry.getEnquiryId();
        this.enquiryStatus = enquiry.getEnquiryStatus();
        this.productName = enquiry.getProductName();
        this.grade = enquiry.getGrade();
        this.quotation=enquiry.getQuotation();
        this.customer=customer.getCustomerWithDisplayName(customer);
        this.quantity = enquiry.getQuantity();
        this.casNumber=enquiry.getCasNumber();
        this.unitOfMeasure = enquiry.getUnitOfMeasure();
        this.customerId = enquiry.getCustomerId();
        this.incoterms = enquiry.getIncoTerms();
        this.application = enquiry.getApplication();
        this.assignedTo = enquiry.getAssignedTo();
        this.assignedBy = enquiry.getAssignedBy();
        this.createdAt = enquiry.getCreatedAt();
        this.createdBy = enquiry.getCreatedBy();
        this.lastUpdatedAt = enquiry.getLastUpdatedAt();
        this.lastUpdatedBy = enquiry.getLastUpdatedBy();
        this.deleted = enquiry.isDeleted();
        this.quotationHistory = enquiry.getQuotationHistory();
        this.enquiryPriority = enquiry.getEnquiryPriority();
        this.categories = enquiry.getCategories();
        this.rejectionRemark = enquiry.getRejectionRemark();
        this.negotiationRemark = enquiry.getNegotiationRemark();
    }
}
