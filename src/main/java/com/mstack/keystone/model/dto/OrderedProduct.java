package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.enums.OrderStatus;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Product;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

@Data
public class OrderedProduct {

    private Product product;
    String id;
    private UnitOfMeasure uom;
    List<LinkedOrder> linkedOrders;
    private double quantity;
    private double price;
    private String hsCode;
    private Packaging packaging;
    private double units;
    OrderStatus status;
    private File labelFile;
    private String remarks;
    List<HashMap<String,Object>> batchData;
    private double quantityPerUnit;
    private String stateOfOrigin;
    private String districtOfOrigin;
    private String countryOfOrigin;
    private String hazDetails;
    double perUnitWeight;
    // used for dsipalying custom product name alias in generated documents
    private String productNameAlias;
    private double chemstackPrice;
    // per uom to kg value -- to handle gallon and others
    private double perUnitKgValue;
    private boolean batchDateVisible;
    private String batchDateVisibilty;
    private String moa;
    private String label;
    private String otherPackagingDetails;
}
