package com.mstack.keystone.model.dto;

import com.mstack.keystone.model.repository.Product;
import com.mstack.keystone.model.repository.inventory.Inventory;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;

@Data
public class InventoryProductDto {
    private Inventory inventory;
    private String inventoryId;
    private String id;
    private Product product;
    private Packaging packaging;
    private double units;

    public InventoryProductDto(Inventory inventory, String id, Product product, Packaging packaging, double units) {
        this.inventoryId = inventory.getInventoryId();
        this.inventory = inventory;
        this.id = id;
        this.product = product;
        this.packaging = packaging;
        this.units = units;
    }
}
