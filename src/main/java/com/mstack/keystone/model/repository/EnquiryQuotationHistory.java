package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.SupplierQuotation;
import lombok.Data;

import java.util.*;

@Data
public class EnquiryQuotationHistory {
    String id;
    String enquiryId;
    List<SupplierQuotation> quotationList;
    String createdBy;
    Date createdAt;
    String lastUpdatedBy;
    Date lastUpdatedAt;

    public EnquiryQuotationHistory(String enquiryId) {
        this.enquiryId = enquiryId;
        this.quotationList = new ArrayList<>();
    }

    public void addQuotation(SupplierQuotation supplierQuotation) {
        SupplierQuotation temp = new SupplierQuotation(
                this.getQuotationList().size() + 1,
                supplierQuotation.getMaterialCost(),
                supplierQuotation.getLogisticCost(),
                supplierQuotation.getQuotedPrice(),
                supplierQuotation.getUom(),
                supplierQuotation.getSupplierId(),
                supplierQuotation.getProductId(),
                supplierQuotation.getSupplier(),
                supplierQuotation.getPackaging(),
                supplierQuotation.getLeadTime(),
                supplierQuotation.getTransitTime(),
                supplierQuotation.getIncoterms(),
                supplierQuotation.getDocuments(),
                supplierQuotation.getSupplierQuotationStatus(),
                supplierQuotation.getRejectionReason(),
                supplierQuotation.getProductName()
        );
        this.getQuotationList().add(temp);
    }
}
