package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.AccountOwner;
import com.mstack.keystone.model.dto.Address;
import com.mstack.keystone.model.dto.EmailInfo;
import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.enums.CompanySize;
import com.mstack.keystone.model.enums.CustomerType;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Nullable
@NoArgsConstructor
public class Customer {

    private String id;
    private String customerId;
    private String name;
    private Address address;
    private String email;
    // New field for structured email information
    private EmailInfo emailInfo;
    private String countryCode;
    private String mobile;
    private CompanySize size;
    private String accountOwner;
    private List<String> categories;
    private CustomerType type;
    private List<HashMap<String, Object>> remarks;
    private List<String> l1Reviewers;
    private List<String> l2Reviewers;
    //    private List<AccountOwner> customerAccountOwners;
    private boolean deleted;
    String createdBy;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;
    private HashMap<String, List<File>> documents;

    public void softDelete() {
        this.deleted = true;
    }

    public Customer(String name) {
        this.name = name;
    }

    public Customer getCustomerWithDisplayName(Customer customer) {
        Customer customerDto = new Customer();
        customerDto.setId(this.id);
        customerDto.setCustomerId(this.customerId);
        customerDto.setName(this.name);
        return customerDto;
    }
}