package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.enums.DistanceUnit;
import com.mstack.keystone.model.enums.TransportationType;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class TransportationDetails {

    String id;
    TransportationType transportationType; // carrier type
    String origin;
    String destination;
    String transportNumber; // ex- vehicle number or cargo number or flight number, etc
    String transportContactNumber;
    String transportContactPersonName;
    List<String> transportAttachments; // documents related to this transportation
    float distance;
    DistanceUnit distanceUnit;
    BigDecimal cost;
    BigDecimal insuranceCost;
    String preCarriageBy; // not sure what this could be
    String shippingBillNo;
}
