package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.enums.MailStatus;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
@Document
@Builder
public class MailRecord {
    @Id
    String id;
    String mailTemplate;
    String senderId;
    List<String> tos;
    List<String> ccs;
    String body;
    String subject;
    // name : doc id
    HashMap<String, String> fileMap;
    Date createdAt;
    String createdBy;
    Date lastUpdatedAt;
    String lastUpdatedBy;
    HashMap<String, Object> meta;
    MailStatus mailStatus;
    boolean deleted;
}
