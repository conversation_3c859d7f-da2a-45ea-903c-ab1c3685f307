package com.mstack.keystone.model.repository;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "audit")
public class Audit {
    private String id;
    private Object entity;
    private String entityType;
    private String createdBy;
    private Date createdAt;
}
