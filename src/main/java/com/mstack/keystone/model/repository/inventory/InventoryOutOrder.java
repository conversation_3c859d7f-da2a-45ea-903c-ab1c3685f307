package com.mstack.keystone.model.repository.inventory;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InventoryOutOrder {
    String id;
    String inventoryId;
    Inventory inventoryDetail;
    String customerDispatchOrderId;
    String customerDispatchOrderNumber;
    String orderId;
    List<InventoryOutOrderProduct> products;
    Date createdAt;
    Date lastUpdatedAt;
    String createdBy;
    String lastUpdatedBy;
    boolean deleted;
}
