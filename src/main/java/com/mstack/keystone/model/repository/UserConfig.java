package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.TabConfig;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Document
@Data
@NoArgsConstructor
public class UserConfig {
    @Id
    String id;
    @NotEmpty(message = "user id cannot be empty ")
    String userId;
    HashMap<String ,TabConfig> tabConfigs;
    String createdBy;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;
}
