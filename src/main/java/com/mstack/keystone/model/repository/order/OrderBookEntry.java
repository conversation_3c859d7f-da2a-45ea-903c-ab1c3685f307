package com.mstack.keystone.model.repository.order;

import com.mstack.keystone.model.dto.Address;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Product;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.Data;
import org.springframework.data.annotation.Id;

@Data
public class OrderBookEntry {

    String id;
    private UnitOfMeasure uom;
    private double quantity;
    private double price;
    private Product product;
    private IncoTerms incoterms;
    private Date expectedDeliveryDate;
    private Packaging packaging;
    List<HashMap<String, Object>> remarks;
    private String hsCode;
    private double margin;
    String customerProductCode;
    //    String numberOfPacking;
    String productDescription;
    String label;
    // TODO NEED THIS IN FRONTEND
    private double units;
    private HashMap<String, Object> meta;
    HashMap<String, List<File>> documents;
    private Double marginPercentage;
    private Date marginApprovedOn;
    private String marginApprovedBy;
    private String moa;
    private Date deliveryDate;
    private List<HashMap<String, Object>> deliveryInstructions;
    private String countryOfOrigin;
    private String modeOfExport;
    private boolean testingRequired;
    private boolean dispatchWithResults;
    private double quantityPerUnit;
    private String otherPackagingDetails;
}
