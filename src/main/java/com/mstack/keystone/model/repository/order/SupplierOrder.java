package com.mstack.keystone.model.repository.order;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.OrderedProduct;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.enums.OrderStatus;
import com.mstack.keystone.model.repository.Supplier;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

@Data
public class SupplierOrder {
    String id;
    String orderId;
    Supplier supplier;
    String purchaseOrderNumber;
    Date purchaseOrderDate;
    HashMap<String, List<File>> documents;
    List<OrderedProduct> products;
    //    List<String> documentsRequired;
    IncoTerms incoterms;
    OrderStatus status;
    Date createdAt;
    String createdBy;
    Date lastUpdatedAt;
    Date shipmentDate;
    String lastUpdatedBy;
    double perUnitWeight;
    PaymentTerms paymentTerms;
    List<HashMap> statusHistory;
    List<HashMap<String, Object>> remarks;
    List<String> approvedBy;
    Date approvedAt;
    boolean approved;
    boolean deleted;
    String arnNumber;
    String linkedSupplierOrderBookId;

    private Date originBookingMadeOn;
    private String originCarrierName; // to the origin port
    private String originTrackingNumber;
    private String originTrackingUrl;
    private Date destinationBookingMadeOn;
    private String destinationCarrierName;
    private String  destinationTrackingNumber;
    private String destinationTrackingUrl;
    private String sentForTestingBy;
    private Date sentForTestingOn;
    private String labCoaReceivedBy;
    private Date testingDate;
    private String batchNumber;
    private String packagingDoneBy;
    private String qcApprovedBy;
    private Date qcApprovedOn;
    private Date loadingCompletedOn;
    private String loadingCompletedBy;
    private Date shippingCommunicatedOn;
    private Date arrivalDateAtOrigin;
    private String rePackagingConfirmedBy;
    private String labMoa;
    private String movementApprovedBy;
    private Date movementApprovedOn;
    private Date dispatchToDestinationDate;
    private Date destinationRecievedDate;
    private Date dispatchFromDestinationOn;
    private String sampleApprovedBy;
    private Date etaForDestination;
    private Date supplierDispatchedOn;
    private Date dispatchFromOrigin;
    private Date deliveryAtDestinationPartnerDate;
    private String deliveryAtDestinationConfirmedBy;
    private String releasePaymentMadeBy;
    private Date releasePaymentMadeOn;
    private String route;
    private Date eta;
    private Date actualDateOfDeparture;
    private Date isfCutoffDate;
    private Date isfActualDate;



    public void softDelete() {
        this.deleted = true;
    }
}
