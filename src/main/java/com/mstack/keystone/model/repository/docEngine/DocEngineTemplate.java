package com.mstack.keystone.model.repository.docEngine;

import com.mstack.keystone.model.enums.EntityType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
public class DocEngineTemplate {
    @Id
    String id;
    String docType; //Invoice
    String displayName;
    List<EntityType> viewers; //Customer
    String category; // Customer Document
    String state; /// Draft
    Date createdAt;
    Date lastUpdatedAt;
    boolean generated;
    String createdBy;
    String lastUpdatedBy;
    HashMap meta;
//    HashMap<String, List<String>> entityConfig;
}
