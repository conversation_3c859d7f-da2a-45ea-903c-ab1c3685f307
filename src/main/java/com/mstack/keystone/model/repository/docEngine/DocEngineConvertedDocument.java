package com.mstack.keystone.model.repository.docEngine;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.docEngine.LinkedEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;
import java.util.List;

@Data
public class DocEngineConvertedDocument {
    @Id
    String id;
    private File fileDetails;
    private String docType;
    private List<LinkedEntity> linkedEntities;
    private String createdBy;
    private String lastUpdatedBy;
    private Date createdAt;
    private Date lastUpdatedAt;

    public DocEngineConvertedDocument(File fileDetails, String docType, List<LinkedEntity> linkedEntites) {
        this.fileDetails = fileDetails;
        this.docType = docType;
        this.linkedEntities = linkedEntites;
    }
}
