package com.mstack.keystone.model.repository.inventory;

import lombok.Data;

import java.util.Date;

@Data
public class Inventory {
    private String name;
    private String id;
    private String country;
    private String inventoryId; // INV wala logic TODO - PS
    private boolean delete;
    Date createdAt;
    Date lastUpdatedAt;
    String createdBy;
    String lastUpdatedBy;
    // Constructors can be added if needed

    public void softDelete() {
        this.delete = true;
    }
}

