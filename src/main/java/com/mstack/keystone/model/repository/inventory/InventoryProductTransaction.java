package com.mstack.keystone.model.repository.inventory;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;

@Data
@NoArgsConstructor
public class InventoryProductTransaction {
    String transactionId;
    String id;
    String inventoryProductId;
    double units;
    HashMap meta;
    String transactionType;
    Date createdAt;
    String createdBy;
    Date updatedAt;
    String updatedBy;

    public InventoryProductTransaction(String transactionId,String productId,double units,String transactionType,
                                       String orderBookId,String supplierOrderbookId,String purchaseOrderNumber,String orderedProductId){
        this.transactionId=transactionId;
        this.inventoryProductId=productId;
        this.units=units;
        this.transactionType=transactionType;
        HashMap map=new HashMap<>();
        map.put("orderBookId",orderBookId);
        map.put("supplierOrderbookId",supplierOrderbookId);
        map.put("orderedProductId",orderedProductId);
        map.put("purchaseOrderNumber",purchaseOrderNumber);
        this.meta=map;
    }
}
