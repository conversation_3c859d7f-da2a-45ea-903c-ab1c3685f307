package com.mstack.keystone.model.repository.inventory;

import com.mstack.keystone.model.dto.Packaging;
import com.mstack.keystone.model.dto.SDOVirtualProductBatchDto;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import com.mstack.keystone.model.repository.Product;
import lombok.Data;

import java.util.List;

@Data
public class InventoryOutOrderProduct {
    String productId;
    Product product;// for dto
    Packaging packaging;// for dto
    String packagingId;
    double units;
    double quantity;
    UnitOfMeasure uom;
    List<SDOVirtualProductBatchDto> productBatches;
}
