package com.mstack.keystone.model.repository.Accounting;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.enums.CurrencyType;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
public class Invoice {
    String id;
    String vendorName;
    String invoiceType;
    private List<File> invoiceFile;
    private String invoiceNumber;
    private double invoiceAmount;
    private CurrencyType currencyType;
    private Date invoiceDate;
    private Date blDate;
    private Date deliveryDate;
    private Date paymentDate;
    private List<HashMap> items;
    private Date createdAt;
    private Date updatedAt;
    private String createdBy;
    private String updatedBy;
    boolean deleted;

    public void softDelete() {
        this.deleted = true;
    }
}
