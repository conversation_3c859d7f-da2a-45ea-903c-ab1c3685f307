package com.mstack.keystone.model.repository.inventory;

import com.mstack.keystone.model.enums.UnitOfMeasure;
import lombok.Data;

import java.util.Date;

@Data
public class ProductBatchDetail {
    String id;
    private String batchNumber;
    private String containerNumber;
    private double units;
    private String supplierOrderDispatchId;
    private String customerOrderDispatchId;
    private String inventoryInOrderId;
    private String inventoryOutOrderId;
    // which inventory it belongs to
    private String inventoryId;
    // since not required
//    private double tareWeight;
    private double netWeightPerUnit;
    private String packagingId;
    private UnitOfMeasure unitOfMeasure;
    private String productId;
    String type;
    Date mfgDate;
    Date expDate;
    Date createdAt;
    Date lastUpdatedAt;
    String createdBy;
    String lastUpdatedBy;
    private boolean deleted;
    private String batchUID;

    public void softDelete() {
        this.deleted = true;
    }

    public ProductBatchDetail deepClone() {
        ProductBatchDetail cloned = new ProductBatchDetail();
//        cloned.setId(this.id);
        cloned.setUnits(this.units);
        cloned.setSupplierOrderDispatchId(this.supplierOrderDispatchId);
        cloned.setCustomerOrderDispatchId(this.customerOrderDispatchId);
        cloned.setInventoryInOrderId(this.inventoryInOrderId);
        cloned.setInventoryOutOrderId(this.inventoryOutOrderId);
        cloned.setInventoryId(this.inventoryId);
//        cloned.setTareWeight(this.tareWeight);
        cloned.setNetWeightPerUnit(this.netWeightPerUnit);
        cloned.setPackagingId(this.packagingId);
        cloned.setUnitOfMeasure(this.unitOfMeasure != null ? this.unitOfMeasure : null);
        cloned.setProductId(this.productId);
        cloned.setType(this.type);
        cloned.setCreatedAt(this.createdAt != null ? (Date) this.createdAt.clone() : null);
        cloned.setLastUpdatedAt(this.lastUpdatedAt != null ? (Date) this.lastUpdatedAt.clone() : null);
        cloned.setCreatedBy(this.createdBy);
        cloned.setLastUpdatedBy(this.lastUpdatedBy);
        cloned.setDeleted(this.deleted);
        cloned.setBatchUID(this.batchUID);
        cloned.setBatchNumber(this.batchNumber);
        cloned.setContainerNumber(this.containerNumber);
        cloned.setMfgDate(this.mfgDate);
        cloned.setExpDate(this.expDate);
        return cloned;
    }

}

