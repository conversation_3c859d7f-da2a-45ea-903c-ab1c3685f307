package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.Address;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.dto.SupplierProduct;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class Supplier {

    String id;
    String supplierId;
    String name;
    String email;
    String mobile;
    Address address;
    List<SupplierProduct> products;
    boolean deleted;
    String createdBy;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;
    float totalCapacity;
    float revenue;
    PaymentTerms paymentTerms;
    String gstin;
    String pan;
    public void softDelete() {
        this.deleted = true;
    }
}
