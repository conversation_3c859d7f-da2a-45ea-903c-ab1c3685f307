package com.mstack.keystone.model.repository.order;

import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.enums.BillOfLading;
import com.mstack.keystone.model.enums.OrderStatus;
import com.mstack.keystone.model.repository.Customer;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerOrder {

    String id;
    String orderId;
    String purchaseOrderNumber;
    Date purchaseOrderDate;
    Customer customer;
    String inventoryId;
    List<OrderedProduct> products;
    OrderStatus status;
    List<HashMap> statusHistory;
    boolean deleted;
    String typeOfShipment;
    boolean approved;
    String approvedBy;
    Date createdAt;
    String createdBy;
    String assignedTo;
    Date invoiceDate;
    Date chemstackInvoiceDate;
    String mstackInvoiceNumber;
    String chemstackInvoiceNumber;
    Date assignedAt;
    String assignedBy;
    Date lastUpdatedAt;
    String lastUpdatedBy;
    Double freightCost;
    Double insuranceCost;
    String bLNumber;
    String vesselNumber;
    String voyageName;
    String trackingUrl;
    String trackingNumber;
    //TODO remove while pushing to prod
    List<String> documentsRequired;
    HashMap<String, List<File>> draftDocuments;
    HashMap<String, List<File>> finalDocuments;
    private BillOfLading typeOfBL;
    private IncoTerms incoterms;
    private Date shipmentDate;
    private Date shipmentCreatedAtDate;
    private Date deliveryDate;
    private Date expectedDeliveryDate;
    private Date blDate;
    String remarks;
    private PaymentTerms paymentTerms;
//        private Consignee consigneeDetails;
//    private NotifyParty notifyPartyDetails;
    private String consignee;
    private String notifyParty;
    private String tradeAgreementCode;
    private Double customExchangeRate;
    private Integer numberOfPallets;
    private Double palletWt;
    private Double mstackPalletWt;
    private Double igstAmt;
    String marksAndContainers;
    private PaymentTerms mstackPaymentTerms;
    private String countryOfOrigin;
    // Mstack To Chemstack Payment Terms
    private String mToCPayTerms;
    private HashMap<String, Object> meta;
    private String reasonForCancellation;
    private String invoiceNumberGeneratedBy;
    private boolean invoiceDateValidationDisabled;
    private String sealNumber;
    private String deliveryAddress;
    private String invoiceSentBy;
    private Date invoiceSentOn;
    private Date dispatchDate;
    private String sampleDeliveryConfirmedBy;
    private Date sampleDeliveredOn;
    private Date sampleDispatchedOn;
    private String carrierName;
    private SupplierRFQ supplierRfq;
    private Date supplierRFQRecordedOn;
    private String supplierRFQRecordedBy;
    private String marginApprovedBy;
    private Date marginApprovedOn;
    private Double marginPercentage;
    private String customsReleaseConfirmedBy;
    private Date customsReleasedOn;
    private Date orderDeliveredOn;
    private Date dispatchFromDestinationOn;
    private String deliveryInstructions;
    private Date customerAppointementDate;
    private Date truckerPickUpDate;
    private String buyerCurrency;

    public String getProductString(){
        List<OrderedProduct> products=this.getProducts();
        String productString="";
        for (OrderedProduct product:products) {
            productString+=product.getProduct().getTradeName()+", ";
        }
        productString=productString.substring(0, productString.length() - 2);
        return productString;
    }

    public String getPaymentTermsString(){
        if (paymentTerms == null) return "N/A"; // or any default value you prefer
        String poPaymentTerms = Objects.nonNull(paymentTerms.getPoPaymentTerms()) ? paymentTerms.getPoPaymentTerms() : paymentTerms.getStartDate();
        String output="T/T ";
        output+=(paymentTerms.getCreditorDays()+" days at ");
        output+=(poPaymentTerms);
        return  output;
    }
    public void softDelete() {
        this.deleted = true;
    }
}
