package com.mstack.keystone.model.repository.order;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.IncoTerms;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierRFQ {

  private IncoTerms incoterms;
  private String uom;
  private String supplierName;
  private String currency;
  private Double costPrice;
  private Double quotedQuantity;
  private Double firstMileLogistics;
  private Double seaFreight;
  private Double lastMileLogistics;
  private Double destinationCharges;
  private String customerCountry;
  private String cityPlaceOfSupply;
  private String hsnCode;
  private String countryOfOrigin;
  private Double dutyPercentage;
  private File dutyCheckScreenshot;
  private String packagingType;
  private Double targetContributionMargin;
  private Integer leadTime;
  private Date supplierQuotationValidity;
  private MonetaryValue purchaseOrderValue;
  private MonetaryValue logisticsCost;
  private MonetaryValue dutyAmountValue;
  private MonetaryValue salesOrderValue;
  private Double creditPercent;
  private Integer creditDays;
  private String paymentTerms;
}