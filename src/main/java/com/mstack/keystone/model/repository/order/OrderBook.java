package com.mstack.keystone.model.repository.order;

import com.mstack.keystone.model.dto.*;
import com.mstack.keystone.model.enums.CurrencyType;
import com.mstack.keystone.model.enums.OrderType;
import com.mstack.keystone.model.repository.Customer;
import com.mstack.keystone.model.repository.inventory.Inventory;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

@Data
public class OrderBook {

    String id;
    String orderBookId;
    private String purchaseOrderNumber;
    List<File> purchaseOrderFile;
    private Date purchaseOrderDate;
    private Customer customer;
    private Inventory inventory;
    List<OrderBookEntry> products;
    private PaymentTerms paymentTerms;
    List<HashMap<String, Object>> remarks;
    private boolean generalTermsAndConditions;
    double perUnitWeight;
    boolean deleted;
    String createdBy;
    String lastUpdatedBy;
    String billTo;
    String deliveryAddress;
    Date createdAt;
    Date lastUpdatedAt;
    String inventoryId;
    boolean testKey;
    CurrencyType buyerCurrency = CurrencyType.DOLLAR;
    private OrderType orderType;
    private String enquiryNumber;
    private String category;
    private boolean onHold;
    private double poValue;
    private double taxPercent;
    private String countryOfDelivery;
    private String sampleRequestedBy;
    // DEFAULT TO BUYER
}
