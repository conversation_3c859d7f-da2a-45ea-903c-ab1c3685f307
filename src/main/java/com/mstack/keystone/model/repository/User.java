package com.mstack.keystone.model.repository;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@Data
@Document(collection = "users")
public class User {
    String id;
    private String username;
    private String entityId;
    private String entityType;
    private String password;
    private List<String> permissions;
    private List<String> permissionsGroups;
    private boolean isEmailVerified;
    private Date createdAt;
    private Date updatedAt;
    boolean deleted;
}
