package com.mstack.keystone.model.repository.enquiry;

import com.mstack.keystone.model.dto.EnquiryPriority;
import com.mstack.keystone.model.dto.EnquiryStatus;
import com.mstack.keystone.model.dto.IncoTerms;
import com.mstack.keystone.model.dto.SupplierQuotation;
import com.mstack.keystone.model.enums.UnitOfMeasure;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class Enquiry {
    String id;
    String enquiryId;
    EnquiryStatus enquiryStatus;
    String productName;
    String casNumber;
    String grade;
    float quantity;
    UnitOfMeasure unitOfMeasure;
    String customerId;
    IncoTerms incoTerms;
    String application;
    String assignedTo;
    String assignedBy;
    Date createdAt;
    SupplierQuotation quotation;
    List<SupplierQuotation> quotationHistory;
    String createdBy;
    Date lastUpdatedAt;
    String lastUpdatedBy;
    EnquiryPriority enquiryPriority;
    private List<String> categories;
    String rejectionRemark;
    String negotiationRemark;
    boolean deleted;

    public void softDelete() {
        this.deleted = true;
    }


}
