package com.mstack.keystone.model.repository.email;

import com.mstack.keystone.model.enums.DeliveryStatus;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Email {

    String id;
    String from; // either from service or default one
    String to; // always from service
    String subject; // from template
    List<String> attachments; // list of attachments
    String emailType; // always from service
    DeliveryStatus deliveryStatus; // set urself
    HashMap<String, Object> additionalData; // coming from service only OR null for now
    HashMap<String, Object> templateInfo; // coming from service OR null
    String sentBy; // coming from service
    Date createdAt; // in service
    Date updatedAt; // in service

    public Email(
        String from,
        String to,
        String emailType,
        HashMap<String, Object> additionalData,
        HashMap<String, Object> templateInfo,
        String sentBy
    ) {
        this.from = from;
        this.to = to;
        this.emailType = emailType;
        this.additionalData = additionalData;
        this.templateInfo = templateInfo;
        this.sentBy = sentBy;
    }
}
