package com.mstack.keystone.model.repository.docEngine;

import com.mstack.keystone.model.dto.Event;
import com.mstack.keystone.model.enums.EntityType;
import com.mstack.keystone.model.enums.docEngine.DocStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.annotation.Id;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DocMeta {
    @Id
    String id;
    @NonNull
    String docType; //BL
    String label;
    List<EntityType> viewers;
    String category;
    String state; // DRAFT OR FINAL
    DocStatus status;
    String mode;
    String fileName; //1234
    String fileId;  //  1234
    EntityType entityType; //Customer ORder
    String entityId; // customerOrder Id
    Date createdAt;
    boolean deleted;
    Date lastUpdatedAt;
    boolean isApproved;
    HashMap meta;
    String createdBy;
    String lastUpdatedBy;
    List<Event> eventsHistory;

    public DocMeta(@NonNull String docType, String label, List<EntityType> viewers, String category, String state) {
        this.docType = docType;
        this.label = label;
        this.viewers = viewers;
        this.category = category;
        this.state = state;
        this.eventsHistory = new ArrayList<>();
    }

    public DocMeta(@NonNull String docType, String label, List<EntityType> viewers, String category, String state, EntityType entityType, String entityId,HashMap meta) {
        this.docType = docType;
        this.label = label;
        this.viewers = viewers;
        this.category = category;
        this.state = state;
        this.entityType = entityType;
        this.entityId = entityId;
        this.meta=meta;
        this.eventsHistory = new ArrayList<>();

    }

    public DocMeta(@NonNull String docType, String label, List<EntityType> viewers, String category, String state, EntityType entityType, String entityId,HashMap meta,String fileId,String fileName) {
        this.docType = docType;
        this.label = label;
        this.viewers = viewers;
        this.category = category;
        this.state = state;
        this.entityType = entityType;
        this.entityId = entityId;
        this.meta=meta;
        this.fileId=fileId;
        this.fileName=fileName;
        this.eventsHistory = new ArrayList<>();
    }

    public static DocMeta getCopy(DocMeta original) {
        DocMeta cloned = new DocMeta();
        cloned.docType = original.docType;
        cloned.label = original.label;
        cloned.viewers = original.viewers != null ? new ArrayList<>(original.viewers) : null;
        cloned.category = original.category;
        cloned.state = original.state;
        cloned.mode = original.mode;
        cloned.fileName = original.fileName;
        cloned.fileId = original.fileId;
        cloned.entityType = original.entityType;
        cloned.entityId = original.entityId;
        cloned.createdAt = original.createdAt != null ? new Date(original.createdAt.getTime()) : null;
        cloned.deleted = original.deleted;
        cloned.lastUpdatedAt = original.lastUpdatedAt != null ? new Date(original.lastUpdatedAt.getTime()) : null;
        cloned.isApproved = original.isApproved;
        cloned.meta = original.meta != null ? new HashMap<>(original.meta) : null;
        cloned.createdBy = original.createdBy;
        cloned.eventsHistory = original.eventsHistory;
        return cloned;
    }


}
