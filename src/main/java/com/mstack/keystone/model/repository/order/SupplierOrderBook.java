package com.mstack.keystone.model.repository.order;

import com.mstack.keystone.model.dto.File;
import com.mstack.keystone.model.dto.PaymentTerms;
import com.mstack.keystone.model.enums.CurrencyType;
import com.mstack.keystone.model.repository.Supplier;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import lombok.Data;

@Data
public class SupplierOrderBook {
    String id;
    String orderBookId;
    private String purchaseOrderNumber;
    List<File> purchaseOrderFile;
    List<File> materialSpecFile;
    private Date purchaseOrderDate;
    private Date expectedDeliveryDate;
    private Supplier supplier;
    List<OrderBookEntry> products;
    private PaymentTerms paymentTerms;
    List<HashMap<String, Object>> remarks;
    boolean deleted;
    String createdBy;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;
    String billingAddress;
    String shippingAddress;
    double perUnitWeight;
    CurrencyType currencyType = CurrencyType.RUPEE;
    Double gstPercent;
    // linked customer order book ids
    List<String> linkedCOBs;

    // Additional Fields
    private String supplierRefNumber;
    private String quotationNumber;
    private String modeOfDelivery;
    private String deliveryTerm;
    private String deliveryLocation;
    private String pickupLocation;
    private List<Tax> taxList;
    private String additionalCondition;
    private String termAndCondition;
    private List<DeliverySchedule> deliverySchedule;
    private boolean transShipment;
    private boolean partialShipment;
    private boolean excludeDefaultTc;
    private Date mrd;
    private String moa;
    private String advPaymentMadeBy;
    private Date advPaymentDate;
    private double advPaymentAmount; //  represents full amount for sample orders
    private String linkedCOBId;
    private Boolean poGenrationRequired;
}

