package com.mstack.keystone.model.repository.inventory;

import lombok.Data;

import java.util.Date;

@Data
public class InventoryProduct {
    private String inventoryId;
    private String id;
    private String productId;
    private String productName;
    private String packagingId;
    private String packagingName;
    private double units;
    private boolean deleted;
    Date createdAt;
    Date lastUpdatedAt;
    String createdBy;
    String lastUpdatedBy;

    // Constructors can be added if needed

    public void softDelete() {
        this.deleted = true;
    }


}
