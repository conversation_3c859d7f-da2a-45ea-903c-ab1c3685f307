package com.mstack.keystone.model.repository;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Date;

@Data
public class ClientEnquiry {
    @NotBlank(message = "Name cannot be blank or null")
    String name;

    @NotBlank(message = "Company name cannot be blank or null")
    String companyName;

    @NotBlank(message = "Email cannot be blank or null")
    String email;

    String mobile;

    @NotBlank(message = "Requirement cannot be blank or null")
    String requirement;

    Date createdAt;
    String createdBy;
    Date lastUpdatedAt;
    String lastUpdatedBy;
    boolean deleted;
}
