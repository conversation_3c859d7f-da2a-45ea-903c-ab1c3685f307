package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

@Data
public class Product {

    String id;
    String productId;
    String tradeName;
    String grade;
    String technicalName;
    List<String> synonyms;
    List<HashMap<String, String>> categories;
    List<String> functions;
    List<String> family;
    List<String> endUses;
    List<String> features;
    HashMap<String, String> specifications;
    HashMap<String, List<File>> documents;
    boolean deleted;
    String casNumber;
    String createdBy;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;

    public void softDelete() {
        this.deleted = true;
    }
}
