package com.mstack.keystone.model.repository.inventory;

import lombok.Data;

import java.util.Date;

@Data
public class OBProductBatch {
    // PROPERTIES
    String id;
    private String supplierOrderBookId;
    private String customerOrderBookId;
    private String inventoryInOrderId;
    private String inventoryId;
    private String packagingId;
    private String productId;
    private double units;
    private String type;
    // META
    private boolean deleted;
    Date createdAt;
    Date lastUpdatedAt;
    String createdBy;
    String lastUpdatedBy;

    public void softDelete() {
        this.deleted = true;
    }

    public OBProductBatch getDeepClone() {
        OBProductBatch clone = new OBProductBatch();
        clone.setSupplierOrderBookId(this.supplierOrderBookId);
        clone.setCustomerOrderBookId(this.customerOrderBookId);
        clone.setInventoryInOrderId(this.inventoryInOrderId);
        clone.setInventoryId(this.inventoryId);
        clone.setPackagingId(this.packagingId);
        clone.setProductId(this.productId);
        clone.setUnits(this.units);
        clone.setType(this.type);
        return clone;
    }

}
