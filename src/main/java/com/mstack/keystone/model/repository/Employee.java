package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.Address;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class Employee {

    String id;
    String name;
    String employeeId;
    String mobile;
    String email;
    String countryCode;
    String vertical;
    String team;
    String level;
    Address address;
    Date createdAt;
    String createdBy;
    Date lastUpdatedAt;
    String lastUpdatedBy;
    String profilePhoto; // Not in current scope
    String thumbnail; // Not in current scope
    String reportingTo;
    boolean deleted;

    public void softDelete() {
        this.deleted = true;
    }
}
