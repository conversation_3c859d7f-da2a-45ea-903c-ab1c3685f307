package com.mstack.keystone.model.repository.Accounting;

import com.mstack.keystone.model.dto.File;
import lombok.Data;

@Data
public class InvoiceEntry {
  private String supplierName;
  private String supplierOrderId;
  private String customerOrderNumber;
  private String customerDispatchOrderId;
  private String chemicalName;
  private String invoiceType;
  private String invoiceNumber;
  private String invoiceDate;
  private String invoiceAmountWithoutTax;
  private String currencyType;
  private String dueDate;
  private String taxAmount;
  private String invoiceQuantity;
  private String totalAmount;
  private String reasonforDeviation;
  private String remarks;
  private File invoiceFile;
  private Double conversionRate;
}
