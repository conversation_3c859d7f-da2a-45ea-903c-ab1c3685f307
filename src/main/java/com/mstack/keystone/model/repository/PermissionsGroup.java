package com.mstack.keystone.model.repository;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.UniqueElements;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Document
@Data
@NoArgsConstructor
public class PermissionsGroup {
    @Id
    String id;
    @NonNull
    String name;
    String description;
    List<String> permissions;
    String createdBy;
    String lastUpdatedBy;
    Date createdAt;
    Date lastUpdatedAt;
    HashMap<String, Object> meta;
    boolean deleted;
}
