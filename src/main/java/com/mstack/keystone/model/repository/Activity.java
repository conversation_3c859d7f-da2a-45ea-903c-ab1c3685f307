package com.mstack.keystone.model.repository;

import com.mstack.keystone.model.dto.ActivityRemark;
import com.mstack.keystone.model.dto.Event;
import com.mstack.keystone.model.dto.PrerequisiteTask;
import com.mstack.keystone.model.enums.ActivityStatus;
import com.mstack.keystone.model.enums.EntityType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Activity {

    private String id;
    private EntityType entityType;
    private String entityId;
    private String orderId;
    private String secondaryId;
    private String name;
    private String description;
    private String assignedBy;
    private String assignedTo;
    private Date dueDate;
    private String dueDateLogic;
    private List<Event> events;
    private boolean deleted;
    private Date displayDate;
    private List<HashMap<String, Object>> taskConfig;
    private List<HashMap> history;
    private List<ActivityRemark> remarks;
    private String category;
    private ActivityStatus status;
    private String createdBy;
    private HashMap<String, Object> additionalData;
    private String lastUpdatedBy;
    private Date createdAt;
    private Date lastUpdatedAt;
    private String role;
    private Date taskCompletedOn;
    private String group;
    private String taskId;
    private String dependentOn;
    private boolean dependencyResolved;
    private String orderType;

    // ✅ New fields as per requirement
    private List<ActivityRemark> remarksGiven;
    private List<ActivityRemark> remarksReceived;
    private List<PrerequisiteTask> prerequisiteTasks; // List of taskId and taskName
    private boolean onHold;
    private String customerName;
    private String productName;

    // ✅ Copy Constructor
    public Activity(Activity original) {
        this.id = original.id;
        this.entityType = original.entityType;
        this.entityId = original.entityId;
        this.orderId = original.orderId;
        this.secondaryId = original.secondaryId;
        this.name = original.name;
        this.description = original.description;
        this.assignedBy = original.assignedBy;
        this.assignedTo = original.assignedTo;
        this.dueDate = original.dueDate;
        this.dueDateLogic = original.dueDateLogic;
        this.events = deepCopyEvents(original.events);
        this.deleted = original.deleted;
        this.displayDate = original.displayDate;
        this.taskConfig = deepCopyTaskConfig(original.taskConfig);
        this.history = deepCopyHistory(original.history);
        this.remarks = deepCopyRemarks(original.remarks);
        this.category = original.category;
        this.status = original.status;
        this.createdBy = original.createdBy;
        this.additionalData = deepCopyAdditionalData(original.additionalData);
        this.lastUpdatedBy = original.lastUpdatedBy;
        this.createdAt = original.createdAt;
        this.lastUpdatedAt = original.lastUpdatedAt;
        this.role = original.role;
        this.taskCompletedOn = original.taskCompletedOn;
        this.group = original.group;
        this.taskId = original.taskId;
        this.dependentOn = original.dependentOn;
        this.dependencyResolved = original.dependencyResolved;
        this.remarksGiven = deepCopyRemarks(original.remarksGiven);
        this.remarksReceived = deepCopyRemarks(original.remarksReceived);
        this.prerequisiteTasks = original.prerequisiteTasks != null
                ? original.prerequisiteTasks.stream()
                .map(p -> new PrerequisiteTask(p.getTaskId(), p.getTaskName())) // Deep copy each PrerequisiteTask
                .collect(Collectors.toList())
                : null;
    }

    // ✅ Helper method to deep copy events
    private List<Event> deepCopyEvents(List<Event> originalEvents) {
        if (originalEvents == null) return null;
        List<Event> copiedEvents = new ArrayList<>();
        for (Event event : originalEvents) {
            copiedEvents.add(new Event(event));
        }
        return copiedEvents;
    }

    // ✅ Helper method to deep copy history
    private List<HashMap> deepCopyHistory(List<HashMap> originalHistory) {
        if (originalHistory == null) return null;
        List<HashMap> copiedHistory = new ArrayList<>();
        for (HashMap<String, Object> entry : originalHistory) {
            copiedHistory.add(new HashMap<>(entry));
        }
        return copiedHistory;
    }

    // ✅ Helper method to deep copy taskConfig
    private List<HashMap<String, Object>> deepCopyTaskConfig(List<HashMap<String, Object>> originalTaskConfig) {
        if (originalTaskConfig == null) return null;
        List<HashMap<String, Object>> copiedConfig = new ArrayList<>();
        for (HashMap<String, Object> config : originalTaskConfig) {
            copiedConfig.add(new HashMap<>(config));
        }
        return copiedConfig;
    }

    // ✅ Helper method to deep copy remarks
    private List<ActivityRemark> deepCopyRemarks(List<ActivityRemark> originalRemarks) {
        if (originalRemarks == null) return null;
        List<ActivityRemark> copiedRemarks = new ArrayList<>();
        for (ActivityRemark remark : originalRemarks) {
            copiedRemarks.add(new ActivityRemark(remark.getTaskId(), remark.getRemark(),remark.getTaskName()));
        }
        return copiedRemarks;
    }

    // ✅ Helper method to deep copy additionalData
    private HashMap<String, Object> deepCopyAdditionalData(HashMap<String, Object> originalData) {
        if (originalData == null) {
            return null;
        }
        return new HashMap<>(originalData);
    }

    // ✅ Soft delete method
    public void softDelete() {
        this.deleted = true;
    }
}
