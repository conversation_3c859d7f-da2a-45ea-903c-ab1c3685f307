[{"variables": [{"key": "deliveryDate", "label": "Delivery date at site"}], "productLevelTasks": [], "orderLevelTasks": [{"name": "Mstack document generation", "dueDateLogic": "-3", "description": "Generate Mstack documents", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_MSTACK_INVOICE_NUMBER", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_INVOICE_DATE", "context": {}}], "taskConfig": [{"label": "Mstack Invoice Number", "key": "MSTACK_INVOICE_NUMBER", "inputType": "text", "mandatory": false}, {"label": "Mstack Invoice Date", "inputType": "date", "key": "INVOICE_DATE", "mandatory": true}], "category": "DELIVERY"}, {"name": "Rates finalisation with vendors", "dueDateLogic": "-7", "description": "Rates finalisation with vendors", "events": [], "category": "DELIVERY"}, {"name": "Delivery appointment from customer", "dueDateLogic": "-1", "description": "Confirm delivery appointment with customer", "events": [], "category": "DELIVERY"}, {"name": "Handover of documents to forwarder", "dueDateLogic": "-1", "description": "Hand over documents to the forwarder for shipment", "events": [], "category": "DELIVERY"}, {"name": "Handover of documents to customer by mail", "dueDateLogic": "-1", "description": "Send documents to customer via mail", "events": [], "category": "DELIVERY"}, {"name": "Delivery done at site", "dueDateLogic": "0", "description": "Complete delivery at the site", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_DELIVERY_STATUS", "context": {"status": "DELIVERY_COMPLETED"}}], "taskConfig": [{"label": "POD", "key": "POD", "inputType": "file", "fileType": "application/pdf", "fileCount": 2, "mandatory": false}], "category": "DELIVERY"}, {"name": "Document Creation Data", "dueDateLogic": "-3", "description": "", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_MARKS_AND_CONTAINERS", "context": {}}], "taskConfig": [{"label": "Marks and Containers", "key": "MARKS_AND_CONTAINERS", "inputType": "text", "mandatory": false}], "category": "DELIVERY"}]}]