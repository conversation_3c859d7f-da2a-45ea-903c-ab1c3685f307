[{"variables": [{"key": "intransitDays(Sea)", "label": "Intransit Days(Sea)"}, {"key": "testingTimeForDispatch", "label": "Intransit Days"}, {"key": "intransitDays(Road)", "label": "Intransit Days(Road)"}, {"key": "deliveryDate", "label": "Desired Delivered at destination date"}, {"key": "documentDispatch", "label": "Sailing to Final Document dispatch desired TAT"}, {"key": "additionalCFS", "label": "Additional Delay required at CFS"}, {"key": "stuffToSail", "label": "Stuffing to sailing TAT"}], "productLevelTasks": [{"name": "Palletization : $name", "dueDateLogic": "-$intransitDaysSea-5", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Proof of task", "key": "PALLETIZATION", "inputType": "file", "fileType": "image/jpeg", "fileCount": 4, "mandatory": false}], "category": "MATERIAL_ARRIVAL_AT_CFS"}, {"name": "Vehicle arrival ar destination: $name", "dueDateLogic": "-$intransitDaysSea-5 -$additionalTimeAtCFS", "description": "Updates supplier order's product to delivered", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "DELIVERED"}}], "category": "MATERIAL_ARRIVAL_AT_CFS"}, {"name": "Inspection at central station  : $name", "dueDateLogic": "-$intransitDaysSea-5", "description": "", "events": [], "category": "MATERIAL_TO_REACH_AT_CENTRAL_STATION"}, {"name": "Vehicle to reach at central station: $name", "dueDateLogic": "-$intransitDaysSea-5", "description": "", "events": [], "category": "MATERIAL_TO_REACH_AT_CENTRAL_STATION"}, {"name": "Test Results -  Postive ( within range): $name", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays-1", "description": "Updates supplier order product to manufactured and tested", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "MANUFACTURED_AND_TESTED"}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Test Report", "key": "TEST_REPORT", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 2, "mandatory": false}, {"label": "Supplier COA", "key": "SUPPLIER_COA", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 2, "mandatory": false}], "category": "TESTING"}, {"name": "Sample arrival at testing premises: $name", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays-1 - $testingTime", "description": "", "events": [], "category": "TESTING"}, {"name": "Sample collection: $name", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays-2 - $testingTime", "description": "Updates supplier order product to under testing", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "UNDER_TESTING"}}], "category": "TESTING"}, {"name": "Testing Feasibility check / quotation check: $name", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays-6 - $testingTime", "description": "", "events": [], "category": "TESTING"}, {"name": "Dispactch date from pickup : $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-5-$intransitRoadDays", "description": "Updates supplier order product to dispatched", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "DISPATCHED"}}], "category": "VEHICLE_DISPATCHED_FROM_PICKUP_POINT"}, {"name": "(Chemstack invoice, Chemstack packing list , Chemstack COA, Chemstack SDS, Supplier Invoice, supplier ISO, E-waybill): $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-5", "description": "", "events": [], "category": "DOCUMENTS_READINESS"}, {"name": "Label fixed: $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-5-$intransitRoadDays", "description": "", "events": [], "category": "MATERIAL_READINESS"}, {"name": "packing as per PO: $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-5-$intransitRoadDays", "description": "", "events": [], "category": "MATERIAL_READINESS"}, {"name": "Vehicle arrival at Pickup point: $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-5-$intransitRoadDays", "description": "Updates supplier order product to production completed", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "PRODUCTION_COMPLETED"}}], "category": "VEHICLE_ARRANGEMENT"}, {"name": "transporter booking : $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-8-$intransitRoadDays", "description": "", "events": [], "category": "VEHICLE_ARRANGEMENT"}, {"name": "Details shared with Supplier: $name", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays-6 - $testingTime", "description": "", "events": [], "category": "VEHICLE_ARRANGEMENT"}, {"name": "Payment to supplier for $name", "dueDateLogic": "0", "description": "Updates supplier order product to payment completed", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "PAYMENT_COMPLETED"}}], "category": "PAYMENT"}, {"name": "Supplier check list to supplier for $name", "dueDateLogic": "-$intransitDaysSea-$additionalTimeAtCFS-13-$intransitRoadDays", "description": "Updates supplier order product to under production", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SUPPLIER_ORDER_STATUS", "context": {"status": "UNDER_PRODUCTION"}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Supplier check list for this Supplier", "key": "SUPPLIER_CHECKLIST", "inputType": "file", "fileType": "application/pdf", "fileCount": 2, "mandatory": false}], "category": "SUPPLIER_CHECKLIST_ALIGNMENT"}], "orderLevelTasks": [{"name": "Update custom exchange rate", "dueDateLogic": "-$intransitDaysSea-13 -$additionalTimeAtCFS-3", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_CUSTOM_EXCHANGE_RATE", "context": {}}], "taskConfig": [{"label": "Custom Exchange Rate", "key": "CUSTOM_EXCHANGE_RATE", "inputType": "text", "mandatory": false}], "category": "PO_FLOW"}, {"name": "PO from chemstack to Supplier", "dueDateLogic": "-$intransitDaysSea-13 -$additionalTimeAtCFS-3", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Supplier PO", "key": "SUPPLIER_PO", "inputType": "file", "fileType": "application/pdf", "fileCount": 1, "mandatory": false}], "category": "PO_FLOW"}, {"name": "PO from mstack to chemstack", "dueDateLogic": "-$intransitDaysSea-13 -$additionalTimeAtCFS-3", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_MTOCPAYTERMS", "context": {}}], "taskConfig": [{"label": "Creditor days", "key": "CREDITOR_DAYS", "inputType": "text", "mandatory": false}, {"label": "Credit amount", "key": "CREDIT_AMOUNT", "inputType": "text", "mandatory": false}, {"label": "Start date", "inputType": "dropdown", "options": [{"key": 1, "label": "Delivery Date", "value": "DeliveryDate"}, {"key": 2, "label": "Invoice Date", "value": "InvoiceDate"}, {"key": 3, "label": "BL Date", "value": "BLDate"}], "key": "START_DATE", "mandatory": false}, {"label": "PO payment terms", "key": "PO_PAYMENT_TERMS", "inputType": "text", "mandatory": false}, {"label": "Mstack TO Chemstack payment terms", "key": "M_TO_C_PAYTERMS", "inputType": "text", "mandatory": false}], "category": "PO_FLOW"}, {"name": "PO from buyer", "dueDateLogic": "-$intransitDaysSea-13 -$additionalTimeAtCFS-3", "description": "", "events": [], "category": "PO_FLOW"}, {"name": "Document Creation Data", "dueDateLogic": "-$intransitDaysSea-11 -$additionalTimeAtCFS", "description": "", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_INVOICE_DATE", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_CHEMSTACK_INVOICE_DATE", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_MSTACK_INVOICE_NUMBER", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_CHEMSTACK_INVOICE_NUMBER", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_TRADE_AGREEMENT_CODE", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_TYPE_OF_SHIPMENT", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_NUMBER_OF_PALLETS", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_PALLET_WT", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_MARKS_AND_CONTAINERS", "context": {}}, {"trigger": "TASK_COMPLETED", "name": "UPDATE_COUNTRY_OF_ORIGIN", "context": {}}], "taskConfig": [{"label": "Trade Agreement Code", "inputType": "text", "key": "TRADE_AGREEMENT_CODE", "mandatory": false}, {"label": "Type of Shipment", "inputType": "dropdown", "options": [{"key": 1, "label": "Full Container <PERSON><PERSON> (FCL)", "value": "FCL"}, {"key": 2, "label": "Less than Container Load (LCL)", "value": "LCL"}], "key": "TYPE_OF_SHIPMENT", "mandatory": false}, {"label": "Number of pallets ", "inputType": "text", "key": "NUMBER_OF_PALLETS", "mandatory": false}, {"label": "<PERSON><PERSON><PERSON>", "inputType": "text", "key": "PALLET_WT", "mandatory": false}, {"label": "Marks and Containers", "key": "MARKS_AND_CONTAINERS", "inputType": "text", "mandatory": false}, {"label": "Country of origin", "key": "COUNTRY_OF_ORIGIN", "inputType": "text", "mandatory": false}, {"label": "Mstack Invoice Date", "inputType": "date", "key": "INVOICE_DATE", "mandatory": true}, {"label": "Mstack Invoice Number", "key": "MSTACK_INVOICE_NUMBER", "inputType": "text", "mandatory": false}, {"label": "Chemstack Invoice Date", "inputType": "date", "key": "CHEMSTACK_INVOICE_DATE", "mandatory": true}, {"label": "Chemstack Invoice Number", "key": "CHEMSTACK_INVOICE_NUMBER", "inputType": "text", "mandatory": false}, {"label": "BL Date", "key": "BL_DATE", "inputType": "date", "mandatory": false}], "category": "DOCUMENT_APPROVAL_FROM_CUSTOMER"}, {"name": "Mstack invoice, Mstack PL, COA , COO (if needed)", "dueDateLogic": "-$intransitDaysSea-11 -$additionalTimeAtCFS", "description": "", "events": [], "taskConfig": [], "category": "DOCUMENT_APPROVAL_FROM_CUSTOMER"}, {"name": "Sending of buyer checklist", "dueDateLogic": "-$intransitDaysSea-11 -$additionalTimeAtCFS", "description": "", "events": [], "category": "BUYER_CHECKLIST_ALIGNMENT"}, {"name": "Approval from buyer checklist", "dueDateLogic": "-$intransitDaysSea-9 -$additionalTimeAtCFS", "description": "", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_TYPE_OF_BL", "context": {}}], "taskConfig": [{"label": "Type of Bill of Lading", "inputType": "dropdown", "options": [{"key": 1, "label": "Orignal BL", "value": "ORIGINAL_BL"}, {"key": 2, "label": "Seaway BL", "value": "SEAWAY_BL"}, {"key": 3, "label": "Telex Release", "value": "TELEX_RELEASE"}], "key": "TYPE_OF_BL", "mandatory": true}], "category": "BUYER_CHECKLIST_ALIGNMENT"}, {"name": "Custom clearance importing country", "dueDateLogic": "0", "description": "", "events": [], "category": "IMPORTING_COUNTRY_CUSTOMS"}, {"name": "Documents to Forwarder for custom clearance", "dueDateLogic": "-8", "description": "", "events": [], "category": "IMPORTING_COUNTRY_CUSTOMS"}, {"name": "Enquiry to Forwarders / vendors", "dueDateLogic": "-8", "description": "", "events": [], "category": "IMPORTING_COUNTRY_LOGISTICS"}, {"name": "Finalisation of booking", "dueDateLogic": "-6", "description": "", "events": [], "category": "IMPORTING_COUNTRY_LOGISTICS"}, {"name": "Label approval", "dueDateLogic": "-$intransitDaysSea-12-$additionalTimeAtCFS", "description": "", "events": [], "category": "DOCUMENT_APPROVAL_FROM_CUSTOMER"}, {"name": "BL draft approval", "dueDateLogic": "-$intransitDaysSea-11-$additionalTimeAtCFS", "description": "", "events": [], "category": "DOCUMENT_APPROVAL_FROM_CUSTOMER"}, {"name": "Enquiry to Forwarders ", "dueDateLogic": "-$intransitDaysSea-16-$additionalTimeAtCFS", "description": "", "events": [], "category": "SEA_TRANSPORT_BOOKING"}, {"name": "Haz document ( DG- Declaration)", "dueDateLogic": "-$intransitDaysSea-16-$additionalTimeAtCFS", "description": "", "events": [], "category": "SEA_TRANSPORT_BOOKING"}, {"name": "Quotation from  Fowarders", "dueDateLogic": "-$intransitDaysSea-15-$additionalTimeAtCFS", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Quotation from  Fowarders", "key": "FORWARDER_QUOTATION", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 5, "mandatory": false}], "category": "SEA_TRANSPORT_BOOKING"}, {"name": "feasibility check of ports ", "dueDateLogic": "-$intransitDaysSea-14-$additionalTimeAtCFS", "description": "", "events": [], "category": "SEA_TRANSPORT_BOOKING"}, {"name": "Booking confirmation", "dueDateLogic": "-$intransitDaysSea-11-$additionalTimeAtCFS", "description": "", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_SEA_BOOKING_INFORMATION", "context": {}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Freight Cost", "key": "FREIGHT_COST", "inputType": "text", "mandatory": false}, {"label": "Insurance Cost", "key": "INSURANCE_COST", "inputType": "text", "mandatory": false}, {"label": "Proof of task", "key": "SEA_TRANSPORT_BOOKING", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 2, "mandatory": false}], "category": "SEA_TRANSPORT_BOOKING"}, {"name": "document submission (Chemstack invoice, Chemstack packing list , Chemstack COA,  Chemstack SDS, Supplier Invoice, supplier ISO )", "dueDateLogic": "-$intransitDaysSea-5", "description": "", "events": [], "category": "CUSTOM_CLEARANCE"}, {"name": "Checklist  review", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays-1", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Shipping Checklist", "key": "SHIPPING_CHECKLIST", "inputType": "file", "fileType": "application/pdf", "fileCount": 2, "mandatory": false}], "category": "CUSTOM_CLEARANCE"}, {"name": "Assessed order copy ", "dueDateLogic": "-$intransitDaysSea-5", "description": "", "events": [], "category": "CUSTOM_CLEARANCE"}, {"name": "LEO (shipping bill)", "dueDateLogic": "-$intransitDaysSea-4", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "LEO Bill", "key": "LEO_BILL", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 2, "mandatory": false}], "category": "CUSTOM_CLEARANCE"}, {"name": "BL draft submission to forwarder", "dueDateLogic": "-$intransitDaysSea-3", "description": "", "events": [], "category": "CUSTOM_CLEARANCE"}, {"name": "Stuffing", "dueDateLogic": "-$intransitDaysSea-$stuffingToSailDays", "description": "", "events": [{"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Proof Of Stuffing", "key": "STUFFING", "inputType": "file", "fileType": "image/jpeg", "fileCount": 4, "mandatory": false}], "category": "DISPATCH"}, {"name": "Sealing", "dueDateLogic": "-$intransitDaysSea-3", "description": "", "events": [], "category": "DISPATCH"}, {"name": "Gate In (container inside port, ready for loading)", "dueDateLogic": "-$intransitDaysSea-3", "description": "", "taskConfig": [{"label": "BL Number", "key": "BL_NUMBER", "inputType": "text", "mandatory": false}], "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_BL_NUMBER", "context": {}}], "category": "DISPATCH"}, {"name": "Container loading on vessel", "dueDateLogic": "-$intransitDaysSea-1", "description": "", "taskConfig": [{"label": "Vessel Number", "key": "VESSEL_NUMBER", "inputType": "text", "mandatory": false}, {"label": "Voyage Name", "key": "VOYAGE_NAME", "inputType": "text", "mandatory": false}], "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_BL_NUMBER", "context": {}}], "category": "DISPATCH"}, {"name": "V<PERSON>el <PERSON>", "dueDateLogic": "-$intransitDaysSea", "description": "Updates customer order to dispatched", "taskConfig": [{"label": "Tracking url", "key": "TRACKING_URL", "inputType": "text", "mandatory": false}, {"label": "Tracking Number", "key": "TRACKING_NUMBER", "inputType": "text", "mandatory": false}], "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_CUSTOMER_ORDER_STATUS", "context": {"status": "DISPATCHED"}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_SHIPMENT_DATE", "context": {}}], "category": "DISPATCH"}, {"name": "Documents to Bank ", "dueDateLogic": "-$intransitDaysSea+$documentDispatch", "description": "Mstack Invoice, Mstack COA, Mstack Packing list , BL copy (telex release/ seaway BL, Original BL courrier )", "events": [], "category": "DISPATCH"}, {"name": "Document dispatch", "dueDateLogic": "-$intransitDaysSea+$documentDispatch", "description": "Mstack Invoice, Mstack COA, Mstack Packing list , BL copy (telex release/ seaway BL, Original BL courrier )", "events": [], "category": "DISPATCH"}, {"name": "Delivered at Destination Port", "dueDateLogic": "0", "description": "Order reached final destination", "events": [], "category": "DELIVERY"}, {"name": "Delivered date at Customer location", "dueDateLogic": "0", "description": "Order reached final destination. Updates customer order to delivered", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_CUSTOMER_ORDER_STATUS", "context": {"status": "DELIVERED"}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_DELIVERY_DATE", "context": {}}], "category": "DELIVERY"}, {"name": "Payment By Buyer", "dueDateLogic": "0", "description": "Order reached final destination. Updates customer order to payment completed", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_CUSTOMER_ORDER_STATUS", "context": {"status": "PAYMENT_COMPLETED"}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Proof of Payment", "key": "CUSTOMER_PAYMENT", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 1, "mandatory": false}], "category": "DELIVERY"}, {"name": "Payment to forwarder", "dueDateLogic": "0", "description": "Order reached final destination. Updates customer order to payment completed", "events": [{"trigger": "TASK_COMPLETED", "name": "UPDATE_CUSTOMER_ORDER_STATUS", "context": {"status": "PAYMENT_COMPLETED"}}, {"trigger": "TASK_UPDATE", "name": "UPDATE_FILE", "context": {}}], "taskConfig": [{"label": "Proof of Payment", "key": "FORWARDER_PAYMENT", "inputType": "file", "fileType": "application/pdf,image/*", "fileCount": 2, "mandatory": false}], "category": "DELIVERY"}]}]