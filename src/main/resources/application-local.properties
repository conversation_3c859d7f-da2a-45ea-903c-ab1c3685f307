region=ap-south-1
spring.data.mongodb.uri=${MONGO_DB_URI}
redis.url=${REDIS_URI}
redis.emailQueue=dev-email
server.servlet.context-path=/keystone
logging.level.org.springframework.web=DEBUG
url.gatewayUrl=http://localhost:3001/v1
url.documentUrl=http://dev-api.mstack.co/document
email.customerEnquiryRecieverMailList=<EMAIL>,<EMAIL>
email.customerEnquirySenderMailId=<EMAIL>
email.customerEnquiryMailTemplate=customerEnquiryMail
useractivty.cron.expression=0 30 17 * * ?
enquiry.cron.expression=0 30 3 * * ?
invoiceScripts.cron.expression=0 30 8,17 * * ?