region=ap-south-1
spring.data.mongodb.uri=mongodb+srv://tech:<EMAIL>/keystone?retryWrites=true&w=majority
redis.url=http://localhost:6379
redis.emailQueue=dev-email
server.servlet.context-path=/keystone
logging.level.org.springframework.web=DEBUG
url.gatewayUrl=https://api-dev.mstack.co/v1
url.documentUrl=http://document-service.legacy.svc.cluster.local/document
email.customerEnquiryRecieverMailList=<EMAIL>,<EMAIL>
email.customerEnquirySenderMailId=<EMAIL>
email.customerEnquiryMailTemplate=customerEnquiryMail
useractivty.cron.expression=0 30 17 * * ?
enquiry.cron.expression=0 30 3 * * ?
invoiceScripts.cron.expression=0 30 8,17 * * ?