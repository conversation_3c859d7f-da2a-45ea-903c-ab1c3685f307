region=ap-south-1
spring.data.mongodb.uri=${MONGO_DB_URI}
redis.url=${REDIS_URI}
redis.emailQueue=prod-email
server.servlet.context-path=/keystone
logging.level.org.springframework.web=DEBUG
url.gatewayUrl=https://api-prod.mstack.co/v1
url.documentUrl=http://document-service.legacy.svc.cluster.local/document
email.customerEnquiryRecieverMailList=<EMAIL>,<EMAIL>,<EMAIL>
email.customerEnquirySenderMailId=<EMAIL>
email.customerEnquiryMailTemplate=customerEnquiryMail
useractivty.cron.expression=0 30 17 * * ?
enquiry.cron.expression=0 30 3 * * ?
invoiceScripts.cron.expression=0 30 8,17 * * ?
