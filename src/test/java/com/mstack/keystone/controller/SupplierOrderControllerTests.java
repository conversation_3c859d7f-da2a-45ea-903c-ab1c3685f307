package com.mstack.keystone.controller;

import com.mstack.keystone.controller.order.SupplierOrderController;
import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.service.order.interfaces.SupplierOrderService;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@WebMvcTest(SupplierOrderController.class)
class SupplierOrderControllerTests {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SupplierOrderService supplierOrderService;

    @Test
    public void testGetAllSupplierOrders() throws Exception {
        List<SupplierOrder> mockSupplierOrders = new ArrayList<>();
        // Add some mock supplier orders to the list

        Mockito.when(supplierOrderService.getAllNonDeletedSupplierOrders()).thenReturn(mockSupplierOrders);

        mockMvc
            .perform(MockMvcRequestBuilders.get("/supplier-orders").contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.length()").value(mockSupplierOrders.size()));
    }

    @Test
    public void testGetSupplierOrderById() throws Exception {
        SupplierOrder mockSupplierOrder = new SupplierOrder();
        // Set mock data for the supplier order

        Mockito
            .when(supplierOrderService.getSupplierOrderById(Mockito.anyString()))
            .thenReturn(Optional.of(mockSupplierOrder));

        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/supplier-orders/{id}", "mockId").contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.id").value(mockSupplierOrder.getId()));
    }

    @Test
    public void testCreateSupplierOrder() throws Exception {
        SupplierOrder mockSupplierOrder = new SupplierOrder();
        // Set mock data for the supplier order

        Mockito
            .when(supplierOrderService.createSupplierOrder(Mockito.any(SupplierOrder.class)))
            .thenReturn(mockSupplierOrder);

        String requestBody = "{ \"purchaseOrder\": \"Order123\" }"; // Replace with actual JSON payload

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/supplier-orders")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.id").value(mockSupplierOrder.getId()));
    }

    @Test
    public void testUpdateSupplierOrder() throws Exception {
        // Create a mock supplier order
        SupplierOrder mockSupplierOrder = new SupplierOrder();
        mockSupplierOrder.setId("mockId");
        mockSupplierOrder.setPurchaseOrderNumber("OriginalOrder");

        Mockito
            .when(supplierOrderService.updateSupplierOrder(Mockito.eq("mockId"), Mockito.any(SupplierOrder.class)))
            .thenAnswer(invocation -> {
                SupplierOrder updatedOrder = invocation.getArgument(1);
                updatedOrder.setId("mockId");
                return Optional.of(updatedOrder);
            });

        String requestBody = "{ \"purchaseOrder\": \"UpdatedOrder123\" }";

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .put("/supplier-orders/{id}", "mockId")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.purchaseOrder").value("UpdatedOrder123"));
    }

    @Test
    public void testDeleteSupplierOrder() throws Exception {
        mockMvc
            .perform(
                MockMvcRequestBuilders.delete("/supplier-orders/{id}", "mockId").contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(MockMvcResultMatchers.status().isNoContent());
    }
}
