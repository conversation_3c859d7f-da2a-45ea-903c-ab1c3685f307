package com.mstack.keystone.controller;

import com.mstack.keystone.controller.order.CustomerOrderController;
import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.service.order.interfaces.CustomerOrderService;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@WebMvcTest(CustomerOrderController.class)
public class CustomerOrderControllerTests {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CustomerOrderService customerOrderService;

    @Test
    public void testGetAllCustomerOrders() throws Exception {
        List<CustomerOrder> mockCustomerOrders = new ArrayList<>();
        // Add some mock customer orders to the list

        Mockito.when(customerOrderService.getAllNonDeletedCustomerOrders()).thenReturn(mockCustomerOrders);

        mockMvc
            .perform(MockMvcRequestBuilders.get("/customer-orders").contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.length()").value(mockCustomerOrders.size()));
    }

    @Test
    public void testGetCustomerOrderById() throws Exception {
        CustomerOrder mockCustomerOrder = new CustomerOrder();
        // Set mock data for the customer order

        Mockito.when(customerOrderService.getCustomerOrderById(Mockito.anyString())).thenReturn((mockCustomerOrder));

        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/customer-orders/{id}", "mockId").contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.id").value(mockCustomerOrder.getId()));
    }

    @Test
    public void testCreateCustomerOrder() throws Exception {
        CustomerOrder mockCustomerOrder = new CustomerOrder();
        // Set mock data for the customer order

        Mockito
            .when(customerOrderService.createCustomerOrder(Mockito.any(CustomerOrder.class)))
            .thenReturn(mockCustomerOrder);

        String requestBody = "{ \"purchaseOrder\": \"Order123\" }"; // Replace with actual JSON payload

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/customer-orders")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.id").value(mockCustomerOrder.getId()));
    }

    @Test
    public void testUpdateCustomerOrder() throws Exception {
        CustomerOrder mockCustomerOrder = new CustomerOrder();
        mockCustomerOrder.setId("mockId");
        mockCustomerOrder.setPurchaseOrderNumber("TEST 123");

        Mockito
            .when(customerOrderService.updateCustomerOrder(Mockito.anyString(), Mockito.any(CustomerOrder.class)))
            .thenAnswer(invocation -> {
                CustomerOrder updatedOrder = invocation.getArgument(1);
                updatedOrder.setId("mockId");
                return Optional.of(updatedOrder);
            });

        String requestBody = "{ \"purchaseOrder\": \"UpdatedOrder123\" }";

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .put("/customer-orders/{id}", "mockId")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.purchaseOrder").value("UpdatedOrder123"));
    }

    @Test
    public void testDeleteCustomerOrder() throws Exception {
        mockMvc
            .perform(
                MockMvcRequestBuilders.delete("/customer-orders/{id}", "mockId").contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(MockMvcResultMatchers.status().isNoContent());
    }
}
