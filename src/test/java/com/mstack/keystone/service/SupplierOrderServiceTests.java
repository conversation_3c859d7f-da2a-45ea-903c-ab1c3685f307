package com.mstack.keystone.service;

import static org.mockito.Mockito.verify;

import com.mstack.keystone.model.repository.order.SupplierOrder;
import com.mstack.keystone.repository.order.SupplierOrderRepository;
import com.mstack.keystone.service.order.SupplierOrderServiceImpl;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class SupplierOrderServiceTests {

    @Mock
    private SupplierOrderRepository supplierOrderRepository;

    @InjectMocks
    private SupplierOrderServiceImpl supplierOrderService;

    @Test
    public void testCreateSupplierOrder() {
        SupplierOrder mockSupplierOrder = new SupplierOrder();
        mockSupplierOrder.setPurchaseOrderNumber("Order123");

        Mockito.when(supplierOrderRepository.save(Mockito.any(SupplierOrder.class))).thenReturn(mockSupplierOrder);

        SupplierOrder createdSupplierOrder = supplierOrderService.createSupplierOrder(mockSupplierOrder);

        Assertions.assertEquals("Order123", createdSupplierOrder.getPurchaseOrderNumber());
    }

    @Test
    public void testUpdateSupplierOrder() {
        SupplierOrder mockSupplierOrder = new SupplierOrder();
        mockSupplierOrder.setId("mockId");
        mockSupplierOrder.setPurchaseOrderNumber("OriginalOrder");

        Mockito.when(supplierOrderRepository.existsById("mockId")).thenReturn(true);
        Mockito
            .when(supplierOrderRepository.save(Mockito.any(SupplierOrder.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));

        SupplierOrder updatedSupplierOrder = new SupplierOrder();
        updatedSupplierOrder.setId("mockId");
        updatedSupplierOrder.setPurchaseOrderNumber("UpdatedOrder123");

        SupplierOrder result = supplierOrderService.updateSupplierOrder("mockId", updatedSupplierOrder);
        //        Assertions.assertTrue(result);
        //        Assertions.assertEquals("UpdatedOrder123", result.get().getPurchaseOrder());
    }

    @Test
    public void testUpdateSupplierOrderNotFound() {
        Mockito.when(supplierOrderRepository.existsById("nonExistentId")).thenReturn(false);

        SupplierOrder updatedSupplierOrder = new SupplierOrder();
        updatedSupplierOrder.setPurchaseOrderNumber("UpdatedOrder123");

        SupplierOrder result = supplierOrderService.updateSupplierOrder("nonExistentId", updatedSupplierOrder);
        //TODO
        //        Assertions.assertTrue(result,null);
    }

    @Test
    public void testDeleteSupplierOrder() {
        SupplierOrder mockSupplierOrder = new SupplierOrder();
        mockSupplierOrder.setId("mockId");

        Mockito.when(supplierOrderRepository.findById("mockId")).thenReturn(Optional.of(mockSupplierOrder));

        supplierOrderService.deleteSupplierOrder("mockId");

        ArgumentCaptor<SupplierOrder> captor = ArgumentCaptor.forClass(SupplierOrder.class);
        verify(supplierOrderRepository).save(captor.capture());

        SupplierOrder deletedSupplierOrder = captor.getValue();
        Assertions.assertTrue(deletedSupplierOrder.isDeleted());
    }

    @Test
    public void testDeleteSupplierOrderNotFound() {
        Mockito.when(supplierOrderRepository.findById("nonExistentId")).thenReturn(Optional.empty());

        supplierOrderService.deleteSupplierOrder("nonExistentId");

        verify(supplierOrderRepository, Mockito.times(0)).save(Mockito.any());
    }
}
