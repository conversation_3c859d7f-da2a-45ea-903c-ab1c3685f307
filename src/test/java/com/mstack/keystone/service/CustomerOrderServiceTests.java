package com.mstack.keystone.service;

import static org.mockito.Mockito.verify;

import com.mstack.keystone.model.repository.order.CustomerOrder;
import com.mstack.keystone.repository.order.CustomerOrderRepository;
import com.mstack.keystone.service.order.CustomerOrderServiceImpl;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class CustomerOrderServiceTests {

    @Mock
    private CustomerOrderRepository customerOrderRepository;

    @InjectMocks
    private CustomerOrderServiceImpl customerOrderService;

    @Test
    public void testGetAllNonDeletedBuyerOrders() {
        List<CustomerOrder> mockBuyerOrders = new ArrayList<>();
        // Add some mock customer orders to the list

        Mockito.when(customerOrderRepository.findByDeletedFalse()).thenReturn(mockBuyerOrders);

        List<CustomerOrder> result = customerOrderService.getAllNonDeletedCustomerOrders();

        Assertions.assertEquals(mockBuyerOrders, result);
    }

    @Test
    public void testGetBuyerOrderById() {
        CustomerOrder mockBuyerOrder = new CustomerOrder();
        // Set mock data for the customer order

        Mockito.when(customerOrderRepository.findById("mockId")).thenReturn(Optional.of(mockBuyerOrder));

        CustomerOrder result = customerOrderService.getCustomerOrderById("mockId");
        //        Assertions.assertTrue(result.isPresent());
        //        Assertions.assertEquals(mockBuyerOrder, result.get());
    }

    @Test
    public void testCreateBuyerOrder() {
        CustomerOrder mockBuyerOrder = new CustomerOrder();
        // Set mock data for the customer order

        Mockito.when(customerOrderRepository.save(Mockito.any(CustomerOrder.class))).thenReturn(mockBuyerOrder);

        CustomerOrder createdBuyerOrder = customerOrderService.createCustomerOrder(mockBuyerOrder);

        Assertions.assertEquals(mockBuyerOrder, createdBuyerOrder);
    }

    @Test
    public void testUpdateBuyerOrder() {
        CustomerOrder mockBuyerOrder = new CustomerOrder();
        mockBuyerOrder.setId("mockId");
        mockBuyerOrder.setPurchaseOrderNumber("OriginalOrder");

        Mockito.when(customerOrderRepository.existsById("mockId")).thenReturn(true);
        Mockito
            .when(customerOrderRepository.save(Mockito.any(CustomerOrder.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));

        CustomerOrder updatedBuyerOrder = new CustomerOrder();
        updatedBuyerOrder.setId("mockId");
        updatedBuyerOrder.setPurchaseOrderNumber("UpdatedOrder123");

        CustomerOrder result = customerOrderService.updateCustomerOrder("mockId", updatedBuyerOrder);
        //        Assertions.assertTrue(result.isPresent());
        //        Assertions.assertEquals("UpdatedOrder123", result.get().getPurchaseOrder());
    }

    @Test
    public void testUpdateBuyerOrderNotFound() {
        Mockito.when(customerOrderRepository.existsById("nonExistentId")).thenReturn(false);

        CustomerOrder updatedBuyerOrder = new CustomerOrder();
        updatedBuyerOrder.setPurchaseOrderNumber("UpdatedOrder123");

        CustomerOrder result = customerOrderService.updateCustomerOrder("nonExistentId", updatedBuyerOrder);
        //        Assertions.assertFalse(result.isPresent());
    }

    @Test
    public void testDeleteBuyerOrder() {
        CustomerOrder mockBuyerOrder = new CustomerOrder();
        mockBuyerOrder.setId("mockId");

        Mockito.when(customerOrderRepository.findById("mockId")).thenReturn(Optional.of(mockBuyerOrder));

        customerOrderService.deleteCustomerOrder("mockId");

        ArgumentCaptor<CustomerOrder> captor = ArgumentCaptor.forClass(CustomerOrder.class);
        Mockito.verify(customerOrderRepository).save(captor.capture());

        CustomerOrder deletedBuyerOrder = captor.getValue();
        Assertions.assertTrue(deletedBuyerOrder.isDeleted());
    }

    @Test
    public void testDeleteBuyerOrderNotFound() {
        Mockito.when(customerOrderRepository.findById("nonExistentId")).thenReturn(Optional.empty());

        // Call the delete method, it should complete successfully without throwing an exception
        customerOrderService.deleteCustomerOrder("nonExistentId");

        // You can add additional assertions here if needed
        verify(customerOrderRepository, Mockito.times(0)).save(Mockito.any());
    }
}
