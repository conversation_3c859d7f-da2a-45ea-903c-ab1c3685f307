FROM --platform=linux/arm64 maven:3.8.6-openjdk-18-slim as build
WORKDIR /usr/src/code


COPY . .
RUN mkdir -p /usr/share/maven/ref/repository
RUN mvn -B -f ./pom.xml -Dmaven.repo.local=/usr/share/maven/ref/repository dependency:go-offline --fail-never

EXPOSE 8080
RUN mvn -Dmaven.repo.local=/usr/share/maven/ref/repository -Dmaven.test.skip=true package
ENTRYPOINT ["java","-jar","/usr/src/code/target/keystone-0.0.1-SNAPSHOT.jar" ]