# https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven
name: Java Check Lint

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
      - name: Check lint with Maven
        run: mvn validate